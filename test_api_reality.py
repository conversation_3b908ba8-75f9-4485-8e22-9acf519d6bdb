#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实API调用情况
验证当前抓取方法是否有效
"""

import requests
import json
import time

def test_douyin_api():
    """测试抖音API"""
    print("🎵 测试抖音API")
    print("=" * 50)
    
    # 当前系统使用的API
    api_url = "https://www.douyin.com/aweme/v1/web/general/search/"
    
    params = {
        'keyword': '酒店建设 企业',
        'search_source': 'tab_search',
        'type': 1,
        'count': 20,
        'cursor': 0
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.douyin.com/',
        'Accept': 'application/json, text/plain, */*'
    }
    
    try:
        print(f"🔗 请求URL: {api_url}")
        print(f"📝 请求参数: {params}")
        
        response = requests.get(api_url, params=params, headers=headers, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        print(f"📝 响应内容长度: {len(response.text)}")
        print(f"🔍 响应内容预览: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON解析成功")
                print(f"📊 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            except:
                print(f"❌ 不是有效的JSON响应")
                print(f"🔍 实际内容: {response.text[:200]}")
        else:
            print(f"❌ API调用失败")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_kuaishou_api():
    """测试快手API"""
    print("\n🎬 测试快手API")
    print("=" * 50)
    
    api_url = "https://www.kuaishou.com/graphql"
    
    query = {
        "operationName": "visionSearchPhoto",
        "variables": {
            "keyword": "酒店建设 企业",
            "pcursor": "",
            "searchSessionId": "",
            "page": "search"
        },
        "query": "fragment photoContent on PhotoEntity {...}"
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.kuaishou.com/',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    try:
        print(f"🔗 请求URL: {api_url}")
        print(f"📝 GraphQL查询: {json.dumps(query, indent=2)}")
        
        response = requests.post(api_url, json=query, headers=headers, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        print(f"📝 响应内容长度: {len(response.text)}")
        print(f"🔍 响应内容预览: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON解析成功")
                print(f"📊 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            except:
                print(f"❌ 不是有效的JSON响应")
        else:
            print(f"❌ API调用失败")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_qichacha_api():
    """测试企查查API"""
    print("\n🏢 测试企查查API")
    print("=" * 50)
    
    search_url = "https://www.qcc.com/web/search"
    
    params = {
        'key': '酒店建设',
        'index': '1',
        'sortField': '0',
        'isSortAsc': 'false'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.qcc.com/',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
    }
    
    try:
        print(f"🔗 请求URL: {search_url}")
        print(f"📝 请求参数: {params}")
        
        response = requests.get(search_url, params=params, headers=headers, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        print(f"📝 响应内容长度: {len(response.text)}")
        
        # 检查是否被重定向到登录页面
        if 'login' in response.url.lower() or '登录' in response.text:
            print(f"⚠️ 被重定向到登录页面")
        
        # 检查是否有验证码
        if 'captcha' in response.text.lower() or '验证码' in response.text:
            print(f"⚠️ 需要验证码验证")
        
        # 检查是否有企业数据
        if '公司' in response.text and '有限' in response.text:
            print(f"✅ 可能包含企业数据")
        else:
            print(f"❌ 未发现企业数据")
            
        print(f"🔍 响应内容预览: {response.text[:300]}...")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_tianyancha_api():
    """测试天眼查API"""
    print("\n👁️ 测试天眼查API")
    print("=" * 50)
    
    api_url = "https://www.tianyancha.com/services/v3/t/search/sug"
    
    params = {
        'key': '酒店建设',
        'sessionNo': str(int(time.time() * 1000)),
        'checkFrom': 'searchBox'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.tianyancha.com/',
        'Accept': 'application/json'
    }
    
    try:
        print(f"🔗 请求URL: {api_url}")
        print(f"📝 请求参数: {params}")
        
        response = requests.get(api_url, params=params, headers=headers, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        print(f"📝 响应内容长度: {len(response.text)}")
        print(f"🔍 响应内容预览: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON解析成功")
                print(f"📊 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                
                if 'data' in data and data['data']:
                    print(f"🎯 找到 {len(data['data'])} 条数据")
                else:
                    print(f"❌ 没有找到有效数据")
            except:
                print(f"❌ 不是有效的JSON响应")
        else:
            print(f"❌ API调用失败")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_baidu_search():
    """测试百度搜索"""
    print("\n🔍 测试百度搜索")
    print("=" * 50)
    
    search_url = "https://www.baidu.com/s"
    
    params = {
        'wd': '北京 酒店建设 公司',
        'pn': 0
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
    }
    
    try:
        print(f"🔗 请求URL: {search_url}")
        print(f"📝 请求参数: {params}")
        
        response = requests.get(search_url, params=params, headers=headers, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📝 响应内容长度: {len(response.text)}")
        
        # 检查是否被反爬虫拦截
        if '百度安全验证' in response.text or 'captcha' in response.text.lower():
            print(f"⚠️ 触发百度反爬虫验证")
        
        # 检查是否有搜索结果
        if 'result' in response.text and '公司' in response.text:
            print(f"✅ 可能包含搜索结果")
        else:
            print(f"❌ 未发现有效搜索结果")
            
        print(f"🔍 响应内容预览: {response.text[:300]}...")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def analyze_problems():
    """分析问题"""
    print("\n🔬 问题分析")
    print("=" * 50)
    
    print("📋 当前抓取方法的主要问题:")
    print()
    print("1. 🎵 抖音平台:")
    print("   ❌ API地址可能不正确或已失效")
    print("   ❌ 缺少必要的认证参数（如签名、token）")
    print("   ❌ 抖音有强大的反爬虫机制")
    print("   ❌ 需要登录或特殊权限")
    print()
    print("2. 🎬 快手平台:")
    print("   ❌ GraphQL查询语句不完整")
    print("   ❌ 缺少必要的session参数")
    print("   ❌ 快手API需要特殊的认证机制")
    print()
    print("3. 🏢 企查查平台:")
    print("   ❌ 需要用户登录才能查看详细信息")
    print("   ❌ 有验证码机制防止自动化访问")
    print("   ❌ 对访问频率有严格限制")
    print()
    print("4. 👁️ 天眼查平台:")
    print("   ❌ 需要复杂的session和签名验证")
    print("   ❌ 详细信息需要付费会员权限")
    print("   ❌ 有完善的反爬虫检测系统")
    print()
    print("5. 🔍 搜索引擎:")
    print("   ❌ 百度等搜索引擎有强大的反爬虫系统")
    print("   ❌ 频繁访问会触发验证码")
    print("   ❌ 可能导致IP被封禁")
    print()
    print("💡 根本原因:")
    print("   • 这些平台都不是为程序化访问设计的")
    print("   • 它们有商业利益需要保护数据")
    print("   • 反爬虫技术越来越先进")
    print("   • 需要官方API或付费服务才能获取数据")

def main():
    """主测试函数"""
    print("🚀 API真实性测试")
    print("=" * 80)
    print("测试当前系统使用的各个平台API是否真的有效")
    print("=" * 80)
    
    # 测试各个平台API
    test_douyin_api()
    test_kuaishou_api()
    test_qichacha_api()
    test_tianyancha_api()
    test_baidu_search()
    
    # 分析问题
    analyze_problems()
    
    print("\n" + "=" * 50)
    print("🎯 结论")
    print("=" * 50)
    print("通过实际测试可以看出，当前的API调用方法")
    print("很可能无法获取到真实数据，这就是为什么")
    print("系统只能显示演示数据的根本原因！")
    print()
    print("💡 建议:")
    print("1. 使用我们开发的真实数据解决方案")
    print("2. 考虑购买官方API服务")
    print("3. 使用政府公开数据源")
    print("4. 采用合规的数据获取方式")

if __name__ == "__main__":
    main()
