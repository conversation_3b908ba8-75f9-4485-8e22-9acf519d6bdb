const BrowserManager = require('../../config/browserConfig');
const fs = require('fs').promises;
const path = require('path');

class KuaishouCrawler {
    constructor() {
        this.browserManager = new BrowserManager();
        this.baseUrl = 'https://www.kuaishou.com';
    }

    async extractUserProfile(userUrl) {
        const browser = await this.browserManager.createBrowser();
        const page = await this.browserManager.createPage(browser);

        try {
            console.log(`🔍 正在访问快手用户页面: ${userUrl}`);
            
            await page.goto(userUrl, { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });

            // 等待页面加载
            await page.waitForTimeout(5000);

            // 提取用户信息
            const userInfo = await page.evaluate(() => {
                const nameEl = document.querySelector('.profile-user-name');
                const descEl = document.querySelector('.profile-user-desc');
                const statsEls = document.querySelectorAll('.profile-user-stats .count');

                return {
                    username: nameEl?.textContent?.trim() || '',
                    description: descEl?.textContent?.trim() || '',
                    followers: statsEls[1]?.textContent?.trim() || '0',
                    following: statsEls[2]?.textContent?.trim() || '0',
                    works: statsEls[0]?.textContent?.trim() || '0'
                };
            });

            console.log(`✅ 快手用户信息: ${userInfo.username}`);

            // 滚动加载视频
            await this.loadMoreVideos(page);

            // 提取视频数据
            const videos = await this.extractVideos(page);

            console.log(`✅ 提取到 ${videos.length} 个快手视频`);

            return {
                userInfo,
                videos,
                crawlTime: new Date().toISOString(),
                platform: 'kuaishou'
            };

        } catch (error) {
            console.error('❌ 快手数据提取失败:', error.message);
            throw error;
        } finally {
            await this.browserManager.closeBrowser(browser);
        }
    }

    async loadMoreVideos(page, maxScrolls = 6) {
        console.log('📜 开始滚动加载快手视频...');
        
        for (let i = 0; i < maxScrolls; i++) {
            // 滚动到页面底部
            await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            
            await page.waitForTimeout(3000);
            
            // 检查视频数量变化
            const videoCount = await page.$$eval('.video-card-wrap', els => els.length);
            console.log(`第 ${i + 1} 次滚动，当前视频数: ${videoCount}`);
            
            // 如果没有新视频加载，提前结束
            if (i > 0 && videoCount === 0) {
                break;
            }
        }
    }

    async extractVideos(page) {
        return await page.evaluate(() => {
            const videoCards = document.querySelectorAll('.video-card-wrap');
            const videos = [];

            videoCards.forEach((card, index) => {
                try {
                    const linkEl = card.querySelector('a');
                    const imgEl = card.querySelector('img');
                    const titleEl = card.querySelector('.caption');
                    const timeEl = card.querySelector('.time');
                    const playCountEl = card.querySelector('.play-count');

                    const video = {
                        id: index + 1,
                        title: titleEl?.textContent?.trim() || `快手视频${index + 1}`,
                        url: linkEl?.href || '',
                        cover: imgEl?.src || '',
                        duration: timeEl?.textContent?.trim() || '',
                        playCount: playCountEl?.textContent?.trim() || '',
                        extractTime: new Date().toISOString()
                    };

                    if (video.url && video.url.includes('kuaishou.com')) {
                        videos.push(video);
                    }
                } catch (error) {
                    console.warn(`快手视频 ${index + 1} 提取失败:`, error.message);
                }
            });

            return videos;
        });
    }

    async interceptKuaishouApi(page) {
        const apiResponses = [];
        
        page.on('response', async (response) => {
            const url = response.url();
            
            // 拦截快手API
            if (url.includes('/rest/') || url.includes('/graphql')) {
                try {
                    const contentType = response.headers()['content-type'];
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        apiResponses.push({
                            url,
                            data,
                            timestamp: Date.now()
                        });
                    }
                } catch (error) {
                    // 忽略解析错误
                }
            }
        });

        return apiResponses;
    }

    async saveData(data, filename) {
        const dataDir = path.join(__dirname, '../../../data/kuaishou');
        await fs.mkdir(dataDir, { recursive: true });
        
        const filepath = path.join(dataDir, `${filename}.json`);
        await fs.writeFile(filepath, JSON.stringify(data, null, 2), 'utf8');
        
        console.log(`💾 快手数据已保存到: ${filepath}`);
        return filepath;
    }

    async crawlUser(userUrl, options = {}) {
        try {
            const data = await this.extractUserProfile(userUrl);
            
            // 生成文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `kuaishou_user_${timestamp}`;
            
            // 保存数据
            await this.saveData(data, filename);
            
            return {
                success: true,
                data,
                message: `成功爬取快手用户数据，共 ${data.videos.length} 个视频`
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: '快手数据爬取失败'
            };
        }
    }

    async searchUsers(keyword, limit = 10) {
        const browser = await this.browserManager.createBrowser();
        const page = await this.browserManager.createPage(browser);

        try {
            const searchUrl = `${this.baseUrl}/search/user?keyword=${encodeURIComponent(keyword)}`;
            await page.goto(searchUrl, { waitUntil: 'networkidle' });
            
            await page.waitForTimeout(3000);

            const users = await page.evaluate((limit) => {
                const userCards = document.querySelectorAll('.user-card');
                const results = [];

                for (let i = 0; i < Math.min(userCards.length, limit); i++) {
                    const card = userCards[i];
                    const nameEl = card.querySelector('.user-name');
                    const linkEl = card.querySelector('a');
                    const avatarEl = card.querySelector('.user-avatar img');

                    if (nameEl && linkEl) {
                        results.push({
                            name: nameEl.textContent.trim(),
                            url: linkEl.href,
                            avatar: avatarEl?.src || ''
                        });
                    }
                }

                return results;
            }, limit);

            return users;

        } catch (error) {
            console.error('❌ 快手用户搜索失败:', error.message);
            return [];
        } finally {
            await this.browserManager.closeBrowser(browser);
        }
    }
}

module.exports = KuaishouCrawler;
