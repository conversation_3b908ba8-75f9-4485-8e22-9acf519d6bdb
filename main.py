#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户信息收集系统 - 主程序入口
作者: 爬虫专家
功能: 根据行业自动收集客户信息并可视化展示
"""

import streamlit as st
import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.crawler_engine import CrawlerEngine
from src.data_processor import DataProcessor
from src.visualizer import DataVisualizer
from src.database_manager import DatabaseManager
from src.region_manager import RegionManager
from src.utils.logger import setup_logger
from src.real_bidding_crawler import RealBiddingCrawler
from intelligent_analysis_framework import IntelligentAnalyzer
from real_crawler_integration import (
    real_crawler,
    test_real_crawler_system,
    crawl_real_data,
    get_real_crawler_status
)

# 导入智能行业分析器
try:
    from src.intelligent_industry_analyzer import IntelligentIndustryAnalyzer
    industry_analyzer = IntelligentIndustryAnalyzer()
    INDUSTRY_ANALYZER_AVAILABLE = True
    print("✅ 智能行业分析器加载成功")
except ImportError as e:
    print(f"⚠️ 智能行业分析器加载失败: {e}")
    industry_analyzer = None
    INDUSTRY_ANALYZER_AVAILABLE = False

def main():
    """主程序入口"""
    # 设置页面配置
    st.set_page_config(
        page_title="客户信息收集系统",
        page_icon="🕷️",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 设置日志
    logger = setup_logger()
    
    # 页面标题
    st.title("🕷️ 智能客户信息收集系统")

    # 检查真实爬虫系统状态
    real_crawler_status = get_real_crawler_status()

    if real_crawler_status['available']:
        st.success("✅ 真实数据爬取系统已启用 - 可获取抖音、快手、企查查等平台的真实数据")

        # 显示数据摘要
        data_summary = real_crawler_status.get('data_summary', {})
        if data_summary.get('total_files', 0) > 0:
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("总数据文件", data_summary.get('total_files', 0))
            with col2:
                platforms = data_summary.get('platforms', {})
                active_platforms = len([p for p in platforms.values() if p > 0])
                st.metric("活跃平台", f"{active_platforms}/3")
            with col3:
                latest = data_summary.get('latest_crawl')
                if latest:
                    st.metric("最新爬取", latest.strftime("%m-%d %H:%M"))
    else:
        st.warning("⚠️ 真实爬虫系统不可用 - 当前使用模拟数据")
        st.info(f"状态: {real_crawler_status.get('message', '未知错误')}")

        # 检查传统真实爬虫模式
        try:
            import bs4
            import fake_useragent
            st.info("💡 检测到传统真实爬虫依赖，可使用基础真实数据功能")
            real_mode = True
        except ImportError:
            real_mode = False
            if st.button("🔧 安装真实爬虫依赖"):
                st.info("请在命令行运行: python install_real_crawler.py")

    st.markdown("---")
    
    # 初始化组件
    region_manager = RegionManager()
    intelligent_analyzer = IntelligentAnalyzer()

    # 侧边栏配置
    with st.sidebar:
        st.header("⚙️ 智能分析配置")

        # 行业选择
        industry = st.text_input(
            "目标行业",
            placeholder="例如: 酒店建设、房地产开发、建筑工程",
            help="输入您要收集客户信息的行业关键词"
        )

        # 智能行业识别
        if industry and INDUSTRY_ANALYZER_AVAILABLE:
            with st.expander("🧠 智能行业分析", expanded=False):
                analysis_result = industry_analyzer.analyze_industry(industry)

                if analysis_result['success']:
                    st.success(f"✅ 识别行业: **{analysis_result['industry']}**")
                    st.info(f"🎯 置信度: {analysis_result['confidence']:.1f}%")

                    # 显示匹配的关键词
                    if analysis_result.get('matched_keywords'):
                        st.write("🔍 匹配关键词:")
                        keywords_text = ", ".join(analysis_result['matched_keywords'][:5])
                        st.caption(keywords_text)

                    # 显示建议
                    if analysis_result.get('suggestions'):
                        st.write("💡 搜索建议:")
                        for suggestion in analysis_result['suggestions'][:3]:
                            st.caption(f"• {suggestion}")

                    # 显示备选行业
                    if analysis_result.get('alternative_industries'):
                        st.write("🔄 备选行业:")
                        alt_industries = ", ".join(analysis_result['alternative_industries'])
                        st.caption(alt_industries)

                else:
                    st.warning(f"⚠️ {analysis_result['message']}")
                    if analysis_result.get('suggestions'):
                        st.write("💡 建议:")
                        for suggestion in analysis_result['suggestions'][:3]:
                            st.caption(f"• {suggestion}")

        # 智能分析展示
        if industry:
            with st.expander("🧠 智能行业分析", expanded=False):
                try:
                    analysis_result = intelligent_analyzer.analyze_industry_input(industry)

                    # 显示行业识别结果
                    industry_info = analysis_result['industry_info']
                    st.write(f"**识别行业**: {industry_info['primary_industry']}")
                    st.write(f"**业务模式**: {industry_info['business_model']}")

                    # 显示关键角色
                    key_players = analysis_result['key_players']
                    if key_players:
                        st.write("**关键参与者**:")
                        for role_type, players in list(key_players.items())[:2]:
                            st.write(f"- {role_type}: {', '.join(players[:3])}")

                    # 显示数据源
                    data_sources = analysis_result['data_sources']
                    st.write(f"**相关数据源**: {len(data_sources)} 个专业网站")

                    # 显示搜索策略
                    search_strategy = analysis_result['search_strategy']
                    st.write(f"**主要关键词**: {', '.join(search_strategy.primary_keywords[:3])}")

                except Exception as e:
                    st.warning(f"智能分析暂时不可用: {str(e)}")

        st.markdown("---")

        # 真实爬虫系统控制
        if real_crawler_status['available']:
            st.subheader("🚀 真实爬虫系统")

            # 平台测试
            if st.button("🧪 测试平台连通性"):
                with st.spinner("正在测试平台连通性..."):
                    test_result = test_real_crawler_system()

                    if test_result['success']:
                        st.success(test_result['message'])

                        # 显示详细结果
                        platforms = test_result.get('platforms', {})
                        for platform, status in platforms.items():
                            icon = "✅" if status else "❌"
                            st.write(f"{icon} {platform.upper()}")
                    else:
                        st.error(test_result['message'])

            # 爬虫模式选择
            crawl_mode = st.radio(
                "爬取模式",
                ["智能行业爬取", "单平台爬取", "企业信息查询"],
                help="选择不同的爬取模式获取真实数据"
            )

            if crawl_mode == "单平台爬取":
                platform = st.selectbox("选择平台", ["抖音", "快手", "企查查"])
                target_url = st.text_input("目标URL或关键词")

                if st.button("开始爬取") and target_url:
                    with st.spinner(f"正在爬取{platform}数据..."):
                        platform_map = {"抖音": "douyin", "快手": "kuaishou", "企查查": "company"}
                        result = crawl_real_data(platform_map[platform], target_url)

                        if result['success']:
                            st.success("爬取成功！")
                            st.json(result['data'])
                        else:
                            st.error(f"爬取失败: {result['message']}")

            elif crawl_mode == "企业信息查询":
                company_name = st.text_input("企业名称")

                if st.button("查询企业") and company_name:
                    with st.spinner("正在查询企业信息..."):
                        result = crawl_real_data("company", company_name)

                        if result['success']:
                            st.success("查询成功！")
                            st.json(result['data'])
                        else:
                            st.error(f"查询失败: {result['message']}")

        st.markdown("---")

        # 区域选择
        st.subheader("🌍 区域选择")

        # 省份选择
        provinces = ["全国"] + region_manager.get_all_provinces()
        selected_province = st.selectbox(
            "选择省份",
            provinces,
            help="选择目标省份，选择'全国'将搜索所有地区"
        )

        # 城市选择
        if selected_province != "全国":
            cities = ["全省"] + region_manager.get_cities_by_province(selected_province)
            selected_city = st.selectbox(
                "选择城市",
                cities,
                help="选择目标城市，选择'全省'将搜索该省所有城市"
            )
        else:
            selected_city = None
            st.info("已选择全国搜索")

        # 处理选择结果
        province = None if selected_province == "全国" else selected_province
        city = None if not selected_city or selected_city == "全省" else selected_city

        # 显示当前选择
        region_display = region_manager.get_region_display_name(province, city)
        st.success(f"当前搜索区域: {region_display}")

        # 搜索深度
        search_depth = st.slider("搜索深度", 1, 20, 10)

        # 智能数据源配置
        st.subheader("📡 智能数据源配置")

        # 专业数据源（推荐）
        st.write("**🎯 专业数据源 (推荐)**")
        use_real_bidding = st.checkbox("🏛️ 真实招投标数据", value=True,
                                      help="中国政府采购网、公共资源交易平台等官方招投标信息")
        use_professional_sources = st.checkbox("专业行业网站", value=True,
                                              help="招标网站、行业协会、专业媒体等")
        use_enhanced_search = st.checkbox("增强搜索引擎", value=True,
                                        help="通过搜索引擎查找专业网站内容")
        use_enhanced_real = st.checkbox("增强真实数据爬取", value=True,
                                       help="多源真实数据爬取，获取更多真实企业信息")

        # 通用数据源
        st.write("**🌐 通用数据源**")
        use_baidu = st.checkbox("百度搜索", value=False,
                               help="可能遇到反爬限制")
        use_bing = st.checkbox("必应搜索", value=False,
                              help="国际搜索引擎")
        use_business_dirs = st.checkbox("企业目录", value=True,
                                       help="企查查、天眼查等企业信息平台")

        # 新增平台数据源
        st.write("**📱 社交媒体平台**")
        use_social_media = st.checkbox("社交媒体平台", value=False,
                                      help="抖音、快手等短视频平台的企业账号")

        if use_social_media:
            st.write("选择社交媒体平台:")
            col_social1, col_social2 = st.columns(2)
            with col_social1:
                use_douyin = st.checkbox("抖音", value=True, help="抖音企业账号")
            with col_social2:
                use_kuaishou = st.checkbox("快手", value=True, help="快手企业账号")
        else:
            use_douyin = False
            use_kuaishou = False

        st.write("**🏢 企业信息平台**")
        use_enterprise_info = st.checkbox("企业信息平台", value=False,
                                         help="企查查、天眼查、钉钉企典等专业企业信息平台")

        if use_enterprise_info:
            st.write("选择企业信息平台:")
            col_ent1, col_ent2, col_ent3 = st.columns(3)
            with col_ent1:
                use_qichacha = st.checkbox("企查查", value=True, help="企查查企业信息")
            with col_ent2:
                use_tianyancha = st.checkbox("天眼查", value=True, help="天眼查企业信息")
            with col_ent3:
                use_dingding = st.checkbox("钉钉企典", value=True, help="钉钉企典企业信息")
        else:
            use_qichacha = False
            use_tianyancha = False
            use_dingding = False

        # 开始收集按钮
        start_crawling = st.button("🚀 开始收集", type="primary")
    
    # 主界面布局
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📊 智能数据收集与分析")

        # 显示智能分析结果
        if industry and not start_crawling:
            st.subheader("🧠 智能行业分析结果")
            try:
                analysis_result = intelligent_analyzer.analyze_industry_input(industry)

                # 创建分析结果展示
                col_a, col_b = st.columns(2)

                with col_a:
                    st.write("**🏭 行业特征分析**")
                    industry_info = analysis_result['industry_info']
                    st.info(f"""
                    **主要行业**: {industry_info['primary_industry']}
                    **业务模式**: {industry_info['business_model']}
                    **行业特征**: {', '.join(industry_info.get('industry_characteristics', []))}
                    """)

                with col_b:
                    st.write("**🎯 搜索策略预览**")
                    search_strategy = analysis_result['search_strategy']
                    st.success(f"""
                    **主要关键词**: {', '.join(search_strategy.primary_keywords[:3])}
                    **目标网站**: {len(search_strategy.target_websites)} 个专业网站
                    **搜索深度**: {search_strategy.search_depth} 层
                    """)

                # 产业链分析
                value_chain = analysis_result['value_chain']
                if value_chain:
                    st.write("**🔗 产业链分析**")
                    chain_cols = st.columns(len(value_chain))
                    for i, (stage, companies) in enumerate(value_chain.items()):
                        with chain_cols[i]:
                            st.write(f"**{stage}**")
                            for company in companies[:3]:
                                st.write(f"• {company}")

                st.info("💡 点击'开始收集'按钮，系统将根据以上分析结果进行智能数据采集")

            except Exception as e:
                st.warning(f"智能分析功能暂时不可用: {str(e)}")

        if start_crawling and industry:
            # 显示进度条
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            try:
                # 智能分析阶段
                status_text.text("🧠 正在进行智能行业分析...")
                analysis_result = intelligent_analyzer.analyze_industry_input(industry)
                progress_bar.progress(5)

                # 显示分析结果
                st.info(f"""
                🎯 **智能分析完成**
                - 识别行业: {analysis_result['industry_info']['primary_industry']}
                - 发现数据源: {len(analysis_result['data_sources'])} 个专业网站
                - 生成关键词: {len(analysis_result['search_strategy'].primary_keywords)} 个主要关键词
                """)

                # 初始化组件
                status_text.text("正在初始化智能爬虫引擎...")
                crawler = CrawlerEngine()
                db_manager = DatabaseManager()
                data_processor = DataProcessor()
                visualizer = DataVisualizer()

                progress_bar.progress(15)
                
                # 配置爬虫参数
                crawler_config = {
                    'industry': industry,
                    'province': province,
                    'city': city,
                    'search_depth': search_depth,
                    'use_baidu': use_baidu,
                    'use_bing': use_bing,
                    'use_business_dirs': use_business_dirs,
                    'use_professional_sources': use_professional_sources,
                    'use_enhanced_search': use_enhanced_search,
                    'use_enhanced_real': use_enhanced_real,
                    'max_real_results': 100,  # 增加真实数据获取数量
                    'use_social_media': use_social_media,
                    'social_platforms': [],
                    'use_enterprise_info': use_enterprise_info,
                    'enterprise_platforms': []
                }

                # 配置社交媒体平台
                if use_social_media:
                    social_platforms = []
                    if use_douyin:
                        social_platforms.append('douyin')
                    if use_kuaishou:
                        social_platforms.append('kuaishou')
                    crawler_config['social_platforms'] = social_platforms

                # 配置企业信息平台
                if use_enterprise_info:
                    enterprise_platforms = []
                    if use_qichacha:
                        enterprise_platforms.append('qichacha')
                    if use_tianyancha:
                        enterprise_platforms.append('tianyancha')
                    if use_dingding:
                        enterprise_platforms.append('dingding')
                    crawler_config['enterprise_platforms'] = enterprise_platforms
                
                status_text.text("🔍 正在智能收集数据...")
                progress_bar.progress(40)

                # 首先尝试获取真实招投标数据
                real_bidding_data = pd.DataFrame()
                if use_real_bidding:
                    try:
                        status_text.text("🏛️ 正在获取真实招投标数据...")
                        real_crawler = RealBiddingCrawler()
                        real_bidding_data = real_crawler.search_real_bidding_data(
                            industry=industry,
                            province=province,
                            city=city,
                            max_results=30
                        )
                        if not real_bidding_data.empty:
                            st.success(f"✅ 成功获取 {len(real_bidding_data)} 条真实招投标数据")
                        else:
                            st.warning("⚠️ 未获取到真实招投标数据，将使用其他数据源")
                    except Exception as e:
                        st.warning(f"⚠️ 真实数据获取失败: {str(e)}")
                        logger.error(f"真实数据获取失败: {e}")

                progress_bar.progress(55)

                # 开始常规爬取
                raw_data = crawler.start_crawling(crawler_config)
                progress_bar.progress(70)

                status_text.text("🔧 正在智能处理数据...")
                # 数据处理
                processed_data = data_processor.process_data(raw_data)

                # 合并真实招投标数据
                if not real_bidding_data.empty:
                    status_text.text("🔗 正在合并真实数据...")
                    try:
                        # 确保列名一致
                        if 'company_name' not in real_bidding_data.columns and 'project_title' in real_bidding_data.columns:
                            real_bidding_data['company_name'] = real_bidding_data['project_title']

                        # 添加缺失的列
                        required_columns = ['company_name', 'industry', 'source', 'data_type']
                        for col in required_columns:
                            if col not in real_bidding_data.columns:
                                if col == 'industry':
                                    real_bidding_data[col] = industry
                                elif col == 'source':
                                    real_bidding_data[col] = '真实招投标数据'
                                elif col == 'data_type':
                                    real_bidding_data[col] = 'real_bidding'
                                else:
                                    real_bidding_data[col] = ''

                        # 合并数据
                        processed_data = pd.concat([processed_data, real_bidding_data], ignore_index=True)
                        processed_data = processed_data.drop_duplicates(subset=['company_name'], keep='first')

                        st.info(f"✅ 已合并真实数据，总计 {len(processed_data)} 条记录")
                    except Exception as e:
                        st.warning(f"合并真实数据时出错: {str(e)}")
                        logger.error(f"合并真实数据出错: {e}")

                progress_bar.progress(85)

                # 保存到数据库
                db_manager.save_data(processed_data, industry)
                progress_bar.progress(95)

                status_text.text("📊 正在生成智能分析图表...")
                # 生成可视化
                charts = visualizer.create_charts(processed_data)
                progress_bar.progress(100)
                
                status_text.text("✅ 数据收集完成!")
                
                # 显示结果
                st.success(f"成功收集到 {len(processed_data)} 条客户信息")

                # 数据筛选功能
                st.subheader("🔍 数据筛选")
                filter_col1, filter_col2, filter_col3 = st.columns(3)

                with filter_col1:
                    # 按公司名称筛选
                    company_filter = st.text_input("公司名称包含", placeholder="输入关键词筛选")

                with filter_col2:
                    # 按数据源筛选
                    if 'source' in processed_data.columns:
                        sources = ['全部'] + list(processed_data['source'].unique())
                        source_filter = st.selectbox("数据源", sources)
                    else:
                        source_filter = '全部'

                with filter_col3:
                    # 按完整度筛选
                    if 'data_completeness' in processed_data.columns:
                        min_completeness = st.slider("最低完整度 (%)", 0, 100, 0)
                    else:
                        min_completeness = 0

                # 应用筛选
                filtered_data = processed_data.copy()

                if company_filter:
                    if 'company_name' in filtered_data.columns:
                        filtered_data = filtered_data[
                            filtered_data['company_name'].str.contains(company_filter, na=False, case=False)
                        ]

                if source_filter != '全部':
                    if 'source' in filtered_data.columns:
                        filtered_data = filtered_data[filtered_data['source'] == source_filter]

                if min_completeness > 0:
                    if 'data_completeness' in filtered_data.columns:
                        filtered_data = filtered_data[filtered_data['data_completeness'] >= min_completeness]

                # 显示筛选后的数据
                st.subheader(f"📋 客户信息 (显示 {len(filtered_data)} / {len(processed_data)} 条)")

                if not filtered_data.empty:
                    st.dataframe(filtered_data, use_container_width=True)
                else:
                    st.warning("没有符合筛选条件的数据")
                
                # 显示图表
                st.subheader("📈 数据分析图表")

                # 创建图表选项卡
                chart_tabs = st.tabs(["📊 统计图表", "🗺️ 地图分析", "📋 综合仪表板"])

                with chart_tabs[0]:
                    # 统计图表
                    for chart_name, chart in charts.items():
                        if chart_name not in ['province_map', 'city_map', 'region_stats', 'dashboard']:
                            st.plotly_chart(chart, use_container_width=True)

                with chart_tabs[1]:
                    # 地图分析
                    if 'province_map' in charts:
                        st.plotly_chart(charts['province_map'], use_container_width=True)

                    if 'city_map' in charts:
                        st.plotly_chart(charts['city_map'], use_container_width=True)

                    if 'region_stats' in charts:
                        st.plotly_chart(charts['region_stats'], use_container_width=True)

                with chart_tabs[2]:
                    # 综合仪表板
                    if 'dashboard' in charts:
                        st.plotly_chart(charts['dashboard'], use_container_width=True)
                
                # 导出功能
                st.subheader("💾 数据导出")

                # 选择导出数据
                export_option = st.radio(
                    "选择导出数据",
                    ["导出筛选后的数据", "导出全部数据"],
                    horizontal=True
                )

                export_data = filtered_data if export_option == "导出筛选后的数据" else processed_data

                if not export_data.empty:
                    col_export1, col_export2, col_export3 = st.columns(3)

                    # 生成文件名
                    filename_base = f"{industry}_{region_display}_客户信息"

                    with col_export1:
                        csv_data = export_data.to_csv(index=False, encoding='utf-8-sig')
                        st.download_button(
                            f"📄 下载 CSV ({len(export_data)}条)",
                            csv_data,
                            f"{filename_base}.csv",
                            "text/csv"
                        )

                    with col_export2:
                        excel_buffer = data_processor.to_excel(export_data)
                        st.download_button(
                            f"📊 下载 Excel ({len(export_data)}条)",
                            excel_buffer,
                            f"{filename_base}.xlsx",
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        )

                    with col_export3:
                        json_data = export_data.to_json(orient='records', force_ascii=False, indent=2)
                        st.download_button(
                            f"📋 下载 JSON ({len(export_data)}条)",
                            json_data,
                            f"{filename_base}.json",
                            "application/json"
                        )
                else:
                    st.warning("没有数据可以导出")
                
            except Exception as e:
                st.error(f"收集过程中出现错误: {str(e)}")
                logger.error(f"爬虫执行错误: {str(e)}")
        
        elif start_crawling and not industry:
            st.warning("请先输入目标行业!")
    
    with col2:
        st.header("📈 系统状态")

        # 显示系统信息
        db_manager = DatabaseManager()
        total_records = db_manager.get_total_records()

        st.metric("总记录数", total_records)

        # 如果有处理后的数据，显示区域统计
        if 'processed_data' in locals() and not processed_data.empty:
            st.subheader("🌍 区域分布")
            region_stats = region_manager.get_region_statistics(processed_data)

            # 显示TOP省份
            if region_stats['top_provinces']:
                st.write("**TOP 省份:**")
                for province, count in region_stats['top_provinces'][:5]:
                    st.write(f"• {province}: {count}条")

            # 显示TOP城市
            if region_stats['top_cities']:
                st.write("**TOP 城市:**")
                for city, count in region_stats['top_cities'][:5]:
                    st.write(f"• {city}: {count}条")

        # 最近收集的行业
        recent_industries = db_manager.get_recent_industries()
        if recent_industries:
            st.subheader("📊 最近收集的行业")
            for industry_info in recent_industries:
                st.write(f"• {industry_info['industry']} ({industry_info['count']}条)")

        # 智能系统使用提示
        st.subheader("🧠 智能系统特色")
        st.info("""
        **🎯 智能行业分析:**
        - 自动识别行业类型和特征
        - 智能推荐专业数据源
        - 生成针对性搜索策略

        **📡 多维度数据采集:**
        - 专业行业网站优先
        - 政府监管平台集成
        - 企业信息多源验证
        - 社交媒体平台整合
        - 企业信息平台深度挖掘

        **🔍 精准搜索策略:**
        - 根据行业特点调整关键词
        - 智能过滤无关信息
        - 多层次数据质量控制

        **📊 智能数据分析:**
        - 自动构建企业关系图谱
        - 智能识别商业机会
        - 多格式数据导出

        **🆕 新增平台支持:**
        - 📱 抖音、快手等短视频平台企业账号
        - 🏢 企查查、天眼查、钉钉企典等企业信息平台
        - 🔗 多平台数据交叉验证
        - 📈 更全面的企业画像构建
        """)

if __name__ == "__main__":
    main()
