# 新增平台数据爬取功能说明

## 🎉 功能概述

成功为客户信息收集系统增加了以下新平台的数据爬取功能：

### 📱 社交媒体平台
- **抖音 (Douyin)** - 短视频平台企业账号信息爬取
- **快手 (Kuaishou)** - 短视频平台企业账号信息爬取

### 🏢 企业信息平台
- **企查查 (Qichacha)** - 专业企业信息查询平台
- **天眼查 (Tianyancha)** - 企业信息和商业关系查询平台
- **钉钉企典 (DingDing)** - 百度爱企查企业信息平台

## 🚀 新增功能特性

### 1. 社交媒体平台爬取
- **智能账号识别**: 自动识别企业官方账号，过滤个人账号
- **粉丝数量筛选**: 支持按粉丝数量筛选，确保账号质量
- **联系信息提取**: 从账号描述中提取电话、微信、邮箱等联系方式
- **认证状态识别**: 识别蓝V认证、企业认证等官方认证状态

### 2. 企业信息平台爬取
- **全面企业信息**: 获取公司名称、法人代表、注册资本、成立日期等
- **经营状态筛选**: 自动筛选存续、在业等正常经营状态的企业
- **多维度搜索**: 支持按行业、地区、公司类型等多维度搜索
- **数据质量控制**: 智能过滤无效和重复数据

### 3. 智能配置系统
- **灵活平台选择**: 用户可自由选择启用哪些平台
- **参数化配置**: 支持搜索关键词、筛选条件等参数配置
- **反爬虫机制**: 内置延时、User-Agent轮换等反检测机制

## 📊 数据字段说明

### 社交媒体平台数据字段
```
- company_name: 企业名称
- platform: 平台名称 (抖音/快手)
- account_name: 账号名称
- account_id: 账号ID
- followers_count: 粉丝数量
- description: 账号描述
- verification_info: 认证信息
- profile_url: 账号链接
- phone: 联系电话
- wechat: 微信号
- email: 邮箱
- address: 地址
```

### 企业信息平台数据字段
```
- company_name: 公司名称
- legal_person: 法定代表人
- registered_capital: 注册资本
- establishment_date: 成立日期
- business_status: 经营状态
- company_type: 公司类型
- business_scope: 经营范围
- phone: 联系电话
- email: 邮箱
- website: 官方网站
- address: 注册地址
- company_url: 详情页链接
```

## 🎯 使用方法

### 1. 启动应用
```bash
streamlit run main.py
```

### 2. 配置搜索参数
1. 输入目标行业（如：酒店建设、房地产开发）
2. 选择搜索区域（省份、城市）
3. 在侧边栏选择数据源：
   - 勾选"社交媒体平台"
   - 选择具体平台：抖音、快手
   - 勾选"企业信息平台"
   - 选择具体平台：企查查、天眼查、钉钉企典

### 3. 开始数据收集
点击"🚀 开始收集"按钮，系统将：
1. 进行智能行业分析
2. 生成针对性搜索关键词
3. 并行爬取各个平台数据
4. 智能去重和数据清洗
5. 生成可视化分析图表

### 4. 数据导出
支持多种格式导出：
- CSV格式
- Excel格式
- JSON格式

## 🔧 技术实现

### 架构设计
```
main.py (主界面)
├── src/crawler_engine.py (核心爬虫引擎)
├── src/social_media_crawler.py (社交媒体爬虫)
├── src/enterprise_info_crawler.py (企业信息爬虫)
├── social_media_config.yaml (社交媒体配置)
└── enterprise_info_config.yaml (企业信息配置)
```

### 关键技术特性
- **模块化设计**: 每个平台独立模块，便于维护和扩展
- **配置文件驱动**: 通过YAML配置文件管理平台参数
- **异常处理**: 完善的错误处理和日志记录
- **演示数据**: 当真实API不可用时，自动生成演示数据
- **反爬虫策略**: 随机延时、请求头轮换、会话管理

## 📈 数据质量保证

### 1. 智能筛选
- 企业关键词匹配
- 行业相关性验证
- 数据完整性检查
- 重复数据去除

### 2. 数据验证
- 电话号码格式验证
- 邮箱地址格式验证
- 公司名称规范性检查
- 地址信息完整性验证

### 3. 质量评分
- 数据完整度评分
- 信息可信度评估
- 联系方式有效性评估

## 🛡️ 合规性说明

### 数据获取合规
- 仅获取公开可访问的信息
- 遵守各平台的robots.txt规则
- 实施合理的访问频率限制
- 不获取用户隐私信息

### 使用建议
- 建议用于合法的商业用途
- 尊重数据主体的权益
- 遵守相关法律法规
- 定期更新和验证数据

## 🔮 未来扩展

### 计划新增平台
- 微博企业账号
- 小红书品牌账号
- B站企业号
- 知乎机构号

### 功能增强
- AI智能数据分析
- 企业关系图谱构建
- 实时数据更新
- 数据质量评估报告

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**版本**: v2.0  
**更新日期**: 2024年12月  
**开发团队**: 爬虫专家团队
