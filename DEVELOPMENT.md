# 🛠️ 开发指南

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <项目地址>
cd customer-info-collector

# 配置虚拟环境
python setup_env.py

# 激活环境（Windows）
activate_env.bat

# 激活环境（macOS/Linux）
source activate_env.sh
```

### 2. 开发流程
```bash
# 安装开发依赖
pip install -r requirements.txt

# 运行演示
python demo.py

# 启动开发服务器
python run.py

# 运行测试（如果有）
python -m pytest tests/
```

## 📁 项目结构详解

```
customer-info-collector/
├── 🚀 启动文件
│   ├── main.py              # Streamlit主应用
│   ├── run.py               # 启动脚本
│   ├── demo.py              # 功能演示
│   └── start.bat            # Windows一键启动
│
├── ⚙️ 配置文件
│   ├── config.yaml          # 主配置文件
│   ├── .env.example         # 环境变量模板
│   ├── requirements.txt     # 依赖包列表
│   └── requirements-lock.txt # 精确版本锁定
│
├── 🐍 环境管理
│   ├── setup_env.py         # 虚拟环境配置
│   ├── install.py           # 安装脚本
│   ├── activate_env.bat     # Windows激活脚本
│   └── activate_env.sh      # Unix激活脚本
│
├── 📦 核心代码
│   └── src/
│       ├── crawler_engine.py    # 爬虫引擎
│       ├── data_processor.py    # 数据处理
│       ├── visualizer.py        # 可视化
│       ├── database_manager.py  # 数据库管理
│       └── utils/               # 工具模块
│           ├── logger.py        # 日志管理
│           └── anti_detection.py # 反反爬
│
├── 💾 数据目录
│   ├── data/                # 数据库和导出文件
│   └── logs/                # 日志文件
│
└── 📚 文档
    ├── README.md            # 用户文档
    ├── DEVELOPMENT.md       # 开发文档
    └── .gitignore          # Git忽略文件
```

## 🔧 开发规范

### 代码风格
- 使用Python PEP 8规范
- 函数和类添加详细的docstring
- 变量命名使用下划线命名法
- 常量使用大写字母

### 提交规范
```bash
# 功能开发
git commit -m "feat: 添加新的数据源支持"

# 问题修复
git commit -m "fix: 修复数据去重算法bug"

# 文档更新
git commit -m "docs: 更新安装说明"

# 性能优化
git commit -m "perf: 优化爬虫并发性能"
```

### 分支管理
```bash
# 主分支
main/master          # 稳定版本

# 开发分支
develop             # 开发版本
feature/xxx         # 功能分支
hotfix/xxx          # 紧急修复
```

## 🧪 测试指南

### 单元测试
```bash
# 创建测试文件
tests/
├── test_crawler.py
├── test_processor.py
└── test_visualizer.py

# 运行测试
python -m pytest tests/ -v

# 测试覆盖率
python -m pytest --cov=src tests/
```

### 集成测试
```bash
# 测试完整流程
python demo.py

# 测试Web界面
python run.py
# 访问 http://localhost:8501
```

## 📦 依赖管理

### 添加新依赖
```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 安装新包
pip install new_package

# 更新requirements.txt
pip freeze > requirements-lock.txt

# 手动更新requirements.txt（只包含直接依赖）
echo "new_package==1.0.0" >> requirements.txt
```

### 依赖升级
```bash
# 升级单个包
pip install --upgrade package_name

# 升级所有包
pip list --outdated
pip install --upgrade package_name1 package_name2

# 重新生成锁定文件
pip freeze > requirements-lock.txt
```

## 🚀 部署指南

### 本地部署
```bash
# 生产环境配置
cp .env.example .env
# 编辑.env文件设置生产配置

# 启动服务
python run.py
```

### Docker部署
```dockerfile
# Dockerfile示例
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8501

CMD ["streamlit", "run", "main.py", "--server.port=8501", "--server.address=0.0.0.0"]
```

### 云服务器部署
```bash
# 安装依赖
sudo apt update
sudo apt install python3 python3-pip python3-venv

# 克隆项目
git clone <项目地址>
cd customer-info-collector

# 配置环境
python3 setup_env.py

# 后台运行
nohup python run.py > app.log 2>&1 &
```

## 🐛 调试技巧

### 日志调试
```python
# 查看日志
tail -f logs/crawler.log

# 调整日志级别
# 在config.yaml中设置
logging:
  level: "DEBUG"  # INFO, WARNING, ERROR
```

### 性能分析
```python
# 使用cProfile
python -m cProfile -o profile.stats run.py

# 分析结果
python -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(10)"
```

### 内存监控
```bash
# 安装memory_profiler
pip install memory-profiler

# 监控内存使用
python -m memory_profiler demo.py
```

## 🔒 安全注意事项

### 敏感信息
- 不要在代码中硬编码API密钥
- 使用.env文件管理敏感配置
- .env文件不要提交到版本控制

### 爬虫道德
- 遵守robots.txt协议
- 设置合理的请求间隔
- 不要对目标网站造成过大压力
- 仅用于合法的商业研究目的

## 📈 性能优化

### 爬虫优化
```python
# 调整并发数
concurrent_requests: 16  # 根据网络和目标网站调整

# 优化延时
download_delay: 1        # 平衡速度和稳定性
randomize_delay: 0.5     # 添加随机性
```

### 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_company_name ON customer_info(company_name);
CREATE INDEX idx_industry ON customer_info(industry);

-- 定期清理
DELETE FROM customer_info WHERE created_at < date('now', '-30 days');
```

## 🤝 贡献指南

### 提交Pull Request
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request
5. 代码审查
6. 合并代码

### 报告问题
- 使用GitHub Issues
- 提供详细的错误信息
- 包含复现步骤
- 附上相关日志

---

💡 **开发提示**: 始终在虚拟环境中开发，保持代码整洁，编写测试，遵循最佳实践！
