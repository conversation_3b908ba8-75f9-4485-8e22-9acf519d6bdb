# 真实数据爬取系统配置文件示例
# 复制此文件为 .env 并填入真实配置

# 抖音配置
DOUYIN_USER_URL=
DOUYIN_COOKIES=

# 快手配置  
KUAISHOU_USER_URL=
KUAISHOU_COOKIES=

# 企查查配置 (需要真实账号)
QICHACHA_USERNAME=your_username
QICHACHA_PASSWORD=your_password
QICHACHA_COOKIES=

# 天眼查配置
TIANYANCHA_USERNAME=
TIANYANCHA_PASSWORD=
TIANYANCHA_API_KEY=

# 代理配置 (可选，推荐使用)
PROXY_HOST=
PROXY_PORT=
PROXY_USERNAME=
PROXY_PASSWORD=

# OCR验证码识别 (推荐配置)
OCR_API_KEY=
OCR_PROVIDER=2captcha

# 数据库配置 (可选)
MONGODB_URL=mongodb://localhost:27017/crawler_data
REDIS_URL=redis://localhost:6379

# 系统配置
HEADLESS=false
CONCURRENT_LIMIT=3
REQUEST_DELAY=2000
