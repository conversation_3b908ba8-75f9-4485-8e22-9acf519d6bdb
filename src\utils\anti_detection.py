#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反反爬检测模块 - 专家级反反爬策略
"""

import random
import time
from typing import List, Dict
from fake_useragent import UserAgent
import requests

class AntiDetection:
    """反反爬检测类"""
    
    def __init__(self):
        """初始化反反爬工具"""
        self.ua = UserAgent()
        
        # 用户代理池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
        ]
        
        # 请求头模板
        self.header_templates = [
            {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            },
            {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        ]
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        headers = random.choice(self.header_templates).copy()
        headers['User-Agent'] = random.choice(self.user_agents)
        
        # 随机添加一些可选头部
        optional_headers = {
            'DNT': '1',
            'Cache-Control': 'max-age=0',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        }
        
        # 随机选择添加一些可选头部
        for key, value in optional_headers.items():
            if random.random() > 0.5:
                headers[key] = value
        
        return headers
    
    def random_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """随机延时"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def exponential_backoff(self, attempt: int, base_delay: float = 1.0, max_delay: float = 60.0):
        """指数退避延时"""
        delay = min(base_delay * (2 ** attempt), max_delay)
        jitter = random.uniform(0, delay * 0.1)  # 添加抖动
        time.sleep(delay + jitter)
    
    def is_blocked(self, response: requests.Response) -> bool:
        """检测是否被反爬"""
        # 检查状态码
        if response.status_code in [403, 429, 503]:
            return True
        
        # 检查响应内容
        blocked_keywords = [
            '验证码', '人机验证', '安全验证', 'captcha', 'blocked',
            '访问频率', '请稍后再试', 'rate limit', 'too many requests'
        ]
        
        response_text = response.text.lower()
        for keyword in blocked_keywords:
            if keyword in response_text:
                return True
        
        return False
    
    def get_session_with_retries(self, max_retries: int = 3) -> requests.Session:
        """获取带重试机制的会话"""
        session = requests.Session()
        
        # 设置重试适配器
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
