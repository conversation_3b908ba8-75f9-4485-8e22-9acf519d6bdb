#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright环境测试脚本
验证Playwright是否正确安装和配置
"""

import asyncio
import sys
from playwright.async_api import async_playwright

async def test_playwright():
    """测试Playwright基础功能"""
    print("🔍 开始测试Playwright环境...")
    
    try:
        async with async_playwright() as p:
            print("✅ Playwright启动成功")
            
            # 启动浏览器
            browser = await p.chromium.launch(headless=True)
            print("✅ Chromium浏览器启动成功")
            
            # 创建页面
            page = await browser.new_page()
            print("✅ 新页面创建成功")
            
            # 访问测试页面
            await page.goto("https://www.baidu.com")
            print("✅ 页面导航成功")
            
            # 获取页面标题
            title = await page.title()
            print(f"✅ 页面标题: {title}")
            
            # 关闭浏览器
            await browser.close()
            print("✅ 浏览器关闭成功")
            
            print("\n🎉 Playwright环境测试完成，一切正常！")
            return True
            
    except Exception as e:
        print(f"❌ Playwright测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Playwright环境测试")
    print("=" * 50)
    
    # 运行异步测试
    success = asyncio.run(test_playwright())
    
    if success:
        print("\n✅ 环境配置成功，可以开始下一步开发")
        sys.exit(0)
    else:
        print("\n❌ 环境配置失败，请检查安装")
        sys.exit(1)

if __name__ == "__main__":
    main()
