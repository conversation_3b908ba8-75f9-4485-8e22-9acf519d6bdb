#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据抓取问题诊断工具
分析为什么无法抓取到真实数据的具体原因
"""

import sys
import os
from pathlib import Path
import requests
import time
import json
from urllib.parse import urlencode

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_network():
    """测试基础网络连接"""
    print("🌐 基础网络连接测试")
    print("=" * 50)
    
    test_sites = [
        ("百度", "https://www.baidu.com"),
        ("搜狗", "https://www.sogou.com"),
        ("360搜索", "https://www.so.com"),
        ("必应", "https://www.bing.com"),
        ("企查查", "https://www.qcc.com"),
        ("天眼查", "https://www.tianyancha.com")
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for name, url in test_sites:
        try:
            print(f"测试 {name}...")
            response = requests.get(url, headers=headers, timeout=10)
            print(f"  ✅ {name}: 状态码 {response.status_code}, 响应长度 {len(response.text)}")
            
            # 检查是否被重定向到验证页面
            if "验证" in response.text or "captcha" in response.text.lower():
                print(f"  ⚠️ {name}: 可能遇到验证码或反爬虫机制")
            
        except Exception as e:
            print(f"  ❌ {name}: 连接失败 - {e}")
        
        time.sleep(1)

def test_search_engines():
    """测试搜索引擎数据抓取"""
    print("\n🔍 搜索引擎数据抓取测试")
    print("=" * 50)
    
    search_tests = [
        {
            "name": "百度搜索",
            "url": "https://www.baidu.com/s",
            "params": {"wd": "北京酒店管理公司"},
            "expected_keywords": ["公司", "酒店", "管理"]
        },
        {
            "name": "搜狗搜索", 
            "url": "https://www.sogou.com/web",
            "params": {"query": "上海房地产开发企业"},
            "expected_keywords": ["企业", "房地产", "开发"]
        }
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    }
    
    for test in search_tests:
        try:
            print(f"测试 {test['name']}...")
            
            # 构建完整URL
            full_url = f"{test['url']}?{urlencode(test['params'])}"
            print(f"  请求URL: {full_url}")
            
            response = requests.get(test['url'], params=test['params'], headers=headers, timeout=15)
            
            print(f"  状态码: {response.status_code}")
            print(f"  响应长度: {len(response.text)}")
            
            # 检查响应内容
            content = response.text
            
            # 检查是否包含预期关键词
            found_keywords = [kw for kw in test['expected_keywords'] if kw in content]
            print(f"  找到关键词: {found_keywords}")
            
            # 检查是否遇到反爬虫
            anti_bot_indicators = ["验证码", "captcha", "robot", "blocked", "forbidden"]
            anti_bot_found = [indicator for indicator in anti_bot_indicators if indicator.lower() in content.lower()]
            
            if anti_bot_found:
                print(f"  ⚠️ 检测到反爬虫机制: {anti_bot_found}")
            
            # 尝试提取企业名称
            import re
            company_patterns = [
                r'([^<>]*(?:有限公司|股份有限公司|集团|企业))',
                r'title="([^"]*(?:公司|企业|集团)[^"]*)"'
            ]
            
            companies_found = []
            for pattern in company_patterns:
                matches = re.findall(pattern, content)
                companies_found.extend(matches[:3])  # 只取前3个
            
            if companies_found:
                print(f"  ✅ 提取到企业: {companies_found[:3]}")
            else:
                print(f"  ❌ 未提取到企业信息")
                
            # 保存响应内容用于调试
            debug_file = f"debug_{test['name'].replace(' ', '_')}_response.html"
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  💾 响应内容已保存到: {debug_file}")
            
        except Exception as e:
            print(f"  ❌ {test['name']} 测试失败: {e}")
        
        print()
        time.sleep(2)

def test_enterprise_platforms():
    """测试企业信息平台"""
    print("🏢 企业信息平台测试")
    print("=" * 50)
    
    platforms = [
        {
            "name": "企查查",
            "url": "https://www.qcc.com",
            "search_path": "/web/search",
            "params": {"key": "万达集团"}
        },
        {
            "name": "天眼查",
            "url": "https://www.tianyancha.com",
            "search_path": "/search",
            "params": {"key": "恒大地产"}
        }
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Referer': 'https://www.baidu.com'
    }
    
    for platform in platforms:
        try:
            print(f"测试 {platform['name']}...")
            
            # 先访问首页
            home_response = requests.get(platform['url'], headers=headers, timeout=10)
            print(f"  首页访问: 状态码 {home_response.status_code}")
            
            # 尝试搜索
            search_url = platform['url'] + platform['search_path']
            search_response = requests.get(search_url, params=platform['params'], headers=headers, timeout=10)
            
            print(f"  搜索请求: 状态码 {search_response.status_code}")
            print(f"  响应长度: {len(search_response.text)}")
            
            # 检查是否需要登录
            if "登录" in search_response.text or "login" in search_response.text.lower():
                print(f"  ⚠️ {platform['name']}: 需要登录")
            
            # 检查是否有验证码
            if "验证码" in search_response.text or "captcha" in search_response.text.lower():
                print(f"  ⚠️ {platform['name']}: 需要验证码")
            
            # 检查是否被限制
            if "限制" in search_response.text or "blocked" in search_response.text.lower():
                print(f"  ⚠️ {platform['name']}: 访问被限制")
                
        except Exception as e:
            print(f"  ❌ {platform['name']} 测试失败: {e}")
        
        time.sleep(2)

def test_current_crawler():
    """测试当前爬虫系统"""
    print("\n🤖 当前爬虫系统测试")
    print("=" * 50)
    
    try:
        from src.enhanced_real_crawler import EnhancedRealCrawler
        
        print("初始化爬虫...")
        crawler = EnhancedRealCrawler()
        
        print("测试数据收集...")
        results = crawler.search_real_business_data(
            industry="酒店管理",
            province="北京市",
            city="朝阳区",
            max_results=5
        )
        
        if hasattr(results, 'empty') and not results.empty:
            print(f"✅ 收集到 {len(results)} 条数据")
            print("数据样例:")
            for idx, (_, row) in enumerate(results.head(3).iterrows(), 1):
                print(f"  {idx}. {row.get('company_name', 'N/A')} (来源: {row.get('source', 'N/A')})")
        elif isinstance(results, list) and results:
            print(f"✅ 收集到 {len(results)} 条数据")
            print("数据样例:")
            for idx, item in enumerate(results[:3], 1):
                print(f"  {idx}. {item.get('company_name', 'N/A')} (来源: {item.get('source', 'N/A')})")
        else:
            print("❌ 未收集到数据")
            
    except Exception as e:
        print(f"❌ 爬虫系统测试失败: {e}")

def main():
    """主函数"""
    print("🔍 数据抓取问题诊断工具")
    print("=" * 80)
    print("正在分析为什么无法抓取到真实数据...")
    print("=" * 80)
    
    # 1. 基础网络测试
    test_basic_network()
    
    # 2. 搜索引擎测试
    test_search_engines()
    
    # 3. 企业平台测试
    test_enterprise_platforms()
    
    # 4. 当前爬虫测试
    test_current_crawler()
    
    print("\n📋 诊断完成")
    print("=" * 50)
    print("💡 请查看上述测试结果，分析数据抓取失败的具体原因")
    print("💡 如果遇到验证码或反爬虫机制，建议:")
    print("   1. 使用代理IP池")
    print("   2. 降低请求频率")
    print("   3. 使用浏览器自动化工具")
    print("   4. 考虑使用付费API服务")

if __name__ == "__main__":
    main()
