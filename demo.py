#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户信息收集系统 - 演示脚本
展示系统核心功能和专家思维
"""

import pandas as pd
import time
from datetime import datetime
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from src.crawler_engine import CrawlerEngine
from src.data_processor import DataProcessor
from src.visualizer import DataVisualizer
from src.database_manager import DatabaseManager
from src.utils.logger import setup_logger

def create_demo_data():
    """创建演示数据"""
    demo_data = [
        {
            'company_name': '北京智能科技有限公司',
            'industry': '人工智能',
            'website': 'https://ai-tech.com',
            'phone': '010-12345678',
            'email': '<EMAIL>',
            'address': '北京市海淀区中关村',
            'description': '专注于人工智能技术研发的高新技术企业',
            'source': 'baidu'
        },
        {
            'company_name': '上海机器学习研究院',
            'industry': '人工智能',
            'website': 'https://ml-research.com',
            'phone': '021-87654321',
            'email': '<EMAIL>',
            'address': '上海市浦东新区张江',
            'description': '机器学习算法研究与应用',
            'source': 'bing'
        },
        {
            'company_name': '深圳自动驾驶科技公司',
            'industry': '人工智能',
            'website': 'https://autonomous-drive.com',
            'phone': '0755-11111111',
            'email': '<EMAIL>',
            'address': '深圳市南山区科技园',
            'description': '自动驾驶技术开发与测试',
            'source': 'baidu'
        },
        {
            'company_name': '广州语音识别技术有限公司',
            'industry': '人工智能',
            'website': 'https://voice-tech.com',
            'phone': '020-22222222',
            'email': '<EMAIL>',
            'address': '广州市天河区珠江新城',
            'description': '语音识别和自然语言处理技术',
            'source': 'bing'
        },
        {
            'company_name': '杭州计算机视觉实验室',
            'industry': '人工智能',
            'website': 'https://cv-lab.com',
            'phone': '0571-33333333',
            'email': '<EMAIL>',
            'address': '杭州市西湖区文三路',
            'description': '计算机视觉算法研究',
            'source': 'baidu'
        }
    ]
    
    return pd.DataFrame(demo_data)

def demonstrate_expert_thinking():
    """展示专家思维过程"""
    print("🧠 专家思维分析过程")
    print("=" * 60)
    
    print("\n1️⃣ 需求分析阶段:")
    print("   • 目标: 收集特定行业的客户信息")
    print("   • 数据源: 搜索引擎 + 企业目录 + 社交媒体")
    print("   • 输出: 结构化数据表 + 可视化分析")
    
    print("\n2️⃣ 技术架构设计:")
    print("   • 爬虫引擎: 多线程 + 反反爬 + 容错机制")
    print("   • 数据处理: 清洗 + 去重 + 标准化 + 增强")
    print("   • 存储方案: SQLite + 备份 + 索引优化")
    print("   • 可视化: Plotly交互式图表 + Streamlit界面")
    
    print("\n3️⃣ 反反爬策略:")
    print("   • 请求头轮换: 模拟真实浏览器行为")
    print("   • 智能延时: 随机延时 + 指数退避")
    print("   • 代理池: 分布式IP访问（可选）")
    print("   • 会话管理: 自动重试 + 状态保持")
    
    print("\n4️⃣ 数据质量保证:")
    print("   • 多层验证: 格式验证 + 业务逻辑验证")
    print("   • 智能去重: 精确匹配 + 模糊匹配")
    print("   • 数据增强: 自动推断 + 信息补全")
    print("   • 质量评分: 完整度计算 + 可信度评估")

def demonstrate_system_workflow():
    """演示系统工作流程"""
    print("\n🔄 系统工作流程演示")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logger()
    
    # 1. 初始化组件
    print("\n📦 初始化系统组件...")
    crawler = CrawlerEngine()
    processor = DataProcessor()
    visualizer = DataVisualizer()
    db_manager = DatabaseManager()
    
    # 2. 模拟数据收集
    print("\n🕷️ 模拟数据收集过程...")
    print("   正在搜索 '人工智能' 相关企业...")
    time.sleep(1)
    
    # 使用演示数据
    raw_data = create_demo_data()
    print(f"   ✅ 收集到 {len(raw_data)} 条原始数据")
    
    # 3. 数据处理
    print("\n🔧 数据处理与清洗...")
    processed_data = processor.process_data(raw_data)
    print(f"   ✅ 处理完成，得到 {len(processed_data)} 条有效数据")
    
    # 4. 数据存储
    print("\n💾 保存数据到数据库...")
    success = db_manager.save_data(processed_data, "人工智能")
    if success:
        print("   ✅ 数据保存成功")
    
    # 5. 可视化生成
    print("\n📊 生成可视化图表...")
    charts = visualizer.create_charts(processed_data)
    print(f"   ✅ 生成 {len(charts)} 个图表")
    
    # 6. 数据分析报告
    print("\n📋 生成数据分析报告...")
    report = visualizer.create_summary_report(processed_data)
    
    print("\n📈 数据分析结果:")
    for section, data in report.items():
        print(f"\n   {section}:")
        if isinstance(data, dict):
            for key, value in data.items():
                print(f"     • {key}: {value}")
        else:
            print(f"     {data}")
    
    # 7. 展示数据表格
    print("\n📋 收集到的企业信息:")
    print("-" * 80)
    for idx, row in processed_data.iterrows():
        print(f"企业 {idx+1}:")
        print(f"  公司名称: {row.get('公司名称', 'N/A')}")
        print(f"  官网: {row.get('官网', 'N/A')}")
        print(f"  电话: {row.get('电话', 'N/A')}")
        print(f"  地址: {row.get('地址', 'N/A')}")
        print()

def demonstrate_expert_features():
    """展示专家级功能特性"""
    print("\n🎯 专家级功能特性展示")
    print("=" * 60)
    
    print("\n🔍 智能搜索策略:")
    print("   • 关键词扩展: '人工智能' → ['AI公司', 'AI企业', 'AI厂家', 'AI供应商']")
    print("   • 多源并行: 同时搜索百度、必应、企查查等")
    print("   • 深度挖掘: 自动翻页，获取更多结果")
    
    print("\n🧹 数据清洗算法:")
    print("   • 公司名称标准化: '北京XX有限责任公司' → '北京XX有限公司'")
    print("   • 联系方式格式化: '13812345678' → '138-1234-5678'")
    print("   • 网址标准化: 'www.example.com' → 'https://www.example.com'")
    
    print("\n🔄 智能去重机制:")
    print("   • 精确去重: 完全相同的记录")
    print("   • 模糊去重: 相似度>85%的公司名称")
    print("   • 业务去重: 同一公司的不同表述")
    
    print("\n📊 多维度分析:")
    print("   • 行业分布: 饼图展示各行业占比")
    print("   • 规模分析: 大中小型企业分布")
    print("   • 完整度评估: 数据质量评分")
    print("   • 地域分析: 企业地理分布")
    
    print("\n🛡️ 反反爬技术:")
    print("   • 请求伪装: 模拟真实用户行为")
    print("   • 频率控制: 智能调节访问频率")
    print("   • 异常处理: 自动识别并应对反爬")
    print("   • 重试机制: 失败自动重试")

def main():
    """主演示函数"""
    print("🕷️ 智能客户信息收集系统 - 专家演示")
    print("=" * 60)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 展示专家思维
        demonstrate_expert_thinking()
        
        # 展示系统工作流程
        demonstrate_system_workflow()
        
        # 展示专家级功能
        demonstrate_expert_features()
        
        print("\n🎉 演示完成!")
        print("\n💡 启动完整系统:")
        print("   python run.py")
        print("\n📖 查看详细文档:")
        print("   README.md")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查依赖包是否正确安装")

if __name__ == "__main__":
    main()
