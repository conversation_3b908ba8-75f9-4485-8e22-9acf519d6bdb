#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块 - 数据清洗、去重、标准化
专家级数据处理，确保数据质量和一致性
"""

import pandas as pd
import numpy as np
import re
from typing import List, Dict, Any, Tuple
try:
    from fuzzywuzzy import fuzz, process
except ImportError:
    fuzz = None
    process = None
import io
from urllib.parse import urlparse
try:
    import phonenumbers
    from phonenumbers import NumberParseException
except ImportError:
    phonenumbers = None
    NumberParseException = Exception

try:
    from .utils.logger import get_logger
except ImportError:
    from utils.logger import get_logger

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.logger = get_logger(__name__)
        
        # 定义数据字段映射
        self.field_mapping = {
            'company_name': '公司名称',
            'industry': '行业',
            'website': '官网',
            'phone': '电话',
            'email': '邮箱',
            'address': '地址',
            'description': '描述',
            'source': '数据源',
            'contact_person': '联系人',
            'company_size': '公司规模',
            'registration_capital': '注册资本',
            'business_scope': '经营范围'
        }
        
        # 公司规模关键词映射
        self.company_size_keywords = {
            '大型': ['大型', '集团', '股份', '上市', '总部'],
            '中型': ['中型', '有限公司', '科技'],
            '小型': ['小型', '工作室', '个体', '小微']
        }
    
    def process_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """处理原始数据"""
        self.logger.info(f"开始处理数据，原始数据量: {len(raw_data)}")
        
        if raw_data.empty:
            self.logger.warning("原始数据为空")
            return pd.DataFrame()
        
        # 数据清洗流程
        df = raw_data.copy()
        
        # 1. 基础清洗
        df = self._basic_cleaning(df)
        
        # 2. 数据标准化
        df = self._standardize_data(df)
        
        # 3. 去重处理
        df = self._remove_duplicates(df)
        
        # 4. 数据增强
        df = self._enhance_data(df)
        
        # 5. 数据验证
        df = self._validate_data(df)
        
        # 6. 字段重命名
        df = self._rename_fields(df)
        
        self.logger.info(f"数据处理完成，最终数据量: {len(df)}")
        
        return df
    
    def _basic_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """基础数据清洗"""
        self.logger.info("执行基础数据清洗")
        
        # 移除空行
        df = df.dropna(subset=['company_name'], how='all')
        
        # 清理文本字段
        text_fields = ['company_name', 'description', 'address', 'contact_person']
        
        for field in text_fields:
            if field in df.columns:
                # 移除多余空格
                df[field] = df[field].astype(str).str.strip()
                # 移除特殊字符
                df[field] = df[field].str.replace(r'[^\w\s\u4e00-\u9fff\-\(\)（）]', '', regex=True)
                # 替换空字符串为NaN
                df[field] = df[field].replace('', np.nan).infer_objects(copy=False)
                df[field] = df[field].replace('nan', np.nan).infer_objects(copy=False)
        
        return df
    
    def _standardize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据标准化"""
        self.logger.info("执行数据标准化")
        
        # 标准化公司名称
        if 'company_name' in df.columns:
            df['company_name'] = df['company_name'].apply(self._standardize_company_name)
        
        # 标准化网站URL
        if 'website' in df.columns:
            df['website'] = df['website'].apply(self._standardize_website)
        
        # 标准化电话号码
        if 'phone' in df.columns:
            df['phone'] = df['phone'].apply(self._standardize_phone)
        
        # 标准化邮箱
        if 'email' in df.columns:
            df['email'] = df['email'].apply(self._standardize_email)
        
        return df
    
    def _standardize_company_name(self, name: str) -> str:
        """标准化公司名称"""
        if pd.isna(name) or name == '':
            return name
        
        name = str(name).strip()
        
        # 移除常见前缀后缀
        prefixes_to_remove = ['官网', '首页', '主页', '网站']
        suffixes_to_remove = ['官网', '首页', '主页', '网站', '公司简介']
        
        for prefix in prefixes_to_remove:
            if name.startswith(prefix):
                name = name[len(prefix):].strip()
        
        for suffix in suffixes_to_remove:
            if name.endswith(suffix):
                name = name[:-len(suffix)].strip()
        
        # 标准化公司类型
        company_type_mapping = {
            '有限责任公司': '有限公司',
            '股份有限责任公司': '股份有限公司',
            'Co.,Ltd': '有限公司',
            'Co., Ltd': '有限公司',
            'Ltd': '有限公司'
        }
        
        for old_type, new_type in company_type_mapping.items():
            name = name.replace(old_type, new_type)
        
        return name
    
    def _standardize_website(self, website: str) -> str:
        """标准化网站URL"""
        if pd.isna(website) or website == '':
            return website
        
        website = str(website).strip()
        
        # 移除百度跳转链接
        if 'baidu.com' in website and 'url=' in website:
            try:
                from urllib.parse import unquote, parse_qs, urlparse
                parsed = urlparse(website)
                if 'url' in parse_qs(parsed.query):
                    website = parse_qs(parsed.query)['url'][0]
                    website = unquote(website)
            except:
                pass
        
        # 确保有协议
        if not website.startswith(('http://', 'https://')):
            if website.startswith('www.'):
                website = 'https://' + website
            elif '.' in website:
                website = 'https://' + website
        
        # 移除尾部斜杠
        website = website.rstrip('/')
        
        return website
    
    def _standardize_phone(self, phone: str) -> str:
        """标准化电话号码"""
        if pd.isna(phone) or phone == '':
            return phone
        
        phone = str(phone).strip()
        
        # 移除非数字字符（保留+、-、空格、括号）
        phone = re.sub(r'[^\d\+\-\s\(\)]', '', phone)
        
        # 尝试解析电话号码（如果phonenumbers可用）
        if phonenumbers:
            try:
                # 假设是中国号码
                parsed_number = phonenumbers.parse(phone, "CN")
                if phonenumbers.is_valid_number(parsed_number):
                    return phonenumbers.format_number(parsed_number, phonenumbers.PhoneNumberFormat.NATIONAL)
            except NumberParseException:
                pass
        
        # 简单格式化
        digits_only = re.sub(r'[^\d]', '', phone)
        
        if len(digits_only) == 11 and digits_only.startswith('1'):
            # 手机号码格式化
            return f"{digits_only[:3]}-{digits_only[3:7]}-{digits_only[7:]}"
        elif len(digits_only) >= 7:
            # 固定电话格式化
            if len(digits_only) == 8:
                return f"{digits_only[:4]}-{digits_only[4:]}"
            elif len(digits_only) >= 10:
                return f"{digits_only[:-8]}-{digits_only[-8:-4]}-{digits_only[-4:]}"
        
        return phone
    
    def _standardize_email(self, email: str) -> str:
        """标准化邮箱地址"""
        if pd.isna(email) or email == '':
            return email
        
        email = str(email).strip().lower()
        
        # 验证邮箱格式
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if re.match(email_pattern, email):
            return email
        else:
            return np.nan
    
    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """去重处理"""
        self.logger.info("执行去重处理")
        
        initial_count = len(df)
        
        # 1. 精确去重
        df = df.drop_duplicates(subset=['company_name'], keep='first')
        
        # 2. 模糊去重
        df = self._fuzzy_deduplication(df)
        
        final_count = len(df)
        removed_count = initial_count - final_count
        
        self.logger.info(f"去重完成，移除 {removed_count} 条重复数据")
        
        return df
    
    def _fuzzy_deduplication(self, df: pd.DataFrame) -> pd.DataFrame:
        """模糊去重"""
        if 'company_name' not in df.columns or len(df) < 2:
            return df
        
        # 使用模糊匹配找出相似的公司名称
        company_names = df['company_name'].dropna().tolist()
        duplicates_to_remove = set()
        
        for i, name1 in enumerate(company_names):
            if i in duplicates_to_remove:
                continue
                
            for j, name2 in enumerate(company_names[i+1:], i+1):
                if j in duplicates_to_remove:
                    continue
                
                # 计算相似度
                if fuzz:
                    similarity = fuzz.ratio(name1, name2)
                else:
                    # 简单的字符串相似度计算
                    similarity = self._simple_similarity(name1, name2)
                
                # 如果相似度超过85%，认为是重复
                if similarity > 85:
                    duplicates_to_remove.add(j)
        
        # 移除重复项
        if duplicates_to_remove:
            indices_to_keep = [i for i in range(len(df)) if i not in duplicates_to_remove]
            df = df.iloc[indices_to_keep].reset_index(drop=True)
        
        return df

    def _simple_similarity(self, str1: str, str2: str) -> float:
        """简单的字符串相似度计算"""
        if not str1 or not str2:
            return 0.0

        # 转换为小写并去除空格
        s1 = str1.lower().replace(' ', '')
        s2 = str2.lower().replace(' ', '')

        if s1 == s2:
            return 100.0

        # 计算最长公共子序列长度
        max_len = max(len(s1), len(s2))
        if max_len == 0:
            return 100.0

        # 简单的包含关系检查
        if s1 in s2 or s2 in s1:
            return 90.0

        # 计算公共字符数
        common_chars = len(set(s1) & set(s2))
        total_chars = len(set(s1) | set(s2))

        if total_chars == 0:
            return 0.0

        return (common_chars / total_chars) * 100

    def _enhance_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据增强"""
        self.logger.info("执行数据增强")
        
        # 推断公司规模
        if 'company_name' in df.columns:
            df['company_size'] = df['company_name'].apply(self._infer_company_size)
        
        # 提取域名
        if 'website' in df.columns:
            df['domain'] = df['website'].apply(self._extract_domain)
        
        # 计算数据完整度
        df['data_completeness'] = self._calculate_completeness(df)
        
        return df
    
    def _infer_company_size(self, company_name: str) -> str:
        """推断公司规模"""
        if pd.isna(company_name):
            return '未知'
        
        company_name = str(company_name).lower()
        
        for size, keywords in self.company_size_keywords.items():
            for keyword in keywords:
                if keyword in company_name:
                    return size
        
        return '未知'
    
    def _extract_domain(self, website: str) -> str:
        """提取域名"""
        if pd.isna(website) or website == '':
            return ''
        
        try:
            parsed = urlparse(str(website))
            return parsed.netloc
        except:
            return ''
    
    def _calculate_completeness(self, df: pd.DataFrame) -> pd.Series:
        """计算数据完整度"""
        key_fields = ['company_name', 'website', 'phone', 'email', 'address']
        available_fields = [field for field in key_fields if field in df.columns]
        
        if not available_fields:
            return pd.Series([0] * len(df))
        
        completeness = df[available_fields].notna().sum(axis=1) / len(available_fields)
        return (completeness * 100).round(1)
    
    def _validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据验证"""
        self.logger.info("执行数据验证")
        
        # 移除无效记录
        if 'company_name' in df.columns:
            # 移除公司名称过短的记录
            df = df[df['company_name'].str.len() >= 2]
        
        # 验证网站URL
        if 'website' in df.columns:
            df['website'] = df['website'].apply(self._validate_website)
        
        # 验证邮箱
        if 'email' in df.columns:
            df['email'] = df['email'].apply(self._validate_email)
        
        return df
    
    def _validate_website(self, website: str) -> str:
        """验证网站URL"""
        if pd.isna(website) or website == '':
            return website
        
        try:
            parsed = urlparse(str(website))
            if parsed.scheme and parsed.netloc:
                return website
            else:
                return np.nan
        except:
            return np.nan
    
    def _validate_email(self, email: str) -> str:
        """验证邮箱地址"""
        if pd.isna(email) or email == '':
            return email
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if re.match(email_pattern, str(email)):
            return email
        else:
            return np.nan
    
    def _rename_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """重命名字段为中文"""
        rename_mapping = {}
        for eng_name, chn_name in self.field_mapping.items():
            if eng_name in df.columns:
                rename_mapping[eng_name] = chn_name
        
        if rename_mapping:
            df = df.rename(columns=rename_mapping)
        
        return df
    
    def to_excel(self, df: pd.DataFrame, include_summary: bool = True) -> bytes:
        """导出为Excel格式"""
        buffer = io.BytesIO()

        with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
            # 主数据表
            df.to_excel(writer, sheet_name='客户信息', index=False)

            # 设置列宽
            worksheet = writer.sheets['客户信息']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # 添加数据摘要表
            if include_summary and not df.empty:
                summary_data = self._generate_summary_data(df)
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='数据摘要', index=False)

                # 添加统计分析表
                stats_data = self._generate_statistics_data(df)
                if stats_data:
                    stats_df = pd.DataFrame(stats_data)
                    stats_df.to_excel(writer, sheet_name='统计分析', index=False)

        buffer.seek(0)
        return buffer.getvalue()

    def _generate_summary_data(self, df: pd.DataFrame) -> List[Dict]:
        """生成数据摘要"""
        summary = []

        # 基本统计
        summary.append({'指标': '总记录数', '数值': len(df)})

        # 按行业统计
        if '行业' in df.columns:
            industry_counts = df['行业'].value_counts()
            summary.append({'指标': '涉及行业数', '数值': len(industry_counts)})
            summary.append({'指标': '主要行业', '数值': industry_counts.index[0] if len(industry_counts) > 0 else 'N/A'})

        # 数据完整度统计
        if 'data_completeness' in df.columns:
            avg_completeness = df['data_completeness'].mean()
            summary.append({'指标': '平均数据完整度', '数值': f"{avg_completeness:.1f}%"})

        # 联系方式统计
        if '电话' in df.columns:
            phone_count = df['电话'].notna().sum()
            summary.append({'指标': '有电话信息', '数值': f"{phone_count} ({phone_count/len(df)*100:.1f}%)"})

        if '邮箱' in df.columns:
            email_count = df['邮箱'].notna().sum()
            summary.append({'指标': '有邮箱信息', '数值': f"{email_count} ({email_count/len(df)*100:.1f}%)"})

        if '官网' in df.columns:
            website_count = df['官网'].notna().sum()
            summary.append({'指标': '有官网信息', '数值': f"{website_count} ({website_count/len(df)*100:.1f}%)"})

        return summary

    def _generate_statistics_data(self, df: pd.DataFrame) -> List[Dict]:
        """生成统计分析数据"""
        stats = []

        # 行业分布统计
        if '行业' in df.columns:
            industry_stats = df['行业'].value_counts().head(10)
            for industry, count in industry_stats.items():
                stats.append({
                    '分类': '行业分布',
                    '项目': industry,
                    '数量': count,
                    '占比': f"{count/len(df)*100:.1f}%"
                })

        # 公司规模统计
        if 'company_size' in df.columns:
            size_stats = df['company_size'].value_counts()
            for size, count in size_stats.items():
                stats.append({
                    '分类': '公司规模',
                    '项目': size,
                    '数量': count,
                    '占比': f"{count/len(df)*100:.1f}%"
                })

        # 数据源统计
        if 'source' in df.columns:
            source_stats = df['source'].value_counts()
            for source, count in source_stats.items():
                stats.append({
                    '分类': '数据源',
                    '项目': source,
                    '数量': count,
                    '占比': f"{count/len(df)*100:.1f}%"
                })

        return stats

    def to_csv_with_encoding(self, df: pd.DataFrame, encoding: str = 'utf-8-sig') -> str:
        """导出为CSV格式，支持不同编码"""
        return df.to_csv(index=False, encoding=encoding)

    def to_json_formatted(self, df: pd.DataFrame, format_type: str = 'records') -> str:
        """导出为格式化的JSON"""
        if format_type == 'records':
            return df.to_json(orient='records', force_ascii=False, indent=2)
        elif format_type == 'index':
            return df.to_json(orient='index', force_ascii=False, indent=2)
        elif format_type == 'values':
            return df.to_json(orient='values', force_ascii=False, indent=2)
        else:
            return df.to_json(orient='records', force_ascii=False, indent=2)

    def export_custom_fields(self, df: pd.DataFrame, selected_fields: List[str]) -> pd.DataFrame:
        """导出自定义字段"""
        available_fields = [field for field in selected_fields if field in df.columns]
        if available_fields:
            return df[available_fields].copy()
        else:
            return pd.DataFrame()
