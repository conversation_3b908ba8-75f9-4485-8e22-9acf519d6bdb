#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本 - 解决启动问题
"""

import sys
import os
import subprocess
from pathlib import Path

def check_environment():
    """检查环境"""
    print("🔍 检查Python环境...")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 正在使用虚拟环境")
    else:
        print("⚠️ 未使用虚拟环境")

def test_imports():
    """测试关键模块导入"""
    print("\n🧪 测试模块导入...")
    
    modules = [
        'streamlit',
        'pandas', 
        'requests',
        'bs4',
        'plotly',
        'yaml'
    ]
    
    failed = []
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed.append(module)
    
    return len(failed) == 0

def start_streamlit():
    """启动Streamlit应用"""
    print("\n🚀 启动Streamlit应用...")
    
    # 确保目录存在
    Path("data").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    # 检查main.py是否存在
    if not Path("main.py").exists():
        print("❌ main.py文件不存在")
        return False
    
    try:
        print("📱 启动Web界面: http://localhost:8501")
        print("⏹️ 按 Ctrl+C 停止服务")
        print("-" * 50)
        
        # 使用当前Python解释器启动streamlit
        cmd = [sys.executable, "-m", "streamlit", "run", "main.py", 
               "--server.port", "8501", 
               "--server.address", "localhost"]
        
        subprocess.run(cmd)
        return True
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速启动脚本")
    print("=" * 50)
    
    # 检查环境
    check_environment()
    
    # 测试导入
    if not test_imports():
        print("\n❌ 模块导入失败，请检查依赖安装")
        print("尝试运行: pip install -r requirements.txt")
        return
    
    print("\n✅ 环境检查通过")
    
    # 启动应用
    start_streamlit()

if __name__ == "__main__":
    main()
