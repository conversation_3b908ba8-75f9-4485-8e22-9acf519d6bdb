#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本 - 确保所有必需的依赖包都已正确安装
"""

import subprocess
import sys
import importlib

def check_and_install_package(package_name, import_name=None):
    """检查并安装包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                '-i', 'https://mirrors.aliyun.com/pypi/simple/',
                '--trusted-host', 'mirrors.aliyun.com',
                package_name
            ])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False

def main():
    """主函数"""
    print("🔧 检查并安装必需的依赖包...")
    print("=" * 50)
    
    # 必需的包列表
    required_packages = [
        ('streamlit', 'streamlit'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('plotly', 'plotly'),
        ('requests', 'requests'),
        ('pyyaml', 'yaml'),
    ]
    
    success_count = 0
    total_count = len(required_packages)
    
    for package_name, import_name in required_packages:
        if check_and_install_package(package_name, import_name):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 安装结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("🎉 所有必需依赖都已安装完成！")
        print("\n现在可以运行:")
        print("python -m streamlit run main.py")
        return True
    else:
        print("⚠️ 部分依赖安装失败，请检查网络连接或手动安装")
        return False

if __name__ == "__main__":
    main()
