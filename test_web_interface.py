#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面功能测试脚本
测试Streamlit Web界面的启动和基本功能
"""

import sys
import os
import time
import subprocess
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_streamlit_import():
    """测试Streamlit导入"""
    print("🔍 测试Streamlit导入...")
    try:
        import streamlit as st
        print(f"✅ Streamlit版本: {st.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Streamlit导入失败: {e}")
        return False

def test_main_py_syntax():
    """测试main.py语法"""
    print("\n🔍 测试main.py语法...")
    try:
        # 尝试编译main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'main.py', 'exec')
        print("✅ main.py语法检查通过")
        return True
    except SyntaxError as e:
        print(f"❌ main.py语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ main.py检查失败: {e}")
        return False

def test_dependencies():
    """测试Web界面依赖"""
    print("\n🔍 测试Web界面依赖...")
    
    dependencies = [
        ('streamlit', 'streamlit'),
        ('plotly', 'plotly'),
        ('pandas', 'pandas'),
    ]
    
    missing = []
    for package, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"⚠️ 缺少依赖: {', '.join(missing)}")
        return False
    else:
        print("✅ 所有Web界面依赖已安装")
        return True

def test_core_modules():
    """测试核心模块导入"""
    print("\n🔍 测试核心模块导入...")
    
    modules = [
        'src.crawler_engine',
        'src.data_processor', 
        'src.visualizer',
        'src.database_manager',
        'src.region_manager',
        'intelligent_analysis_framework'
    ]
    
    failed = []
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed.append(module)
    
    if failed:
        print(f"⚠️ 模块导入失败: {', '.join(failed)}")
        return False
    else:
        print("✅ 所有核心模块导入成功")
        return True

def test_data_directories():
    """测试数据目录"""
    print("\n🔍 测试数据目录...")
    
    directories = ['data', 'logs']
    for directory in directories:
        path = Path(directory)
        if path.exists():
            print(f"✅ {directory}/ 目录存在")
        else:
            print(f"⚠️ {directory}/ 目录不存在，正在创建...")
            try:
                path.mkdir(exist_ok=True)
                print(f"✅ {directory}/ 目录创建成功")
            except Exception as e:
                print(f"❌ {directory}/ 目录创建失败: {e}")
                return False
    
    return True

def test_config_files():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    
    config_files = [
        'config.yaml',
        'requirements.txt'
    ]
    
    missing = []
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ {config_file}")
        else:
            print(f"❌ {config_file}")
            missing.append(config_file)
    
    if missing:
        print(f"⚠️ 缺少配置文件: {', '.join(missing)}")
        return False
    else:
        print("✅ 所有配置文件存在")
        return True

def simulate_streamlit_functionality():
    """模拟Streamlit功能测试"""
    print("\n🔍 模拟Streamlit功能测试...")
    
    try:
        # 测试数据处理功能
        from src.data_processor import DataProcessor
        from src.database_manager import DatabaseManager
        from src.region_manager import RegionManager
        from intelligent_analysis_framework import IntelligentAnalyzer
        
        print("✅ 核心组件初始化成功")
        
        # 测试区域管理
        region_manager = RegionManager()
        provinces = region_manager.get_all_provinces()
        print(f"✅ 区域管理: 支持 {len(provinces)} 个省份")
        
        # 测试智能分析
        analyzer = IntelligentAnalyzer()
        result = analyzer.analyze_industry_input("酒店建设")
        print(f"✅ 智能分析: 识别行业 '{result['industry_info']['primary_industry']}'")
        
        # 测试数据库
        db_manager = DatabaseManager()
        total_records = db_manager.get_total_records()
        print(f"✅ 数据库: 当前有 {total_records} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Web界面功能测试")
    print("=" * 60)
    
    tests = [
        ("Streamlit导入", test_streamlit_import),
        ("main.py语法", test_main_py_syntax),
        ("Web界面依赖", test_dependencies),
        ("核心模块导入", test_core_modules),
        ("数据目录", test_data_directories),
        ("配置文件", test_config_files),
        ("功能模拟", simulate_streamlit_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📋 Web界面测试结果汇总")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print(f"\n总测试项: {len(results)}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {len(results) - passed}")
    print(f"通过率: {passed/len(results)*100:.1f}%")
    
    if passed >= len(results) * 0.8:
        print("\n🎉 Web界面测试通过！")
        print("\n💡 启动Web界面:")
        print("   方法1: python run.py")
        print("   方法2: streamlit run main.py")
        print("   访问: http://localhost:8501")
        return True
    else:
        print(f"\n⚠️ Web界面存在问题，请检查失败的测试项")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
