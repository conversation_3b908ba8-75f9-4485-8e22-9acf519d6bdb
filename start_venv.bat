@echo off
chcp 65001 >nul
title 智能客户信息收集系统 - 虚拟环境版

echo.
echo 🐍 智能客户信息收集系统 - 虚拟环境版
echo ================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测通过

REM 检查虚拟环境是否存在
if not exist "customer_crawler_env\Scripts\activate.bat" (
    echo.
    echo 🏗️ 虚拟环境不存在，正在创建...
    python -m venv customer_crawler_env
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

REM 激活虚拟环境
echo.
echo 🔄 激活虚拟环境...
call customer_crawler_env\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)

echo ✅ 虚拟环境已激活: customer_crawler_env

REM 检查基础依赖是否安装
echo.
echo 🔍 检查依赖包...
python -c "import requests, bs4, yaml" >nul 2>&1
if errorlevel 1 (
    echo 📦 安装基础依赖包...
    pip install requests beautifulsoup4 pyyaml
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✅ 基础依赖包安装完成
) else (
    echo ✅ 基础依赖包已安装
)

echo.
echo 🎯 选择运行模式:
echo    1. 简化演示 (推荐) - 基础爬虫功能测试
echo    2. 完整演示 - 需要安装更多依赖
echo    3. Web界面 - 需要安装streamlit
echo    4. 环境检查 - 诊断系统状态
echo.

set /p choice="请选择 (1-4, 默认1): "

if "%choice%"=="" set choice=1
if "%choice%"=="2" goto full_demo
if "%choice%"=="3" goto web_interface
if "%choice%"=="4" goto check_status

:simple_demo
echo.
echo 🚀 启动简化演示程序...
python simple_demo.py
goto end

:full_demo
echo.
echo 🚀 启动完整演示程序...
python demo.py
goto end

:web_interface
echo.
echo 📦 检查streamlit是否安装...
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo 正在安装streamlit...
    pip install streamlit plotly
    if errorlevel 1 (
        echo ❌ streamlit安装失败
        pause
        exit /b 1
    )
)
echo 🚀 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:8501
python run.py
goto end

:check_status
echo.
echo 🔍 运行系统状态检查...
python check_status.py
goto end

:end
echo.
echo 💡 提示:
echo    - 虚拟环境路径: %CD%\customer_crawler_env
echo    - 要退出虚拟环境，请输入: deactivate
echo    - 要重新运行，请双击此文件
echo.
pause
