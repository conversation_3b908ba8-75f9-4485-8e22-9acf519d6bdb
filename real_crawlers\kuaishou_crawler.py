#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手真实数据爬虫
"""

import asyncio
import json
from typing import Dict, List, Optional

from base_crawler import BaseCrawler

class Ku<PERSON>houCrawler(BaseCrawler):
    """快手数据爬虫"""
    
    def __init__(self):
        super().__init__("kuaishou")
        self.base_url = "https://www.kuaishou.com"
        
    async def crawl_user_profile(self, user_url: str) -> Dict:
        """爬取用户主页信息"""
        try:
            page = await self.create_page()
            
            print(f"🔍 正在访问快手用户页面: {user_url}")
            await page.goto(user_url, wait_until='networkidle')
            
            # 等待页面加载
            await self.wait_random(3, 5)
            
            # 检查是否遇到反爬
            if await self.check_anti_bot(page):
                if not await self.handle_captcha(page):
                    raise Exception("验证码处理失败")
            
            # 提取用户基本信息
            user_info = await self._extract_user_info(page)
            
            # 滚动加载更多视频
            await self.scroll_page(page, scrolls=5)
            
            # 提取视频列表
            videos = await self._extract_video_list(page)
            
            result = {
                'user_info': user_info,
                'videos': videos,
                'crawl_time': self._get_timestamp(),
                'source_url': user_url,
                'total_videos': len(videos)
            }
            
            # 保存数据
            self.save_data(result, f"kuaishou_user_{user_info.get('username', 'unknown')}")
            
            self.log_result(True, f"成功爬取用户 {user_info.get('username', 'unknown')}", len(videos))
            return result
            
        except Exception as e:
            self.log_result(False, f"用户爬取失败: {str(e)}")
            raise e
        finally:
            await self.close()

    async def _extract_user_info(self, page) -> Dict:
        """提取用户基本信息"""
        user_info = await page.evaluate("""
            () => {
                // 快手用户信息选择器
                const selectors = {
                    username: [
                        '.profile-user-name',
                        '.user-name',
                        '.profile-name',
                        'h1'
                    ],
                    description: [
                        '.profile-user-desc',
                        '.user-desc',
                        '.profile-desc'
                    ],
                    followers: [
                        '.profile-user-info .count:nth-child(2)',
                        '.follower-count',
                        '.fans-count'
                    ],
                    following: [
                        '.profile-user-info .count:nth-child(3)',
                        '.following-count',
                        '.follow-count'
                    ],
                    likes: [
                        '.profile-user-info .count:nth-child(1)',
                        '.like-count',
                        '.zan-count'
                    ],
                    videos_count: [
                        '.profile-user-info .count:nth-child(4)',
                        '.video-count',
                        '.work-count'
                    ]
                };
                
                function getTextBySelectors(selectorList) {
                    for (const selector of selectorList) {
                        const element = document.querySelector(selector);
                        if (element) {
                            return element.textContent.trim();
                        }
                    }
                    return '';
                }
                
                // 获取头像
                const avatarEl = document.querySelector('.profile-avatar img, .user-avatar img, .avatar img');
                
                return {
                    username: getTextBySelectors(selectors.username),
                    description: getTextBySelectors(selectors.description),
                    followers: getTextBySelectors(selectors.followers),
                    following: getTextBySelectors(selectors.following),
                    likes: getTextBySelectors(selectors.likes),
                    videos_count: getTextBySelectors(selectors.videos_count),
                    avatar: avatarEl?.src || ''
                };
            }
        """)
        
        print(f"✅ 用户信息: {user_info.get('username', '未知用户')}")
        return user_info

    async def _extract_video_list(self, page) -> List[Dict]:
        """提取视频列表"""
        videos = await page.evaluate("""
            () => {
                // 快手视频容器选择器
                const videoSelectors = [
                    '.profile-video-list .video-item',
                    '.video-list .video-card',
                    '.works-list .work-item',
                    '.video-feed .feed-item'
                ];
                
                let videoElements = [];
                for (const selector of videoSelectors) {
                    videoElements = document.querySelectorAll(selector);
                    if (videoElements.length > 0) break;
                }
                
                const videos = [];
                
                videoElements.forEach((element, index) => {
                    try {
                        const linkEl = element.querySelector('a');
                        const imgEl = element.querySelector('img');
                        const titleEl = element.querySelector('.video-title, .work-title, .title');
                        const playCountEl = element.querySelector('.play-count, .view-count');
                        const likeCountEl = element.querySelector('.like-count, .zan-count');
                        const durationEl = element.querySelector('.duration, .time');
                        
                        const video = {
                            id: index + 1,
                            title: titleEl?.textContent?.trim() || imgEl?.alt || `视频${index + 1}`,
                            url: linkEl?.href || '',
                            cover: imgEl?.src || '',
                            duration: durationEl?.textContent?.trim() || '',
                            play_count: playCountEl?.textContent?.trim() || '',
                            like_count: likeCountEl?.textContent?.trim() || '',
                            extract_time: new Date().toISOString()
                        };
                        
                        // 只添加有效的视频
                        if (video.url && video.url.includes('kuaishou.com')) {
                            videos.push(video);
                        }
                    } catch (error) {
                        console.warn(`视频 ${index + 1} 提取失败:`, error);
                    }
                });
                
                return videos;
            }
        """)
        
        print(f"✅ 提取到 {len(videos)} 个视频")
        return videos

    async def search_users(self, keyword: str, limit: int = 10) -> List[Dict]:
        """搜索用户"""
        try:
            page = await self.create_page()
            
            search_url = f"{self.base_url}/search/user?keyword={keyword}"
            print(f"🔍 搜索用户: {keyword}")
            
            await page.goto(search_url, wait_until='networkidle')
            await self.wait_random(3, 5)
            
            # 提取搜索结果
            users = await page.evaluate(f"""
                (limit) => {{
                    const userCards = document.querySelectorAll('.search-user-item, .user-card, .user-item');
                    const results = [];
                    
                    for (let i = 0; i < Math.min(userCards.length, limit); i++) {{
                        const card = userCards[i];
                        const nameEl = card.querySelector('.user-name, .username, .name');
                        const linkEl = card.querySelector('a');
                        const avatarEl = card.querySelector('.user-avatar img, .avatar img');
                        const descEl = card.querySelector('.user-desc, .description, .desc');
                        const fansEl = card.querySelector('.fans-count, .follower-count');
                        
                        if (nameEl && linkEl) {{
                            results.push({{
                                name: nameEl.textContent.trim(),
                                url: linkEl.href,
                                avatar: avatarEl?.src || '',
                                description: descEl?.textContent.trim() || '',
                                fans: fansEl?.textContent.trim() || ''
                            }});
                        }}
                    }}
                    
                    return results;
                }}
            """, limit)
            
            self.log_result(True, f"搜索到 {len(users)} 个用户", len(users))
            return users
            
        except Exception as e:
            self.log_result(False, f"用户搜索失败: {str(e)}")
            return []
        finally:
            await self.close()

    async def crawl_video_details(self, video_url: str) -> Dict:
        """爬取视频详细信息"""
        try:
            page = await self.create_page()
            
            print(f"🔍 正在爬取视频详情: {video_url}")
            await page.goto(video_url, wait_until='networkidle')
            await self.wait_random(3, 5)
            
            video_info = await page.evaluate("""
                () => {
                    return {
                        title: document.querySelector('.video-title, .work-title')?.textContent.trim() || '',
                        description: document.querySelector('.video-desc, .work-desc')?.textContent.trim() || '',
                        author: document.querySelector('.author-name, .user-name')?.textContent.trim() || '',
                        play_count: document.querySelector('.play-count, .view-count')?.textContent.trim() || '',
                        like_count: document.querySelector('.like-count, .zan-count')?.textContent.trim() || '',
                        comment_count: document.querySelector('.comment-count')?.textContent.trim() || '',
                        share_count: document.querySelector('.share-count')?.textContent.trim() || '',
                        publish_time: document.querySelector('.publish-time, .time')?.textContent.trim() || '',
                        tags: Array.from(document.querySelectorAll('.tag, .hashtag')).map(el => el.textContent.trim())
                    };
                }
            """)
            
            self.log_result(True, f"成功爬取视频详情: {video_info.get('title', '未知视频')}")
            return video_info
            
        except Exception as e:
            self.log_result(False, f"视频详情爬取失败: {str(e)}")
            return {}
        finally:
            await self.close()

    async def crawl_trending_videos(self, category: str = '', limit: int = 20) -> List[Dict]:
        """爬取热门视频"""
        try:
            page = await self.create_page()
            
            # 构建热门页面URL
            if category:
                trending_url = f"{self.base_url}/trending/{category}"
            else:
                trending_url = f"{self.base_url}/trending"
            
            print(f"🔥 正在爬取热门视频: {category or '全部'}")
            await page.goto(trending_url, wait_until='networkidle')
            await self.wait_random(3, 5)
            
            # 滚动加载更多
            await self.scroll_page(page, scrolls=3)
            
            videos = await page.evaluate(f"""
                (limit) => {{
                    const videoElements = document.querySelectorAll('.trending-item, .hot-video-item, .video-item');
                    const videos = [];
                    
                    for (let i = 0; i < Math.min(videoElements.length, limit); i++) {{
                        const element = videoElements[i];
                        const linkEl = element.querySelector('a');
                        const imgEl = element.querySelector('img');
                        const titleEl = element.querySelector('.title, .video-title');
                        const authorEl = element.querySelector('.author, .user-name');
                        const playCountEl = element.querySelector('.play-count, .view-count');
                        
                        if (linkEl && titleEl) {{
                            videos.push({{
                                rank: i + 1,
                                title: titleEl.textContent.trim(),
                                url: linkEl.href,
                                cover: imgEl?.src || '',
                                author: authorEl?.textContent.trim() || '',
                                play_count: playCountEl?.textContent.trim() || ''
                            }});
                        }}
                    }}
                    
                    return videos;
                }}
            """, limit)
            
            self.log_result(True, f"爬取到 {len(videos)} 个热门视频", len(videos))
            return videos
            
        except Exception as e:
            self.log_result(False, f"热门视频爬取失败: {str(e)}")
            return []
        finally:
            await self.close()

    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

    async def batch_crawl_users(self, user_urls: List[str]) -> List[Dict]:
        """批量爬取用户"""
        results = []
        
        for i, url in enumerate(user_urls):
            print(f"\n📊 进度: {i+1}/{len(user_urls)}")
            
            try:
                result = await self.crawl_user_profile(url)
                results.append({
                    'success': True,
                    'url': url,
                    'data': result
                })
            except Exception as e:
                results.append({
                    'success': False,
                    'url': url,
                    'error': str(e)
                })
            
            # 添加延迟避免被封
            if i < len(user_urls) - 1:
                await asyncio.sleep(5)
        
        return results
