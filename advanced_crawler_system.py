#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
先进爬虫系统
基于最新爬虫原理，支持网页爬取和截屏识别
"""

import sys
import os
import time
import json
import base64
import io
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
import requests
from PIL import Image, ImageEnhance, ImageFilter
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from bs4 import BeautifulSoup
import random
import threading
import queue

# 尝试导入OCR相关库
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("⚠️ OpenCV未安装，部分图像处理功能不可用")

class AdvancedCrawlerSystem:
    """先进爬虫系统"""
    
    def __init__(self):
        """初始化爬虫系统"""
        self.driver = None
        self.session = requests.Session()
        self.setup_session()
        self.setup_browser()
        
        # OCR引擎
        self.ocr_engines = self._setup_ocr_engines()
        
        # 爬虫配置
        self.config = {
            'user_agents': self._load_user_agents(),
            'proxy_list': [],
            'delay_range': (2, 5),
            'retry_count': 3,
            'screenshot_quality': 95,
            'ocr_confidence_threshold': 0.7
        }
        
        print("🚀 先进爬虫系统初始化完成")
        print("✅ 支持动态网页爬取")
        print("✅ 支持截屏识别")
        print("✅ 支持反反爬虫机制")
        print("✅ 支持多种OCR引擎")
    
    def setup_session(self):
        """设置HTTP会话"""
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        }
        self.session.headers.update(headers)
        self.session.timeout = 30
    
    def setup_browser(self):
        """设置浏览器（支持最新反检测技术）"""
        try:
            chrome_options = Options()
            
            # 反检测设置
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 性能优化
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # 可选：禁用图片加载
            
            # 窗口设置
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--start-maximized')
            
            # 随机User-Agent
            user_agent = random.choice(self.config['user_agents'])
            chrome_options.add_argument(f'--user-agent={user_agent}')
            
            # 其他反检测设置
            prefs = {
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.images": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en']})")
            
            print("✅ 高级浏览器初始化成功")
            
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            print("💡 请确保已安装Chrome浏览器和ChromeDriver")
            self.driver = None
    
    def _load_user_agents(self) -> List[str]:
        """加载User-Agent列表"""
        return [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]
    
    def _setup_ocr_engines(self) -> Dict[str, Any]:
        """设置OCR引擎"""
        engines = {}
        
        # 尝试加载EasyOCR
        try:
            import easyocr
            engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            print("✅ EasyOCR引擎加载成功")
        except ImportError:
            print("⚠️ EasyOCR未安装，使用pip install easyocr安装")
        
        # 尝试加载PaddleOCR
        try:
            from paddleocr import PaddleOCR
            engines['paddleocr'] = PaddleOCR(use_angle_cls=True, lang='ch')
            print("✅ PaddleOCR引擎加载成功")
        except ImportError:
            print("⚠️ PaddleOCR未安装，使用pip install paddlepaddle paddleocr安装")
        
        # 尝试加载Tesseract
        try:
            import pytesseract
            engines['tesseract'] = pytesseract
            print("✅ Tesseract引擎加载成功")
        except ImportError:
            print("⚠️ Tesseract未安装")
        
        return engines
    
    def smart_navigate(self, url: str, wait_for_element: str = None) -> bool:
        """智能导航到页面"""
        if not self.driver:
            print("❌ 浏览器未初始化")
            return False
        
        try:
            print(f"🌐 智能导航到: {url}")
            
            # 随机延迟
            time.sleep(random.uniform(*self.config['delay_range']))
            
            # 访问页面
            self.driver.get(url)
            
            # 模拟人类行为
            self._simulate_human_behavior()
            
            # 等待特定元素（如果指定）
            if wait_for_element:
                try:
                    WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, wait_for_element))
                    )
                    print(f"✅ 等待元素加载完成: {wait_for_element}")
                except:
                    print(f"⚠️ 等待元素超时: {wait_for_element}")
            
            # 等待页面完全加载
            WebDriverWait(self.driver, 15).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            print("✅ 页面加载完成")
            return True
            
        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False
    
    def _simulate_human_behavior(self):
        """模拟人类行为"""
        try:
            # 随机滚动
            scroll_count = random.randint(1, 3)
            for _ in range(scroll_count):
                scroll_y = random.randint(100, 500)
                self.driver.execute_script(f"window.scrollBy(0, {scroll_y});")
                time.sleep(random.uniform(0.5, 1.5))
            
            # 随机鼠标移动
            actions = ActionChains(self.driver)
            for _ in range(random.randint(1, 2)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                actions.move_by_offset(x, y)
                time.sleep(random.uniform(0.2, 0.8))
            actions.perform()
            
        except Exception as e:
            print(f"⚠️ 模拟人类行为失败: {e}")
    
    def take_smart_screenshot(self, element_selector: str = None) -> Optional[Image.Image]:
        """智能截屏"""
        if not self.driver:
            print("❌ 浏览器未初始化")
            return None
        
        try:
            print("📸 正在进行智能截屏...")
            
            # 等待页面稳定
            time.sleep(2)
            
            if element_selector:
                # 截取特定元素
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, element_selector)
                    screenshot_data = element.screenshot_as_png
                    print(f"✅ 元素截屏成功: {element_selector}")
                except:
                    print(f"⚠️ 元素截屏失败，使用全屏截屏: {element_selector}")
                    screenshot_data = self.driver.get_screenshot_as_png()
            else:
                # 全屏截屏
                screenshot_data = self.driver.get_screenshot_as_png()
                print("✅ 全屏截屏成功")
            
            # 转换为PIL图像
            image = Image.open(io.BytesIO(screenshot_data))
            
            # 图像优化
            optimized_image = self._optimize_screenshot(image)
            
            return optimized_image
            
        except Exception as e:
            print(f"❌ 截屏失败: {e}")
            return None
    
    def _optimize_screenshot(self, image: Image.Image) -> Image.Image:
        """优化截屏图像"""
        try:
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.2)
            
            # 增强锐度
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            # 调整亮度
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(1.1)
            
            return image
            
        except Exception as e:
            print(f"⚠️ 图像优化失败: {e}")
            return image
    
    def extract_text_with_ocr(self, image: Image.Image, engine: str = 'auto') -> List[Dict[str, Any]]:
        """使用OCR提取文本"""
        if not image:
            return []
        
        print(f"🔍 使用OCR提取文本 (引擎: {engine})")
        
        results = []
        
        if engine == 'auto':
            # 自动选择最佳引擎
            for engine_name in ['paddleocr', 'easyocr', 'tesseract']:
                if engine_name in self.ocr_engines:
                    engine_results = self._extract_with_engine(image, engine_name)
                    results.extend(engine_results)
                    break
        else:
            # 使用指定引擎
            if engine in self.ocr_engines:
                results = self._extract_with_engine(image, engine)
            else:
                print(f"❌ OCR引擎不可用: {engine}")
        
        # 过滤和去重
        filtered_results = self._filter_ocr_results(results)
        
        print(f"✅ OCR提取完成，获得 {len(filtered_results)} 条有效文本")
        return filtered_results
    
    def _extract_with_engine(self, image: Image.Image, engine: str) -> List[Dict[str, Any]]:
        """使用特定引擎提取文本"""
        results = []
        
        try:
            if engine == 'easyocr':
                reader = self.ocr_engines['easyocr']
                img_array = np.array(image)
                ocr_results = reader.readtext(img_array)
                
                for (bbox, text, confidence) in ocr_results:
                    if confidence > self.config['ocr_confidence_threshold']:
                        results.append({
                            'text': text.strip(),
                            'confidence': confidence,
                            'bbox': bbox,
                            'engine': 'easyocr'
                        })
            
            elif engine == 'paddleocr':
                ocr = self.ocr_engines['paddleocr']
                img_array = np.array(image)
                ocr_results = ocr.ocr(img_array, cls=True)
                
                if ocr_results and ocr_results[0]:
                    for line in ocr_results[0]:
                        if line:
                            bbox, (text, confidence) = line
                            if confidence > self.config['ocr_confidence_threshold']:
                                results.append({
                                    'text': text.strip(),
                                    'confidence': confidence,
                                    'bbox': bbox,
                                    'engine': 'paddleocr'
                                })
            
            elif engine == 'tesseract':
                import pytesseract
                text = pytesseract.image_to_string(image, lang='chi_sim+eng')
                if text.strip():
                    results.append({
                        'text': text.strip(),
                        'confidence': 0.8,
                        'bbox': None,
                        'engine': 'tesseract'
                    })
            
        except Exception as e:
            print(f"❌ {engine} 引擎提取失败: {e}")
        
        return results
    
    def _filter_ocr_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤OCR结果"""
        filtered = []
        seen_texts = set()
        
        for result in results:
            text = result['text']
            
            # 基本过滤
            if len(text) < 2 or text in seen_texts:
                continue
            
            # 过滤无意义文本
            if re.match(r'^[^\u4e00-\u9fa5a-zA-Z0-9]+$', text):
                continue
            
            seen_texts.add(text)
            filtered.append(result)
        
        return filtered
    
    def extract_structured_data(self, url: str, selectors: Dict[str, str], 
                              use_screenshot: bool = False) -> Dict[str, Any]:
        """提取结构化数据"""
        print(f"📊 提取结构化数据: {url}")
        
        result = {
            'url': url,
            'extraction_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'method': 'screenshot_ocr' if use_screenshot else 'dom_parsing',
            'data': {},
            'success': False
        }
        
        # 导航到页面
        if not self.smart_navigate(url):
            result['error'] = '页面导航失败'
            return result
        
        try:
            if use_screenshot:
                # 使用截屏+OCR方式
                screenshot = self.take_smart_screenshot()
                if screenshot:
                    ocr_results = self.extract_text_with_ocr(screenshot)
                    result['data'] = self._match_selectors_with_ocr(ocr_results, selectors)
                    result['ocr_results'] = ocr_results
            else:
                # 使用DOM解析方式
                result['data'] = self._extract_with_selectors(selectors)
            
            result['success'] = True
            print("✅ 结构化数据提取成功")
            
        except Exception as e:
            result['error'] = str(e)
            print(f"❌ 结构化数据提取失败: {e}")
        
        return result
    
    def _extract_with_selectors(self, selectors: Dict[str, str]) -> Dict[str, Any]:
        """使用CSS选择器提取数据"""
        data = {}
        
        for field_name, selector in selectors.items():
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    if len(elements) == 1:
                        data[field_name] = elements[0].text.strip()
                    else:
                        data[field_name] = [elem.text.strip() for elem in elements]
                else:
                    data[field_name] = None
                    
            except Exception as e:
                print(f"⚠️ 选择器提取失败 {field_name}: {e}")
                data[field_name] = None
        
        return data
    
    def _match_selectors_with_ocr(self, ocr_results: List[Dict[str, Any]], 
                                 selectors: Dict[str, str]) -> Dict[str, Any]:
        """将OCR结果与选择器匹配"""
        data = {}
        
        # 这里可以实现更复杂的匹配逻辑
        # 目前简单地将所有OCR文本合并
        all_text = ' '.join([result['text'] for result in ocr_results])
        
        for field_name, pattern in selectors.items():
            # 使用正则表达式匹配
            if isinstance(pattern, str) and pattern.startswith('regex:'):
                regex_pattern = pattern[6:]  # 移除'regex:'前缀
                match = re.search(regex_pattern, all_text)
                data[field_name] = match.group(1) if match else None
            else:
                # 简单文本匹配
                data[field_name] = all_text if pattern in all_text else None
        
        return data
    
    def save_screenshot(self, image: Image.Image, filename: str = None) -> str:
        """保存截屏"""
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
        
        try:
            image.save(filename, 'PNG', quality=self.config['screenshot_quality'])
            print(f"✅ 截屏已保存: {filename}")
            return filename
        except Exception as e:
            print(f"❌ 截屏保存失败: {e}")
            return ""
    
    def close(self):
        """关闭爬虫系统"""
        if self.driver:
            self.driver.quit()
            print("✅ 爬虫系统已关闭")

def main():
    """主函数 - 演示先进爬虫功能"""
    crawler = AdvancedCrawlerSystem()
    
    try:
        # 测试网站
        test_url = "https://example.com"
        
        print(f"\n{'='*80}")
        print(f"🧪 测试先进爬虫系统")
        print(f"{'='*80}")
        
        # 测试1: 基本截屏和OCR
        print("\n📸 测试1: 智能截屏和OCR识别")
        if crawler.smart_navigate(test_url):
            screenshot = crawler.take_smart_screenshot()
            if screenshot:
                # 保存截屏
                screenshot_file = crawler.save_screenshot(screenshot)
                
                # OCR识别
                ocr_results = crawler.extract_text_with_ocr(screenshot)
                print(f"OCR识别结果: {len(ocr_results)} 条文本")
                for result in ocr_results[:3]:
                    print(f"  - {result['text']} (置信度: {result['confidence']:.2f})")
        
        # 测试2: 结构化数据提取
        print("\n📊 测试2: 结构化数据提取")
        selectors = {
            'title': 'h1',
            'content': 'p',
            'links': 'a'
        }
        
        # DOM解析方式
        dom_result = crawler.extract_structured_data(test_url, selectors, use_screenshot=False)
        print(f"DOM解析结果: {dom_result['success']}")
        
        # 截屏OCR方式
        ocr_result = crawler.extract_structured_data(test_url, selectors, use_screenshot=True)
        print(f"截屏OCR结果: {ocr_result['success']}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    finally:
        crawler.close()

if __name__ == "__main__":
    main()
