#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人工行为模拟功能测试脚本
验证鼠标移动、点击、滚动、输入等人工行为模拟功能
"""

import asyncio
import sys
import logging
import time
import random
from src.behavior_simulator import BehaviorSimulator, get_behavior_simulator
from src.browser_manager import get_browser_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_mouse_movement():
    """测试鼠标移动功能"""
    print("🔍 测试鼠标移动功能...")
    
    try:
        behavior_simulator = BehaviorSimulator()
        
        # 测试贝塞尔曲线路径生成
        path = behavior_simulator._generate_bezier_path(100, 100, 500, 400)
        
        print(f"✅ 贝塞尔路径生成成功:")
        print(f"   路径点数: {len(path.points)}")
        print(f"   移动时长: {path.duration:.2f}s")
        print(f"   起始点: {path.points[0]}")
        print(f"   结束点: {path.points[-1]}")
        
        # 验证路径合理性
        if len(path.points) >= 10 and path.duration > 0:
            print("✅ 路径参数合理")
            return True
        else:
            print("⚠️ 路径参数异常")
            return False
        
    except Exception as e:
        print(f"❌ 鼠标移动测试失败: {e}")
        return False

async def test_behavior_parameters():
    """测试行为参数配置"""
    print("🔍 测试行为参数配置...")
    
    try:
        behavior_simulator = BehaviorSimulator()
        
        # 检查参数范围
        params = {
            "mouse_speed_range": behavior_simulator.mouse_speed_range,
            "click_delay_range": behavior_simulator.click_delay_range,
            "scroll_delay_range": behavior_simulator.scroll_delay_range,
            "typing_speed_range": behavior_simulator.typing_speed_range,
            "pause_probability": behavior_simulator.pause_probability,
            "pause_duration_range": behavior_simulator.pause_duration_range
        }
        
        print("✅ 行为参数配置:")
        for name, value in params.items():
            print(f"   {name}: {value}")
        
        # 验证参数合理性
        valid = True
        if not (50 <= behavior_simulator.mouse_speed_range[0] <= behavior_simulator.mouse_speed_range[1] <= 500):
            print("⚠️ 鼠标速度范围异常")
            valid = False
        
        if not (0 < behavior_simulator.pause_probability <= 1):
            print("⚠️ 暂停概率异常")
            valid = False
        
        if valid:
            print("✅ 所有参数配置合理")
        
        return valid
        
    except Exception as e:
        print(f"❌ 参数配置测试失败: {e}")
        return False

async def test_browser_integration():
    """测试浏览器集成"""
    print("🔍 测试浏览器集成...")
    
    try:
        # 创建浏览器管理器
        browser_manager = get_browser_manager(headless=True)
        await browser_manager.start()
        
        # 创建浏览器实例和上下文
        browser = await browser_manager.get_or_create_browser()
        context = await browser_manager.create_context(browser)
        page = await browser_manager.create_page(context)
        
        print("✅ 浏览器环境创建成功")
        
        # 测试人性化导航
        success = await browser_manager.human_like_navigate(page, "https://httpbin.org/html")
        if success:
            print("✅ 人性化导航成功")
        else:
            print("⚠️ 人性化导航失败")
        
        # 测试行为模拟器方法
        behavior_simulator = get_behavior_simulator()
        
        # 测试滚动
        await behavior_simulator.human_like_scroll(page, "down", 200)
        print("✅ 滚动模拟完成")
        
        # 测试随机鼠标移动
        await behavior_simulator.random_mouse_movement(page, 1.0)
        print("✅ 随机鼠标移动完成")
        
        # 测试阅读行为
        await behavior_simulator.simulate_reading_behavior(page, 2.0)
        print("✅ 阅读行为模拟完成")
        
        # 清理
        await page.close()
        await browser_manager.close_context(context)
        
        from src.browser_manager import cleanup_browser_manager
        await cleanup_browser_manager()
        
        print("✅ 浏览器集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 浏览器集成测试失败: {e}")
        return False

async def test_search_simulation():
    """测试搜索模拟"""
    print("🔍 测试搜索模拟...")
    
    try:
        # 创建浏览器管理器
        browser_manager = get_browser_manager(headless=True)
        await browser_manager.start()
        
        # 创建浏览器实例和上下文
        browser = await browser_manager.get_or_create_browser()
        context = await browser_manager.create_context(browser)
        page = await browser_manager.create_page(context)
        
        # 导航到搜索页面
        await page.goto("https://httpbin.org/forms/post", timeout=15000)
        
        # 测试人性化搜索（使用表单输入）
        success = await browser_manager.human_like_search(
            page, 
            'input[name="custname"]', 
            "测试公司名称"
        )
        
        if success:
            print("✅ 搜索模拟成功")
        else:
            print("⚠️ 搜索模拟失败")
        
        # 测试浏览会话模拟
        await browser_manager.simulate_browsing_session(page, 3.0)
        print("✅ 浏览会话模拟完成")
        
        # 清理
        await page.close()
        await browser_manager.close_context(context)
        
        from src.browser_manager import cleanup_browser_manager
        await cleanup_browser_manager()
        
        return success
        
    except Exception as e:
        print(f"❌ 搜索模拟测试失败: {e}")
        return False

async def test_timing_behavior():
    """测试时间行为"""
    print("🔍 测试时间行为...")
    
    try:
        behavior_simulator = BehaviorSimulator()
        
        # 测试各种延迟的合理性
        test_cases = [
            ("点击延迟", behavior_simulator.click_delay_range),
            ("滚动延迟", behavior_simulator.scroll_delay_range),
            ("暂停时长", behavior_simulator.pause_duration_range)
        ]
        
        for name, delay_range in test_cases:
            # 生成多个随机值并检查分布
            import random
            values = [random.uniform(*delay_range) for _ in range(100)]
            
            min_val = min(values)
            max_val = max(values)
            avg_val = sum(values) / len(values)
            
            print(f"✅ {name}:")
            print(f"   范围: {delay_range}")
            print(f"   实际: {min_val:.3f} - {max_val:.3f} (平均: {avg_val:.3f})")
            
            # 验证值在合理范围内
            if not (delay_range[0] <= min_val and max_val <= delay_range[1]):
                print(f"⚠️ {name}值超出范围")
                return False
        
        print("✅ 所有时间行为参数正常")
        return True
        
    except Exception as e:
        print(f"❌ 时间行为测试失败: {e}")
        return False

async def test_performance():
    """测试性能"""
    print("🔍 测试性能...")
    
    try:
        behavior_simulator = BehaviorSimulator()
        
        # 测试路径生成性能
        start_time = time.time()
        for i in range(100):
            path = behavior_simulator._generate_bezier_path(
                random.uniform(0, 1000), 
                random.uniform(0, 1000),
                random.uniform(0, 1000), 
                random.uniform(0, 1000)
            )
        generation_time = time.time() - start_time
        
        print(f"✅ 路径生成性能:")
        print(f"   100次生成耗时: {generation_time:.3f}s")
        print(f"   平均每次: {generation_time/100*1000:.1f}ms")
        
        # 性能应该足够快
        if generation_time < 1.0:  # 100次生成应该在1秒内完成
            print("✅ 性能表现良好")
            return True
        else:
            print("⚠️ 性能可能需要优化")
            return False
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("人工行为模拟功能测试")
    print("=" * 60)
    
    async def run_all_tests():
        # 测试鼠标移动
        test1 = await test_mouse_movement()
        
        # 测试行为参数
        test2 = await test_behavior_parameters()
        
        # 测试浏览器集成
        test3 = await test_browser_integration()
        
        # 测试搜索模拟
        test4 = await test_search_simulation()
        
        # 测试时间行为
        test5 = await test_timing_behavior()
        
        # 测试性能
        test6 = await test_performance()
        
        return test1, test2, test3, test4, test5, test6
    
    # 运行所有测试
    results = asyncio.run(run_all_tests())
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 5:  # 至少5个测试通过
        print("✅ 人工行为模拟功能基本正常")
        print("💡 建议：可以在实际爬虫中测试反检测效果")
        sys.exit(0)
    else:
        print("❌ 人工行为模拟功能测试失败，需要检查实现")
        sys.exit(1)

if __name__ == "__main__":
    main()
