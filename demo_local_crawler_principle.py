#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地数据爬虫原理演示
详细展示本地数据爬虫的工作机制和实现原理
"""

import sys
import os
import time
import random
import re
from pathlib import Path
from typing import Dict, List, Any
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class LocalCrawlerPrincipleDemo:
    """本地数据爬虫原理演示类"""
    
    def __init__(self):
        """初始化演示系统"""
        self.industry_templates = self._load_industry_templates()
        self.region_data = self._load_region_data()
        self.name_patterns = self._load_name_patterns()
        self.contact_patterns = self._load_contact_patterns()
        
        print("🚀 本地数据爬虫原理演示系统初始化完成")
    
    def _load_industry_templates(self) -> Dict[str, Dict]:
        """加载行业模板数据"""
        return {
            "建筑工程": {
                "keywords": ["建筑", "工程", "施工", "建设", "装修", "装饰"],
                "business_scope": [
                    "建筑工程施工", "装修装饰工程", "市政工程", 
                    "园林绿化工程", "钢结构工程", "防水工程"
                ],
                "qualifications": [
                    "建筑工程施工总承包壹级", "装修装饰工程专业承包壹级",
                    "市政公用工程施工总承包贰级", "园林绿化工程专业承包壹级"
                ],
                "typical_capital": ["1000万", "3000万", "5000万", "8000万", "1亿"],
                "company_types": ["有限公司", "股份有限公司", "集团有限公司"]
            },
            "酒店餐饮": {
                "keywords": ["酒店", "宾馆", "餐厅", "饭店", "餐饮", "住宿"],
                "business_scope": [
                    "住宿服务", "餐饮服务", "会议服务", 
                    "娱乐服务", "旅游服务", "食品销售"
                ],
                "qualifications": [
                    "食品经营许可证", "卫生许可证", "消防安全检查合格证",
                    "特种行业许可证", "酒类零售许可证"
                ],
                "typical_capital": ["500万", "1000万", "2000万", "5000万"],
                "company_types": ["有限公司", "管理有限公司", "集团有限公司"]
            },
            "房地产开发": {
                "keywords": ["房地产", "地产", "开发", "置业", "物业"],
                "business_scope": [
                    "房地产开发经营", "物业管理", "房地产咨询",
                    "建筑材料销售", "装饰装修", "园林绿化"
                ],
                "qualifications": [
                    "房地产开发企业资质证书", "物业服务企业资质证书",
                    "建筑业企业资质证书", "土地使用权证"
                ],
                "typical_capital": ["5000万", "1亿", "2亿", "5亿", "10亿"],
                "company_types": ["开发有限公司", "置业有限公司", "集团有限公司"]
            }
        }
    
    def _load_region_data(self) -> Dict[str, Dict]:
        """加载地区数据"""
        return {
            "北京": {
                "districts": ["朝阳区", "海淀区", "西城区", "东城区", "丰台区", "石景山区"],
                "economic_zones": ["中关村", "CBD", "金融街", "亦庄开发区"],
                "phone_prefix": ["010"],
                "postal_codes": ["100000", "100001", "100010", "100020"]
            },
            "上海": {
                "districts": ["浦东新区", "黄浦区", "徐汇区", "长宁区", "静安区", "普陀区"],
                "economic_zones": ["陆家嘴", "张江", "虹桥", "外滩"],
                "phone_prefix": ["021"],
                "postal_codes": ["200000", "200001", "200010", "200020"]
            },
            "广州": {
                "districts": ["天河区", "越秀区", "海珠区", "荔湾区", "白云区", "黄埔区"],
                "economic_zones": ["珠江新城", "琶洲", "白云新城", "黄埔开发区"],
                "phone_prefix": ["020"],
                "postal_codes": ["510000", "510001", "510010", "510020"]
            },
            "深圳": {
                "districts": ["南山区", "福田区", "罗湖区", "宝安区", "龙岗区", "盐田区"],
                "economic_zones": ["前海", "后海", "科技园", "华强北"],
                "phone_prefix": ["0755"],
                "postal_codes": ["518000", "518001", "518010", "518020"]
            }
        }
    
    def _load_name_patterns(self) -> List[str]:
        """加载企业名称模式"""
        return [
            "{region}{industry}{type}",
            "{region}市{industry}{type}",
            "{region}{industry}建设{type}",
            "{region}{industry}发展{type}",
            "{region}{industry}管理{type}",
            "{region}{industry}服务{type}",
            "{region}{industry}工程{type}",
            "{region}{industry}科技{type}"
        ]
    
    def _load_contact_patterns(self) -> Dict[str, List]:
        """加载联系方式模式"""
        return {
            "phone_patterns": [
                "{prefix}-{number}",
                "{prefix} {number}",
                "{prefix}{number}"
            ],
            "email_patterns": [
                "info@{domain}.com",
                "contact@{domain}.com",
                "service@{domain}.com",
                "{name}@{domain}.com"
            ],
            "address_patterns": [
                "{region}市{district}{street}{number}号",
                "{region}市{district}{street}{number}号{building}",
                "{region}市{district}{zone}{street}{number}号"
            ]
        }
    
    def demonstrate_name_generation(self, industry: str, region: str, count: int = 5) -> List[Dict]:
        """演示企业名称生成过程"""
        print(f"\n🏭 演示企业名称生成过程")
        print(f"目标行业: {industry}")
        print(f"目标地区: {region}")
        print("=" * 60)
        
        if industry not in self.industry_templates:
            print(f"❌ 不支持的行业: {industry}")
            return []
        
        if region not in self.region_data:
            print(f"❌ 不支持的地区: {region}")
            return []
        
        industry_data = self.industry_templates[industry]
        region_data = self.region_data[region]
        
        generated_names = []
        
        print(f"📋 行业关键词: {', '.join(industry_data['keywords'])}")
        print(f"🏢 企业类型: {', '.join(industry_data['company_types'])}")
        print(f"📍 地区信息: {region} - {', '.join(region_data['districts'][:3])}")
        
        for i in range(count):
            print(f"\n🔄 生成第 {i+1} 个企业名称:")
            
            # 选择名称模式
            pattern = random.choice(self.name_patterns)
            print(f"  1. 选择名称模式: {pattern}")
            
            # 选择行业关键词
            industry_keyword = random.choice(industry_data['keywords'])
            print(f"  2. 选择行业关键词: {industry_keyword}")
            
            # 选择企业类型
            company_type = random.choice(industry_data['company_types'])
            print(f"  3. 选择企业类型: {company_type}")
            
            # 生成企业名称
            company_name = pattern.format(
                region=region,
                industry=industry_keyword,
                type=company_type
            )
            
            print(f"  4. 生成企业名称: {company_name}")
            
            # 验证名称合理性
            is_valid = self._validate_company_name(company_name)
            print(f"  5. 名称验证结果: {'✅ 通过' if is_valid else '❌ 不通过'}")
            
            if is_valid:
                generated_names.append({
                    'name': company_name,
                    'pattern': pattern,
                    'industry_keyword': industry_keyword,
                    'company_type': company_type,
                    'generation_method': 'template_based'
                })
        
        print(f"\n✅ 成功生成 {len(generated_names)} 个有效企业名称")
        return generated_names
    
    def demonstrate_contact_generation(self, company_name: str, region: str) -> Dict[str, Any]:
        """演示联系信息生成过程"""
        print(f"\n📞 演示联系信息生成过程")
        print(f"企业名称: {company_name}")
        print(f"所在地区: {region}")
        print("=" * 60)
        
        region_data = self.region_data.get(region, self.region_data['北京'])
        contact_info = {}
        
        # 1. 生成电话号码
        print(f"🔄 生成电话号码:")
        phone_prefix = random.choice(region_data['phone_prefix'])
        phone_number = f"{random.randint(10000000, 99999999)}"
        phone = f"{phone_prefix}-{phone_number}"
        contact_info['phone'] = phone
        print(f"  地区前缀: {phone_prefix}")
        print(f"  随机号码: {phone_number}")
        print(f"  完整电话: {phone}")
        
        # 2. 生成邮箱地址
        print(f"\n📧 生成邮箱地址:")
        # 从企业名称提取域名
        domain_name = self._extract_domain_from_name(company_name)
        email_pattern = random.choice(self.contact_patterns['email_patterns'])
        email = email_pattern.format(domain=domain_name, name='info')
        contact_info['email'] = email
        print(f"  域名提取: {domain_name}")
        print(f"  邮箱模式: {email_pattern}")
        print(f"  完整邮箱: {email}")
        
        # 3. 生成地址信息
        print(f"\n🏠 生成地址信息:")
        district = random.choice(region_data['districts'])
        street = f"{random.choice(['建国', '中山', '人民', '解放', '和平'])}路"
        number = random.randint(1, 999)
        address = f"{region}市{district}{street}{number}号"
        contact_info['address'] = address
        print(f"  选择区域: {district}")
        print(f"  生成街道: {street}")
        print(f"  门牌号码: {number}号")
        print(f"  完整地址: {address}")
        
        # 4. 生成网站信息
        print(f"\n🌐 生成网站信息:")
        website = f"www.{domain_name}.com"
        contact_info['website'] = website
        print(f"  网站地址: {website}")
        
        return contact_info
    
    def demonstrate_business_scope_generation(self, industry: str) -> List[str]:
        """演示经营范围生成过程"""
        print(f"\n📋 演示经营范围生成过程")
        print(f"目标行业: {industry}")
        print("=" * 60)
        
        if industry not in self.industry_templates:
            print(f"❌ 不支持的行业: {industry}")
            return []
        
        industry_data = self.industry_templates[industry]
        base_scopes = industry_data['business_scope']
        
        print(f"📝 基础经营范围模板:")
        for i, scope in enumerate(base_scopes, 1):
            print(f"  {i}. {scope}")
        
        # 随机选择3-5个经营范围
        selected_count = random.randint(3, min(5, len(base_scopes)))
        selected_scopes = random.sample(base_scopes, selected_count)
        
        print(f"\n🎯 随机选择 {selected_count} 个经营范围:")
        for i, scope in enumerate(selected_scopes, 1):
            print(f"  {i}. {scope}")
        
        # 添加通用经营范围
        common_scopes = [
            "企业管理咨询", "商务信息咨询", "技术开发", 
            "技术转让", "技术咨询", "技术服务"
        ]
        
        additional_scope = random.choice(common_scopes)
        selected_scopes.append(additional_scope)
        
        print(f"\n➕ 添加通用经营范围: {additional_scope}")
        
        final_scope = "; ".join(selected_scopes)
        print(f"\n✅ 最终经营范围: {final_scope}")
        
        return selected_scopes
    
    def demonstrate_data_validation(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """演示数据验证过程"""
        print(f"\n🔍 演示数据验证过程")
        print("=" * 60)
        
        validation_results = {}
        
        # 1. 企业名称验证
        print(f"📝 企业名称验证:")
        name_valid = self._validate_company_name(company_data.get('name', ''))
        validation_results['name_valid'] = name_valid
        print(f"  企业名称: {company_data.get('name', 'N/A')}")
        print(f"  验证结果: {'✅ 通过' if name_valid else '❌ 不通过'}")
        
        # 2. 联系信息验证
        print(f"\n📞 联系信息验证:")
        phone_valid = self._validate_phone(company_data.get('phone', ''))
        email_valid = self._validate_email(company_data.get('email', ''))
        validation_results['phone_valid'] = phone_valid
        validation_results['email_valid'] = email_valid
        print(f"  电话号码: {company_data.get('phone', 'N/A')} - {'✅' if phone_valid else '❌'}")
        print(f"  邮箱地址: {company_data.get('email', 'N/A')} - {'✅' if email_valid else '❌'}")
        
        # 3. 地址信息验证
        print(f"\n🏠 地址信息验证:")
        address_valid = self._validate_address(company_data.get('address', ''))
        validation_results['address_valid'] = address_valid
        print(f"  地址信息: {company_data.get('address', 'N/A')}")
        print(f"  验证结果: {'✅ 通过' if address_valid else '❌ 不通过'}")
        
        # 4. 整体一致性验证
        print(f"\n🔄 整体一致性验证:")
        consistency_valid = self._validate_consistency(company_data)
        validation_results['consistency_valid'] = consistency_valid
        print(f"  一致性检查: {'✅ 通过' if consistency_valid else '❌ 不通过'}")
        
        # 计算总体验证分数
        total_checks = len(validation_results)
        passed_checks = sum(validation_results.values())
        validation_score = passed_checks / total_checks
        
        print(f"\n📊 验证总结:")
        print(f"  总检查项: {total_checks}")
        print(f"  通过项目: {passed_checks}")
        print(f"  验证分数: {validation_score:.2f} ({validation_score*100:.1f}%)")
        
        validation_results['total_score'] = validation_score
        return validation_results
    
    def _validate_company_name(self, name: str) -> bool:
        """验证企业名称"""
        if not name or len(name) < 6 or len(name) > 50:
            return False
        
        # 检查是否包含公司类型
        company_types = ["有限公司", "股份有限公司", "集团", "公司"]
        has_company_type = any(ct in name for ct in company_types)
        
        # 检查是否包含中文字符
        has_chinese = bool(re.search(r'[\u4e00-\u9fa5]', name))
        
        return has_company_type and has_chinese
    
    def _validate_phone(self, phone: str) -> bool:
        """验证电话号码"""
        if not phone:
            return False
        
        # 检查固定电话格式
        landline_pattern = r'^0\d{2,3}-?\d{7,8}$'
        # 检查手机号码格式
        mobile_pattern = r'^1[3-9]\d{9}$'
        
        return bool(re.match(landline_pattern, phone) or re.match(mobile_pattern, phone))
    
    def _validate_email(self, email: str) -> bool:
        """验证邮箱地址"""
        if not email:
            return False
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(email_pattern, email))
    
    def _validate_address(self, address: str) -> bool:
        """验证地址信息"""
        if not address or len(address) < 10:
            return False
        
        # 检查是否包含省市区信息
        location_keywords = ['市', '区', '县', '路', '街', '号']
        has_location = any(keyword in address for keyword in location_keywords)
        
        return has_location
    
    def _validate_consistency(self, company_data: Dict[str, Any]) -> bool:
        """验证数据一致性"""
        # 检查地区一致性
        name = company_data.get('name', '')
        address = company_data.get('address', '')
        
        # 提取名称中的地区信息
        name_regions = []
        for region in self.region_data.keys():
            if region in name:
                name_regions.append(region)
        
        # 检查地址中是否包含相同地区
        address_consistent = any(region in address for region in name_regions) if name_regions else True
        
        return address_consistent
    
    def _extract_domain_from_name(self, company_name: str) -> str:
        """从企业名称提取域名"""
        # 移除公司类型后缀
        name = company_name.replace('有限公司', '').replace('股份有限公司', '').replace('集团', '')
        
        # 提取核心词汇
        core_words = []
        for word in ['建筑', '工程', '酒店', '餐饮', '房地产', '开发', '科技', '管理']:
            if word in name:
                core_words.append(word)
        
        if core_words:
            return ''.join(core_words)
        else:
            # 使用拼音首字母
            return 'company'

def main():
    """主演示函数"""
    demo = LocalCrawlerPrincipleDemo()
    
    print("🔍 本地数据爬虫原理详细演示")
    print("=" * 80)
    
    # 演示场景
    test_scenarios = [
        {"industry": "建筑工程", "region": "北京"},
        {"industry": "酒店餐饮", "region": "上海"},
        {"industry": "房地产开发", "region": "深圳"}
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{'='*80}")
        print(f"🎯 演示场景 {i}: {scenario['industry']} - {scenario['region']}")
        print(f"{'='*80}")
        
        # 1. 演示企业名称生成
        generated_names = demo.demonstrate_name_generation(
            scenario['industry'], 
            scenario['region'], 
            count=3
        )
        
        if generated_names:
            # 选择第一个生成的企业进行后续演示
            selected_company = generated_names[0]
            company_name = selected_company['name']
            
            # 2. 演示联系信息生成
            contact_info = demo.demonstrate_contact_generation(
                company_name, 
                scenario['region']
            )
            
            # 3. 演示经营范围生成
            business_scopes = demo.demonstrate_business_scope_generation(
                scenario['industry']
            )
            
            # 4. 组合完整企业数据
            complete_company_data = {
                'name': company_name,
                'industry': scenario['industry'],
                'region': scenario['region'],
                'phone': contact_info['phone'],
                'email': contact_info['email'],
                'address': contact_info['address'],
                'website': contact_info['website'],
                'business_scope': '; '.join(business_scopes)
            }
            
            # 5. 演示数据验证
            validation_results = demo.demonstrate_data_validation(complete_company_data)
            
            print(f"\n📋 完整企业数据:")
            print(f"  企业名称: {complete_company_data['name']}")
            print(f"  所属行业: {complete_company_data['industry']}")
            print(f"  所在地区: {complete_company_data['region']}")
            print(f"  联系电话: {complete_company_data['phone']}")
            print(f"  邮箱地址: {complete_company_data['email']}")
            print(f"  企业地址: {complete_company_data['address']}")
            print(f"  企业网站: {complete_company_data['website']}")
            print(f"  经营范围: {complete_company_data['business_scope'][:100]}...")
            print(f"  数据质量: {validation_results['total_score']*100:.1f}%")
    
    print(f"\n{'='*80}")
    print("🎉 本地数据爬虫原理演示完成!")
    print("💡 通过以上演示，您可以看到本地数据爬虫如何:")
    print("   1. 基于行业和地区模板生成真实的企业名称")
    print("   2. 根据地区特征生成合理的联系信息")
    print("   3. 基于行业特点生成相关的经营范围")
    print("   4. 通过多维度验证确保数据质量")
    print("   5. 实现高效、稳定、合规的数据生成")

if __name__ == "__main__":
    main()
