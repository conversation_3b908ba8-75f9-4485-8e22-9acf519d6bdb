#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心爬虫引擎 - 多源数据采集
专家级爬虫实现，支持多种数据源和反反爬机制
"""

import requests
try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None
try:
    from fake_useragent import UserAgent
except ImportError:
    UserAgent = None
import time
import random
import re
import pandas as pd
from urllib.parse import urljoin, urlparse, quote
from typing import List, Dict, Any
import yaml
from pathlib import Path

from .utils.logger import get_logger
from .region_manager import RegionManager
from .professional_crawler import ProfessionalCrawler
from .enhanced_search_engine import EnhancedSearchEngine
from .social_media_crawler import SocialMediaCrawler
from .enterprise_info_crawler import EnterpriseInfoCrawler
from .enhanced_real_crawler import EnhancedRealCrawler
from .real_data_solution import RealDataSolution

class CrawlerEngine:
    """核心爬虫引擎类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化爬虫引擎"""
        self.logger = get_logger(__name__)
        self.config = self._load_config(config_path)
        self.ua = UserAgent() if UserAgent else None
        self.anti_detection = None
        self.session = requests.Session()
        self.results = []
        self.region_manager = RegionManager(config_path)

        # 初始化专业爬虫
        try:
            self.professional_crawler = ProfessionalCrawler()
        except Exception as e:
            self.logger.warning(f"专业爬虫初始化失败: {e}")
            self.professional_crawler = None

        # 初始化增强搜索引擎
        try:
            self.enhanced_search = EnhancedSearchEngine()
        except Exception as e:
            self.logger.warning(f"增强搜索引擎初始化失败: {e}")
            self.enhanced_search = None

        # 初始化社交媒体爬虫
        try:
            self.social_media_crawler = SocialMediaCrawler()
        except Exception as e:
            self.logger.warning(f"社交媒体爬虫初始化失败: {e}")
            self.social_media_crawler = None

        # 初始化企业信息爬虫
        try:
            self.enterprise_info_crawler = EnterpriseInfoCrawler()
        except Exception as e:
            self.logger.warning(f"企业信息爬虫初始化失败: {e}")
            self.enterprise_info_crawler = None

        # 初始化增强真实数据爬虫
        try:
            self.enhanced_real_crawler = EnhancedRealCrawler()
        except Exception as e:
            self.logger.warning(f"增强真实数据爬虫初始化失败: {e}")
            self.enhanced_real_crawler = None

        # 初始化真实数据解决方案
        try:
            self.real_data_solution = RealDataSolution()
        except Exception as e:
            self.logger.warning(f"真实数据解决方案初始化失败: {e}")
            self.real_data_solution = None

        # 设置请求头
        self._setup_session()
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'crawler': {
                'concurrent_requests': 8,
                'download_delay': 1,
                'download_timeout': 30,
                'retry_times': 3
            },
            'search_engines': {
                'baidu': {'enabled': True, 'max_pages': 10},
                'bing': {'enabled': True, 'max_pages': 10}
            }
        }
    
    def _setup_session(self):
        """设置请求会话"""
        user_agent = self.ua.random if self.ua else 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        self.session.headers.update({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def start_crawling(self, crawler_config: Dict) -> pd.DataFrame:
        """开始爬取数据"""
        industry = crawler_config['industry']
        province = crawler_config.get('province')
        city = crawler_config.get('city')
        search_depth = crawler_config.get('search_depth', 10)

        # 验证区域选择
        is_valid, message = self.region_manager.validate_region(province, city)
        if not is_valid:
            self.logger.error(f"区域选择无效: {message}")
            return pd.DataFrame()

        region_display = self.region_manager.get_region_display_name(province, city)
        self.logger.info(f"开始爬取 - 行业: {industry}, 区域: {region_display}")

        all_data = []

        # 搜索引擎爬取
        if crawler_config.get('use_baidu', True):
            baidu_data = self._crawl_baidu(industry, search_depth, province, city)
            all_data.extend(baidu_data)

        if crawler_config.get('use_bing', True):
            bing_data = self._crawl_bing(industry, search_depth, province, city)
            all_data.extend(bing_data)

        # 企业目录爬取
        if crawler_config.get('use_business_dirs', True):
            business_data = self._crawl_business_directories(industry, province, city)
            all_data.extend(business_data)

        # 专业数据源爬取（招标、房地产、酒店等）
        if crawler_config.get('use_professional_sources', True) and self.professional_crawler:
            try:
                self.logger.info("开始爬取专业数据源...")
                professional_data = self.professional_crawler.search_professional_sources(
                    industry, province, city
                )
                if not professional_data.empty:
                    # 转换为字典列表格式
                    professional_list = professional_data.to_dict('records')
                    all_data.extend(professional_list)
                    self.logger.info(f"专业数据源获得 {len(professional_list)} 条数据")
                else:
                    self.logger.info("专业数据源未获得数据")
            except Exception as e:
                self.logger.error(f"专业数据源爬取出错: {e}")

        # 增强搜索引擎爬取（通过搜索引擎查找专业内容）
        if crawler_config.get('use_enhanced_search', True) and self.enhanced_search:
            try:
                self.logger.info("开始增强搜索...")
                enhanced_data = self.enhanced_search.search_professional_content(
                    industry, province, city
                )
                if not enhanced_data.empty:
                    # 转换为字典列表格式
                    enhanced_list = enhanced_data.to_dict('records')
                    all_data.extend(enhanced_list)
                    self.logger.info(f"增强搜索获得 {len(enhanced_list)} 条数据")
                else:
                    self.logger.info("增强搜索未获得数据")
            except Exception as e:
                self.logger.error(f"增强搜索出错: {e}")

        # 社交媒体平台爬取（抖音、快手等）
        if crawler_config.get('use_social_media', False) and self.social_media_crawler:
            try:
                self.logger.info("开始爬取社交媒体平台...")
                social_platforms = crawler_config.get('social_platforms', ['douyin', 'kuaishou'])
                social_data = self.social_media_crawler.search_social_media_platforms(
                    industry, province, city, social_platforms
                )
                if not social_data.empty:
                    # 转换为字典列表格式
                    social_list = social_data.to_dict('records')
                    all_data.extend(social_list)
                    self.logger.info(f"社交媒体平台获得 {len(social_list)} 条数据")
                else:
                    self.logger.info("社交媒体平台未获得数据")
            except Exception as e:
                self.logger.error(f"社交媒体平台爬取出错: {e}")

        # 企业信息平台爬取（企查查、天眼查、钉钉企典等）
        if crawler_config.get('use_enterprise_info', False) and self.enterprise_info_crawler:
            try:
                self.logger.info("开始爬取企业信息平台...")
                enterprise_platforms = crawler_config.get('enterprise_platforms', ['qichacha', 'tianyancha', 'dingding'])
                enterprise_data = self.enterprise_info_crawler.search_enterprise_platforms(
                    industry, province, city, enterprise_platforms
                )
                if not enterprise_data.empty:
                    # 转换为字典列表格式
                    enterprise_list = enterprise_data.to_dict('records')
                    all_data.extend(enterprise_list)
                    self.logger.info(f"企业信息平台获得 {len(enterprise_list)} 条数据")
                else:
                    self.logger.info("企业信息平台未获得数据")
            except Exception as e:
                self.logger.error(f"企业信息平台爬取出错: {e}")

        # 真实数据解决方案（优先使用）
        if crawler_config.get('use_enhanced_real', True) and self.real_data_solution:
            try:
                self.logger.info("开始真实数据解决方案...")
                max_real_results = crawler_config.get('max_real_results', 50)
                real_data = self.real_data_solution.get_enhanced_real_data(
                    industry, province, city, max_real_results
                )
                if not real_data.empty:
                    # 转换为字典列表格式
                    real_list = real_data.to_dict('records')
                    all_data.extend(real_list)
                    self.logger.info(f"真实数据解决方案获得 {len(real_list)} 条数据")
                else:
                    self.logger.info("真实数据解决方案未获得数据")
            except Exception as e:
                self.logger.error(f"真实数据解决方案出错: {e}")

        # 增强真实数据爬取（备用方案）
        elif crawler_config.get('use_enhanced_real', True) and self.enhanced_real_crawler:
            try:
                self.logger.info("开始增强真实数据爬取...")
                max_real_results = crawler_config.get('max_real_results', 50)
                real_data = self.enhanced_real_crawler.search_real_business_data(
                    industry, province, city, max_real_results
                )
                if not real_data.empty:
                    # 转换为字典列表格式
                    real_list = real_data.to_dict('records')
                    all_data.extend(real_list)
                    self.logger.info(f"增强真实数据爬取获得 {len(real_list)} 条数据")
                else:
                    self.logger.info("增强真实数据爬取未获得数据")
            except Exception as e:
                self.logger.error(f"增强真实数据爬取出错: {e}")

        # 转换为DataFrame
        df = pd.DataFrame(all_data)
        self.logger.info(f"爬取完成，共获得 {len(df)} 条数据")

        return df
    
    def _crawl_baidu(self, industry: str, max_pages: int = 10, province: str = None, city: str = None) -> List[Dict]:
        """爬取百度搜索结果"""
        region_display = self.region_manager.get_region_display_name(province, city)
        self.logger.info(f"开始爬取百度搜索结果 - 区域: {region_display}")
        results = []

        # 构建区域化搜索关键词
        keywords = self.region_manager.generate_region_keywords(province, city, industry)

        # 如果没有区域关键词，使用默认关键词
        if not keywords:
            keywords = [
                f"{industry} 公司",
                f"{industry} 企业",
                f"{industry} 厂家",
                f"{industry} 供应商"
            ]
        
        for keyword in keywords:
            try:
                for page in range(min(max_pages, 5)):  # 限制页数避免被封
                    url = f"https://www.baidu.com/s?wd={quote(keyword)}&pn={page*10}"

                    # 随机延时
                    time.sleep(random.uniform(1, 3))

                    response = self.session.get(url, timeout=30)
                    if response.status_code == 200:
                        # 尝试解析真实数据
                        page_results = self._parse_search_results(response.text, keyword, industry, 'baidu')
                        results.extend(page_results)

                        self.logger.info(f"百度搜索 '{keyword}' 第{page+1}页: {len(page_results)}条结果")

                    # 检查是否被反爬
                    if "百度安全验证" in response.text:
                        self.logger.warning("百度反爬检测，暂停爬取")
                        break

            except Exception as e:
                self.logger.error(f"百度搜索出错: {e}")
                continue

        # 如果没有获取到真实数据，返回空结果
        if not results:
            self.logger.warning("未获取到真实数据")

        return results



    def _parse_search_results(self, html_content: str, keyword: str, industry: str, source: str) -> List[Dict]:
        """解析搜索结果页面，提取企业信息"""
        results = []

        try:
            if BeautifulSoup:
                soup = BeautifulSoup(html_content, 'html.parser')

                if source == 'baidu':
                    results = self._parse_baidu_results(soup, industry)
                elif source == 'bing':
                    results = self._parse_bing_results(soup, industry)
            else:
                # 使用正则表达式提取基本信息
                results = self._parse_with_regex(html_content, keyword, industry, source)

        except Exception as e:
            self.logger.error(f"解析搜索结果时出错: {e}")
            # 如果解析失败，尝试正则表达式方法
            results = self._parse_with_regex(html_content, keyword, industry, source)

        return results

    def _parse_with_regex(self, html_content: str, keyword: str, industry: str, source: str) -> List[Dict]:
        """使用正则表达式解析HTML内容"""
        results = []

        # 提取公司名称的正则模式
        company_patterns = [
            r'([^<>]*(?:' + keyword + r')[^<>]*(?:公司|企业|集团|有限|股份|科技|实业))',
            r'([^<>]*(?:公司|企业|集团|有限|股份|科技|实业)[^<>]*(?:' + keyword + r')[^<>]*)',
            r'title="([^"]*(?:' + keyword + r')[^"]*(?:公司|企业|集团|有限|股份|科技|实业)[^"]*)"',
            r'>([^<]*(?:' + keyword + r')[^<]*(?:公司|企业|集团|有限|股份|科技|实业)[^<]*)<'
        ]

        # 提取电话号码的正则模式
        phone_patterns = [
            r'(?:电话|联系电话|Tel|TEL|手机|联系方式)[:：\s]*([0-9\-\s\(\)]{7,20})',
            r'(\d{3,4}[-\s]?\d{7,8})',  # 固定电话
            r'(1[3-9]\d{9})',  # 手机号码
            r'(\d{3}-\d{4}-\d{4})',  # 格式化电话
        ]

        # 提取网址的正则模式
        url_patterns = [
            r'href="(https?://[^"]+)"',
            r'(https?://[^\s<>"]+)',
        ]

        # 提取地址的正则模式
        address_patterns = [
            r'(?:地址|Address|ADD)[:：\s]*([^<>\n]{10,100})',
            r'([^<>\n]*(?:省|市|区|县|街道|路|号)[^<>\n]{5,50})',
        ]

        found_companies = set()

        for pattern in company_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                company_name = self._clean_text(match)
                if self._is_valid_company_name(company_name, keyword) and company_name not in found_companies:
                    found_companies.add(company_name)

                    # 尝试在附近文本中找到联系信息
                    company_info = {
                        'company_name': company_name,
                        'industry': industry,
                        'source': source,
                        'phone': self._extract_nearby_info(html_content, company_name, phone_patterns),
                        'website': self._extract_nearby_info(html_content, company_name, url_patterns),
                        'address': self._extract_nearby_info(html_content, company_name, address_patterns),
                        'description': f'{industry}相关企业'
                    }

                    results.append(company_info)

        return results[:10]  # 限制每页结果数量

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\-\(\)（）]', '', text)

        return text.strip()

    def _is_valid_company_name(self, name: str, keyword: str) -> bool:
        """验证公司名称是否有效"""
        if not name or len(name) < 5:
            return False

        # 必须包含关键词
        if keyword.lower() not in name.lower():
            return False

        # 必须包含公司相关词汇
        company_keywords = ['公司', '企业', '集团', '有限', '股份', '科技', '实业']
        if not any(kw in name for kw in company_keywords):
            return False

        # 排除无关结果
        exclude_keywords = ['招聘', '求职', '新闻', '百科', '知乎', '贴吧', '论坛', '视频']
        if any(kw in name for kw in exclude_keywords):
            return False

        return True

    def _extract_nearby_info(self, html_content: str, company_name: str, patterns: List[str]) -> str:
        """在公司名称附近提取相关信息"""
        # 找到公司名称在HTML中的位置
        company_pos = html_content.find(company_name)
        if company_pos == -1:
            return ""

        # 提取公司名称前后1000个字符的内容
        start = max(0, company_pos - 1000)
        end = min(len(html_content), company_pos + len(company_name) + 1000)
        nearby_content = html_content[start:end]

        # 在附近内容中搜索匹配的信息
        for pattern in patterns:
            matches = re.findall(pattern, nearby_content, re.IGNORECASE)
            if matches:
                result = self._clean_text(matches[0])
                if result and len(result) > 3:
                    return result

        return ""

    def _parse_baidu_results(self, soup, industry: str) -> List[Dict]:
        """解析百度搜索结果"""
        results = []
        
        # 查找搜索结果
        result_items = soup.find_all('div', class_='result')
        
        for item in result_items:
            try:
                # 提取标题和链接
                title_elem = item.find('h3')
                if not title_elem:
                    continue
                    
                link_elem = title_elem.find('a')
                if not link_elem:
                    continue
                
                title = title_elem.get_text().strip()
                link = link_elem.get('href', '')
                
                # 提取描述
                desc_elem = item.find('span', class_='content-right_8Zs40')
                description = desc_elem.get_text().strip() if desc_elem else ""
                
                # 基本数据清洗
                if self._is_valid_company_result(title, description, industry):
                    company_info = {
                        'company_name': self._extract_company_name(title),
                        'industry': industry,
                        'website': self._extract_website(link),
                        'description': description,
                        'source': 'baidu',
                        'title': title,
                        'raw_link': link
                    }
                    
                    # 尝试提取更多信息
                    additional_info = self._extract_additional_info(description)
                    company_info.update(additional_info)
                    
                    results.append(company_info)
                    
            except Exception as e:
                self.logger.debug(f"解析百度结果项出错: {e}")
                continue
        
        return results
    
    def _crawl_bing(self, industry: str, max_pages: int = 10, province: str = None, city: str = None) -> List[Dict]:
        """爬取必应搜索结果"""
        region_display = self.region_manager.get_region_display_name(province, city)
        self.logger.info(f"开始爬取必应搜索结果 - 区域: {region_display}")
        results = []

        # 构建区域化搜索关键词
        keywords = self.region_manager.generate_region_keywords(province, city, industry)

        # 如果没有区域关键词，使用默认关键词
        if not keywords:
            keywords = [
                f"{industry} 公司",
                f"{industry} 企业官网",
                f"{industry} 制造商"
            ]
        
        for keyword in keywords:
            try:
                for page in range(min(max_pages, 3)):
                    url = f"https://www.bing.com/search?q={quote(keyword)}&first={page*10+1}"

                    time.sleep(random.uniform(1, 2))

                    response = self.session.get(url, timeout=30)
                    if response.status_code == 200:
                        # 尝试解析真实数据
                        page_results = self._parse_search_results(response.text, keyword, industry, 'bing')
                        results.extend(page_results)

                        self.logger.info(f"必应搜索 '{keyword}' 第{page+1}页: {len(page_results)}条结果")

            except Exception as e:
                self.logger.error(f"必应搜索出错: {e}")
                continue

        # 如果没有获取到真实数据，返回空结果
        if not results:
            self.logger.warning("必应未获取到真实数据")

        return results
    
    def _parse_bing_results(self, soup, industry: str) -> List[Dict]:
        """解析必应搜索结果"""
        results = []

        # 尝试多种选择器来查找搜索结果
        selectors = [
            'li.b_algo',  # 传统必应结果
            '.b_algo',    # 简化选择器
            'li[class*="algo"]',  # 包含algo的类
            '.b_searchResult',  # 另一种可能的类名
            'li[class*="result"]'  # 包含result的类
        ]

        result_items = []
        for selector in selectors:
            items = soup.select(selector)
            if items:
                result_items = items
                self.logger.debug(f"使用选择器 {selector} 找到 {len(items)} 个结果")
                break

        if not result_items:
            self.logger.warning("未找到必应搜索结果项")
            return results

        for item in result_items:
            try:
                # 提取标题和链接 - 尝试多种方式
                title_elem = item.find('h2') or item.find('h3') or item.find('a')
                if not title_elem:
                    continue

                # 获取链接
                link_elem = title_elem.find('a') if title_elem.name != 'a' else title_elem
                if not link_elem:
                    link_elem = item.find('a')

                if not link_elem:
                    continue

                title = title_elem.get_text().strip()
                link = link_elem.get('href', '')

                # 提取描述 - 尝试多种方式
                desc_elem = item.find('p') or item.find('.b_caption') or item.find('[class*="caption"]')
                description = desc_elem.get_text().strip() if desc_elem else ""

                # 如果没有找到描述，尝试获取所有文本
                if not description:
                    all_text = item.get_text()
                    # 移除标题文本，剩下的作为描述
                    description = all_text.replace(title, '').strip()

                if self._is_valid_company_result(title, description, industry):
                    company_info = {
                        'company_name': self._extract_company_name(title),
                        'industry': industry,
                        'website': link,
                        'description': description,
                        'source': 'bing',
                        'title': title
                    }

                    additional_info = self._extract_additional_info(description)
                    company_info.update(additional_info)

                    results.append(company_info)

            except Exception as e:
                self.logger.debug(f"解析必应结果项出错: {e}")
                continue

        return results
    
    def _crawl_business_directories(self, industry: str, province: str = None, city: str = None) -> List[Dict]:
        """爬取企业目录网站"""
        region_display = self.region_manager.get_region_display_name(province, city)
        self.logger.info(f"开始爬取企业目录 - 区域: {region_display}")
        results = []

        # 企业信息网站列表
        business_sites = [
            {
                'name': '企查查',
                'search_url': 'https://www.qcc.com/web/search',
                'params': {'key': industry}
            },
            {
                'name': '天眼查',
                'search_url': 'https://www.tianyancha.com/search',
                'params': {'key': industry}
            }
        ]

        for site in business_sites:
            try:
                self.logger.info(f"正在搜索 {site['name']}...")

                # 构建搜索参数
                search_params = site['params'].copy()
                if province:
                    search_params['province'] = province
                if city:
                    search_params['city'] = city

                # 发送搜索请求
                response = self.session.get(
                    site['search_url'],
                    params=search_params,
                    timeout=30,
                    headers={'Referer': 'https://www.google.com'}
                )

                if response.status_code == 200:
                    # 解析企业目录结果
                    site_results = self._parse_business_directory_results(
                        response.text, industry, site['name']
                    )
                    results.extend(site_results)

                    self.logger.info(f"{site['name']} 搜索完成: {len(site_results)}条结果")

                    # 添加延时避免被封
                    time.sleep(random.uniform(2, 5))

            except Exception as e:
                self.logger.warning(f"搜索 {site['name']} 时出错: {e}")
                continue

        return results

    def _parse_business_directory_results(self, html_content: str, industry: str, source: str) -> List[Dict]:
        """解析企业目录搜索结果"""
        results = []

        try:
            # 使用正则表达式提取企业信息
            # 企业名称模式
            name_patterns = [
                r'company["\']?\s*:\s*["\']([^"\']+)["\']',
                r'title["\']?\s*:\s*["\']([^"\']*(?:公司|企业|集团|有限|股份)[^"\']*)["\']',
                r'>([^<]*(?:公司|企业|集团|有限|股份)[^<]*)<',
            ]

            # 联系信息模式
            contact_patterns = {
                'phone': [
                    r'["\']phone["\']?\s*:\s*["\']([^"\']+)["\']',
                    r'电话[：:]\s*([0-9\-\s\(\)]{7,20})',
                    r'(\d{3,4}[-\s]?\d{7,8})',
                ],
                'address': [
                    r'["\']address["\']?\s*:\s*["\']([^"\']+)["\']',
                    r'地址[：:]\s*([^<>\n]{10,100})',
                ],
                'website': [
                    r'["\']website["\']?\s*:\s*["\']([^"\']+)["\']',
                    r'href=["\']?(https?://[^"\'>\s]+)["\']?',
                ]
            }

            # 提取企业名称
            found_companies = set()
            for pattern in name_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    company_name = self._clean_text(match)
                    if (self._is_valid_company_name(company_name, industry) and
                        company_name not in found_companies and
                        len(company_name) > 5):

                        found_companies.add(company_name)

                        # 提取联系信息
                        company_info = {
                            'company_name': company_name,
                            'industry': industry,
                            'source': source,
                            'phone': '',
                            'website': '',
                            'address': '',
                            'description': f'{industry}相关企业'
                        }

                        # 在公司名称附近查找联系信息
                        for info_type, patterns in contact_patterns.items():
                            info = self._extract_nearby_info(html_content, company_name, patterns)
                            if info:
                                company_info[info_type] = info

                        results.append(company_info)

                        if len(results) >= 20:  # 限制结果数量
                            break

        except Exception as e:
            self.logger.error(f"解析企业目录结果时出错: {e}")

        return results
    
    def _is_valid_company_result(self, title: str, description: str, industry: str) -> bool:
        """判断是否为有效的公司结果"""
        # 排除无关结果
        exclude_keywords = ['招聘', '求职', '新闻', '百科', '知乎', '贴吧', '论坛']
        
        text = (title + " " + description).lower()
        
        for keyword in exclude_keywords:
            if keyword in text:
                return False
        
        # 必须包含公司相关词汇
        company_keywords = ['公司', '企业', '有限', '股份', '集团', '科技', '实业']
        
        for keyword in company_keywords:
            if keyword in text:
                return True
        
        return False
    
    def _extract_company_name(self, title: str) -> str:
        """从标题中提取公司名称"""
        # 移除常见的后缀
        title = re.sub(r'[-_|].*$', '', title)
        title = re.sub(r'官网|首页|主页', '', title)
        
        # 提取公司名称模式
        patterns = [
            r'(.+?有限公司)',
            r'(.+?股份有限公司)',
            r'(.+?集团)',
            r'(.+?科技)',
            r'(.+?实业)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1).strip()
        
        return title.strip()
    
    def _extract_website(self, link: str) -> str:
        """提取网站域名"""
        try:
            if link.startswith('http'):
                parsed = urlparse(link)
                return f"{parsed.scheme}://{parsed.netloc}"
            return ""
        except:
            return ""
    
    def _extract_additional_info(self, description: str) -> Dict:
        """从描述中提取额外信息"""
        info = {}
        
        # 提取电话号码
        phone_pattern = r'(\d{3,4}[-\s]?\d{7,8}|\d{11})'
        phone_match = re.search(phone_pattern, description)
        if phone_match:
            info['phone'] = phone_match.group(1)
        
        # 提取邮箱
        email_pattern = r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
        email_match = re.search(email_pattern, description)
        if email_match:
            info['email'] = email_match.group(1)
        
        # 提取地址信息
        address_keywords = ['地址', '位于', '坐落']
        for keyword in address_keywords:
            if keyword in description:
                # 简单的地址提取逻辑
                parts = description.split(keyword)
                if len(parts) > 1:
                    potential_address = parts[1][:50]  # 取前50个字符
                    info['address'] = potential_address.strip()
                    break
        
        return info
