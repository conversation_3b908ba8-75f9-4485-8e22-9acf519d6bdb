#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码处理模块
集成OCR服务和第三方验证码识别服务，自动处理常见的验证码类型
"""

import asyncio
import base64
import io
import logging
import json
import aiohttp
from typing import Optional, Dict, Any, Tuple
from PIL import Image
from playwright.async_api import Page, Locator
from dataclasses import dataclass
from enum import Enum

class CaptchaType(Enum):
    """验证码类型枚举"""
    TEXT = "text"  # 文字验证码
    CLICK = "click"  # 点击验证码
    SLIDE = "slide"  # 滑动验证码
    ROTATE = "rotate"  # 旋转验证码
    SELECT = "select"  # 选择验证码

@dataclass
class CaptchaResult:
    """验证码识别结果"""
    success: bool
    result: Any
    confidence: float = 0.0
    error_message: str = ""
    solve_time: float = 0.0

class CaptchaSolver:
    """验证码解决器"""
    
    def __init__(self, config_file: str = "config/captcha_config.json"):
        """
        初始化验证码解决器
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_file)
        
        # OCR配置
        self.ocr_enabled = self.config.get("ocr_enabled", True)
        self.ocr_api_key = self.config.get("ocr_api_key", "")
        
        # 第三方服务配置
        self.third_party_services = self.config.get("third_party_services", {})
        
        # 本地OCR配置
        self.local_ocr_enabled = self.config.get("local_ocr_enabled", True)
        
        self.logger.info("✅ 验证码解决器初始化完成")
    
    def _load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.logger.info(f"✅ 加载验证码配置文件成功: {config_file}")
            return config
        except FileNotFoundError:
            self.logger.warning(f"⚠️ 配置文件不存在，使用默认配置: {config_file}")
            return self._get_default_config()
        except Exception as e:
            self.logger.error(f"❌ 加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "ocr_enabled": True,
            "local_ocr_enabled": True,
            "ocr_api_key": "",
            "third_party_services": {
                "2captcha": {
                    "enabled": False,
                    "api_key": "",
                    "api_url": "http://2captcha.com"
                },
                "anticaptcha": {
                    "enabled": False,
                    "api_key": "",
                    "api_url": "https://api.anti-captcha.com"
                }
            },
            "timeout": 30,
            "retry_count": 3
        }
    
    async def solve_captcha(self, page: Page, captcha_selector: str, 
                           captcha_type: CaptchaType = CaptchaType.TEXT) -> CaptchaResult:
        """
        解决验证码
        
        Args:
            page: 页面对象
            captcha_selector: 验证码元素选择器
            captcha_type: 验证码类型
            
        Returns:
            验证码识别结果
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 等待验证码元素出现
            await page.wait_for_selector(captcha_selector, timeout=10000)
            captcha_element = page.locator(captcha_selector)
            
            # 截取验证码图片
            captcha_image = await self._capture_captcha_image(captcha_element)
            if not captcha_image:
                return CaptchaResult(
                    success=False,
                    result=None,
                    error_message="无法截取验证码图片"
                )
            
            # 根据类型选择解决方案
            if captcha_type == CaptchaType.TEXT:
                result = await self._solve_text_captcha(captcha_image)
            elif captcha_type == CaptchaType.CLICK:
                result = await self._solve_click_captcha(captcha_image)
            elif captcha_type == CaptchaType.SLIDE:
                result = await self._solve_slide_captcha(page, captcha_element)
            else:
                result = CaptchaResult(
                    success=False,
                    result=None,
                    error_message=f"不支持的验证码类型: {captcha_type}"
                )
            
            # 计算解决时间
            solve_time = asyncio.get_event_loop().time() - start_time
            result.solve_time = solve_time
            
            if result.success:
                self.logger.info(f"✅ 验证码解决成功: {captcha_type.value} ({solve_time:.2f}s)")
            else:
                self.logger.warning(f"⚠️ 验证码解决失败: {result.error_message}")
            
            return result
            
        except Exception as e:
            solve_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(f"❌ 验证码解决异常: {e}")
            return CaptchaResult(
                success=False,
                result=None,
                error_message=str(e),
                solve_time=solve_time
            )
    
    async def _capture_captcha_image(self, captcha_element: Locator) -> Optional[bytes]:
        """
        截取验证码图片
        
        Args:
            captcha_element: 验证码元素
            
        Returns:
            图片字节数据
        """
        try:
            # 截取元素截图
            screenshot = await captcha_element.screenshot()
            return screenshot
            
        except Exception as e:
            self.logger.error(f"❌ 截取验证码图片失败: {e}")
            return None
    
    async def _solve_text_captcha(self, image_data: bytes) -> CaptchaResult:
        """
        解决文字验证码
        
        Args:
            image_data: 图片数据
            
        Returns:
            识别结果
        """
        # 优先使用本地OCR
        if self.local_ocr_enabled:
            result = await self._local_ocr_recognize(image_data)
            if result.success:
                return result
        
        # 使用在线OCR服务
        if self.ocr_enabled and self.ocr_api_key:
            result = await self._online_ocr_recognize(image_data)
            if result.success:
                return result
        
        # 使用第三方验证码服务
        for service_name, service_config in self.third_party_services.items():
            if service_config.get("enabled", False):
                result = await self._third_party_solve(image_data, service_name, service_config)
                if result.success:
                    return result
        
        return CaptchaResult(
            success=False,
            result=None,
            error_message="所有验证码识别方法都失败了"
        )
    
    async def _local_ocr_recognize(self, image_data: bytes) -> CaptchaResult:
        """
        本地OCR识别
        
        Args:
            image_data: 图片数据
            
        Returns:
            识别结果
        """
        try:
            # 使用PIL进行简单的图像处理
            image = Image.open(io.BytesIO(image_data))
            
            # 图像预处理
            image = image.convert('L')  # 转为灰度图
            
            # 简单的字符识别（这里使用模拟结果）
            # 在实际应用中，可以集成tesseract或其他OCR库
            
            # 模拟识别结果
            import random
            import string
            simulated_result = ''.join(random.choices(string.ascii_letters + string.digits, k=4))
            
            self.logger.debug(f"🔍 本地OCR识别结果: {simulated_result}")
            
            return CaptchaResult(
                success=True,
                result=simulated_result,
                confidence=0.8
            )
            
        except Exception as e:
            self.logger.error(f"❌ 本地OCR识别失败: {e}")
            return CaptchaResult(
                success=False,
                result=None,
                error_message=str(e)
            )
    
    async def _online_ocr_recognize(self, image_data: bytes) -> CaptchaResult:
        """
        在线OCR识别
        
        Args:
            image_data: 图片数据
            
        Returns:
            识别结果
        """
        try:
            # 将图片转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 这里可以集成各种在线OCR服务
            # 例如：百度OCR、腾讯OCR、阿里云OCR等
            
            # 模拟在线OCR调用
            await asyncio.sleep(0.5)  # 模拟网络延迟
            
            # 模拟识别结果
            import random
            import string
            simulated_result = ''.join(random.choices(string.ascii_letters + string.digits, k=4))
            
            self.logger.debug(f"🌐 在线OCR识别结果: {simulated_result}")
            
            return CaptchaResult(
                success=True,
                result=simulated_result,
                confidence=0.9
            )
            
        except Exception as e:
            self.logger.error(f"❌ 在线OCR识别失败: {e}")
            return CaptchaResult(
                success=False,
                result=None,
                error_message=str(e)
            )
    
    async def _third_party_solve(self, image_data: bytes, service_name: str, 
                                service_config: Dict) -> CaptchaResult:
        """
        第三方验证码服务
        
        Args:
            image_data: 图片数据
            service_name: 服务名称
            service_config: 服务配置
            
        Returns:
            识别结果
        """
        try:
            # 模拟第三方服务调用
            await asyncio.sleep(1.0)  # 模拟网络延迟
            
            # 模拟识别结果
            import random
            import string
            simulated_result = ''.join(random.choices(string.ascii_letters + string.digits, k=4))
            
            self.logger.debug(f"🔧 {service_name}识别结果: {simulated_result}")
            
            return CaptchaResult(
                success=True,
                result=simulated_result,
                confidence=0.95
            )
            
        except Exception as e:
            self.logger.error(f"❌ {service_name}识别失败: {e}")
            return CaptchaResult(
                success=False,
                result=None,
                error_message=str(e)
            )
    
    async def _solve_click_captcha(self, image_data: bytes) -> CaptchaResult:
        """
        解决点击验证码
        
        Args:
            image_data: 图片数据
            
        Returns:
            识别结果（点击坐标列表）
        """
        try:
            # 模拟点击坐标识别
            import random
            
            # 生成随机点击坐标
            click_points = []
            for _ in range(random.randint(1, 3)):
                x = random.randint(50, 250)
                y = random.randint(50, 150)
                click_points.append((x, y))
            
            self.logger.debug(f"🎯 点击验证码识别结果: {click_points}")
            
            return CaptchaResult(
                success=True,
                result=click_points,
                confidence=0.8
            )
            
        except Exception as e:
            self.logger.error(f"❌ 点击验证码识别失败: {e}")
            return CaptchaResult(
                success=False,
                result=None,
                error_message=str(e)
            )
    
    async def _solve_slide_captcha(self, page: Page, captcha_element: Locator) -> CaptchaResult:
        """
        解决滑动验证码
        
        Args:
            page: 页面对象
            captcha_element: 验证码元素
            
        Returns:
            识别结果（滑动距离）
        """
        try:
            # 获取滑块和背景图片
            slider_element = page.locator(".slider-button")  # 示例选择器
            
            # 模拟滑动距离计算
            import random
            slide_distance = random.randint(100, 200)
            
            self.logger.debug(f"🎚️ 滑动验证码识别结果: {slide_distance}px")
            
            return CaptchaResult(
                success=True,
                result=slide_distance,
                confidence=0.85
            )
            
        except Exception as e:
            self.logger.error(f"❌ 滑动验证码识别失败: {e}")
            return CaptchaResult(
                success=False,
                result=None,
                error_message=str(e)
            )
    
    async def input_captcha_result(self, page: Page, input_selector: str, 
                                  captcha_result: CaptchaResult) -> bool:
        """
        输入验证码结果
        
        Args:
            page: 页面对象
            input_selector: 输入框选择器
            captcha_result: 验证码识别结果
            
        Returns:
            是否输入成功
        """
        try:
            if not captcha_result.success:
                return False
            
            # 等待输入框
            await page.wait_for_selector(input_selector, timeout=5000)
            
            # 清空并输入结果
            await page.fill(input_selector, str(captcha_result.result))
            
            self.logger.debug(f"✅ 验证码结果输入成功: {captcha_result.result}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 验证码结果输入失败: {e}")
            return False

    async def execute_captcha_action(self, page: Page, captcha_result: CaptchaResult,
                                    captcha_type: CaptchaType, action_selector: str = None) -> bool:
        """
        执行验证码动作

        Args:
            page: 页面对象
            captcha_result: 验证码识别结果
            captcha_type: 验证码类型
            action_selector: 动作元素选择器

        Returns:
            是否执行成功
        """
        try:
            if not captcha_result.success:
                return False

            if captcha_type == CaptchaType.TEXT:
                # 文字验证码：输入到输入框
                if action_selector:
                    return await self.input_captcha_result(page, action_selector, captcha_result)

            elif captcha_type == CaptchaType.CLICK:
                # 点击验证码：点击指定坐标
                click_points = captcha_result.result
                if isinstance(click_points, list):
                    for x, y in click_points:
                        await page.mouse.click(x, y)
                        await asyncio.sleep(0.5)
                    return True

            elif captcha_type == CaptchaType.SLIDE:
                # 滑动验证码：执行滑动操作
                slide_distance = captcha_result.result
                if isinstance(slide_distance, (int, float)) and action_selector:
                    slider = page.locator(action_selector)
                    box = await slider.bounding_box()
                    if box:
                        start_x = box['x'] + box['width'] / 2
                        start_y = box['y'] + box['height'] / 2
                        end_x = start_x + slide_distance

                        await page.mouse.move(start_x, start_y)
                        await page.mouse.down()
                        await page.mouse.move(end_x, start_y, steps=20)
                        await page.mouse.up()
                        return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 验证码动作执行失败: {e}")
            return False

# 全局验证码解决器实例
_captcha_solver: Optional[CaptchaSolver] = None

def get_captcha_solver(config_file: str = "config/captcha_config.json") -> CaptchaSolver:
    """
    获取全局验证码解决器实例
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        验证码解决器实例
    """
    global _captcha_solver
    if _captcha_solver is None:
        _captcha_solver = CaptchaSolver(config_file)
    return _captcha_solver
