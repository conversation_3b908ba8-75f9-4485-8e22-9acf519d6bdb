#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器指纹伪造管理模块
提供更复杂的浏览器指纹伪造和环境模拟功能
"""

import random
import json
import os
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

@dataclass
class BrowserFingerprint:
    """浏览器指纹数据类"""
    user_agent: str
    viewport: Dict[str, int]
    screen: Dict[str, int]
    timezone: str
    locale: str
    platform: str
    webgl_vendor: str
    webgl_renderer: str
    canvas_fingerprint: str
    audio_fingerprint: str
    fonts: List[str]
    plugins: List[Dict[str, str]]
    languages: List[str]
    hardware_concurrency: int
    device_memory: int
    color_depth: int
    pixel_ratio: float

class FingerprintManager:
    """浏览器指纹管理器"""

    def __init__(self, config_file: str = "config/fingerprint_config.json"):
        """
        初始化指纹管理器

        Args:
            config_file: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.config_file = config_file

        # 预定义的指纹数据
        self.user_agents = [
            # Windows Chrome
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",

            # Windows Edge
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0",

            # macOS Chrome
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",

            # macOS Safari
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",

            # Linux Chrome
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0",
        ]

        self.viewports = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1536, "height": 864},
            {"width": 1440, "height": 900},
            {"width": 1280, "height": 720},
            {"width": 1600, "height": 900},
            {"width": 2560, "height": 1440},
        ]

        self.screens = [
            {"width": 1920, "height": 1080, "colorDepth": 24},
            {"width": 1366, "height": 768, "colorDepth": 24},
            {"width": 1536, "height": 864, "colorDepth": 24},
            {"width": 1440, "height": 900, "colorDepth": 24},
            {"width": 2560, "height": 1440, "colorDepth": 24},
        ]

        self.timezones = [
            "Asia/Shanghai", "Asia/Beijing", "Asia/Chongqing",
            "America/New_York", "America/Los_Angeles", "America/Chicago",
            "Europe/London", "Europe/Paris", "Europe/Berlin",
            "Asia/Tokyo", "Asia/Seoul", "Australia/Sydney"
        ]

        self.locales = [
            "zh-CN", "zh-TW", "en-US", "en-GB", "ja-JP", "ko-KR"
        ]

        self.platforms = [
            "Win32", "MacIntel", "Linux x86_64"
        ]

        self.webgl_vendors = [
            "Google Inc. (Intel)", "Google Inc. (NVIDIA)", "Google Inc. (AMD)",
            "WebKit", "Mozilla", "Apple Inc."
        ]

        self.webgl_renderers = [
            "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)",
            "Intel Iris OpenGL Engine", "AMD Radeon Pro 560X OpenGL Engine",
            "NVIDIA GeForce GTX 1050 Ti OpenGL Engine"
        ]

        self.common_fonts = [
            "Arial", "Arial Black", "Arial Narrow", "Arial Unicode MS",
            "Calibri", "Cambria", "Candara", "Century Gothic", "Comic Sans MS",
            "Consolas", "Constantia", "Corbel", "Courier New", "Franklin Gothic Medium",
            "Garamond", "Georgia", "Impact", "Lucida Console", "Lucida Sans Unicode",
            "Microsoft Sans Serif", "Palatino Linotype", "Segoe UI", "Tahoma",
            "Times New Roman", "Trebuchet MS", "Verdana", "Webdings", "Wingdings"
        ]

        self.common_plugins = [
            {"name": "Chrome PDF Plugin", "filename": "internal-pdf-viewer"},
            {"name": "Chrome PDF Viewer", "filename": "mhjfbmdgcfjbbpaeojofohoefgiehjai"},
            {"name": "Native Client", "filename": "internal-nacl-plugin"},
        ]

        # 加载配置
        self._load_config()

    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 更新配置数据
                if 'user_agents' in config:
                    self.user_agents.extend(config['user_agents'])
                if 'viewports' in config:
                    self.viewports.extend(config['viewports'])

                self.logger.info(f"✅ 加载指纹配置文件成功")
            else:
                self.logger.warning(f"⚠️ 指纹配置文件不存在: {self.config_file}")
                self._create_default_config()

        except Exception as e:
            self.logger.error(f"❌ 加载指纹配置文件失败: {e}")
            self._create_default_config()

    def _create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "user_agents": [],
            "viewports": [],
            "custom_fingerprints": []
        }

        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            self.logger.info(f"✅ 创建默认指纹配置文件: {self.config_file}")
        except Exception as e:
            self.logger.error(f"❌ 创建默认指纹配置文件失败: {e}")

    def generate_fingerprint(self, platform_hint: str = None) -> BrowserFingerprint:
        """
        生成随机浏览器指纹

        Args:
            platform_hint: 平台提示 (windows, macos, linux)

        Returns:
            浏览器指纹对象
        """
        # 根据平台提示过滤用户代理
        if platform_hint:
            if platform_hint.lower() == "windows":
                user_agents = [ua for ua in self.user_agents if "Windows" in ua]
            elif platform_hint.lower() == "macos":
                user_agents = [ua for ua in self.user_agents if "Macintosh" in ua]
            elif platform_hint.lower() == "linux":
                user_agents = [ua for ua in self.user_agents if "Linux" in ua]
            else:
                user_agents = self.user_agents
        else:
            user_agents = self.user_agents

        user_agent = random.choice(user_agents)
        viewport = random.choice(self.viewports)
        screen = random.choice(self.screens)

        # 确保viewport不超过screen
        if viewport["width"] > screen["width"]:
            viewport["width"] = screen["width"]
        if viewport["height"] > screen["height"]:
            viewport["height"] = screen["height"]

        fingerprint = BrowserFingerprint(
            user_agent=user_agent,
            viewport=viewport,
            screen=screen,
            timezone=random.choice(self.timezones),
            locale=random.choice(self.locales),
            platform=self._get_platform_from_ua(user_agent),
            webgl_vendor=random.choice(self.webgl_vendors),
            webgl_renderer=random.choice(self.webgl_renderers),
            canvas_fingerprint=self._generate_canvas_fingerprint(),
            audio_fingerprint=self._generate_audio_fingerprint(),
            fonts=random.sample(self.common_fonts, random.randint(15, 25)),
            plugins=self.common_plugins.copy(),
            languages=self._generate_languages(),
            hardware_concurrency=random.choice([2, 4, 6, 8, 12, 16]),
            device_memory=random.choice([2, 4, 8, 16, 32]),
            color_depth=screen["colorDepth"],
            pixel_ratio=random.choice([1.0, 1.25, 1.5, 2.0])
        )

        self.logger.debug(f"🎭 生成新指纹: {user_agent[:50]}...")
        return fingerprint

    def _get_platform_from_ua(self, user_agent: str) -> str:
        """从用户代理字符串推断平台"""
        if "Windows" in user_agent:
            return "Win32"
        elif "Macintosh" in user_agent:
            return "MacIntel"
        elif "Linux" in user_agent:
            return "Linux x86_64"
        else:
            return "Win32"  # 默认

    def _generate_canvas_fingerprint(self) -> str:
        """生成Canvas指纹"""
        # 模拟Canvas渲染结果的哈希值
        import hashlib
        random_data = f"{random.random()}{random.randint(1000, 9999)}"
        return hashlib.md5(random_data.encode()).hexdigest()[:16]

    def _generate_audio_fingerprint(self) -> str:
        """生成音频指纹"""
        # 模拟音频上下文指纹
        import hashlib
        random_data = f"audio_{random.random()}{random.randint(1000, 9999)}"
        return hashlib.md5(random_data.encode()).hexdigest()[:16]

    def _generate_languages(self) -> List[str]:
        """生成语言列表"""
        base_languages = ["zh-CN", "zh", "en-US", "en"]
        additional = ["ja", "ko", "fr", "de", "es"]

        # 随机添加一些额外语言
        extra_count = random.randint(0, 3)
        extra_languages = random.sample(additional, extra_count)

        return base_languages + extra_languages

    def get_anti_detection_script(self, fingerprint: BrowserFingerprint) -> str:
        """
        生成反检测脚本

        Args:
            fingerprint: 浏览器指纹

        Returns:
            JavaScript反检测脚本
        """
        script = f"""
        // 移除webdriver属性
        Object.defineProperty(navigator, 'webdriver', {{
            get: () => undefined,
        }});

        // 伪造navigator属性
        Object.defineProperty(navigator, 'platform', {{
            get: () => '{fingerprint.platform}',
        }});

        Object.defineProperty(navigator, 'hardwareConcurrency', {{
            get: () => {fingerprint.hardware_concurrency},
        }});

        Object.defineProperty(navigator, 'deviceMemory', {{
            get: () => {fingerprint.device_memory},
        }});

        Object.defineProperty(navigator, 'languages', {{
            get: () => {json.dumps(fingerprint.languages)},
        }});

        // 伪造screen属性
        Object.defineProperty(screen, 'width', {{
            get: () => {fingerprint.screen['width']},
        }});

        Object.defineProperty(screen, 'height', {{
            get: () => {fingerprint.screen['height']},
        }});

        Object.defineProperty(screen, 'colorDepth', {{
            get: () => {fingerprint.color_depth},
        }});

        Object.defineProperty(window, 'devicePixelRatio', {{
            get: () => {fingerprint.pixel_ratio},
        }});

        // 伪造WebGL指纹
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {{
            if (parameter === 37445) {{
                return '{fingerprint.webgl_vendor}';
            }}
            if (parameter === 37446) {{
                return '{fingerprint.webgl_renderer}';
            }}
            return getParameter.call(this, parameter);
        }};

        // 伪造Canvas指纹
        const toDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function() {{
            const result = toDataURL.apply(this, arguments);
            // 添加轻微的随机变化
            return result.replace(/.$/, '{random.choice("0123456789abcdef")}');
        }};

        // 伪造字体检测
        const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
        const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');

        // 移除Chrome对象
        if (window.chrome) {{
            delete window.chrome;
        }}

        // 伪造插件
        Object.defineProperty(navigator, 'plugins', {{
            get: () => {json.dumps(fingerprint.plugins)},
        }});

        // 伪造时区
        const originalDateTimeFormat = Intl.DateTimeFormat;
        Intl.DateTimeFormat = function(...args) {{
            if (args.length === 0) {{
                args = ['{fingerprint.locale}', {{ timeZone: '{fingerprint.timezone}' }}];
            }}
            return new originalDateTimeFormat(...args);
        }};

        console.log('🎭 反检测脚本已加载');
        """

        return script

    def get_context_options(self, fingerprint: BrowserFingerprint) -> Dict:
        """
        获取浏览器上下文选项

        Args:
            fingerprint: 浏览器指纹

        Returns:
            上下文选项字典
        """
        return {
            'user_agent': fingerprint.user_agent,
            'viewport': fingerprint.viewport,
            'locale': fingerprint.locale,
            'timezone_id': fingerprint.timezone,
            'permissions': ['geolocation'],
            'extra_http_headers': {
                'Accept-Language': f"{fingerprint.locale},{fingerprint.locale.split('-')[0]};q=0.9,en;q=0.8",
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Sec-Ch-Ua': self._generate_sec_ch_ua(fingerprint.user_agent),
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': f'"{fingerprint.platform}"',
            }
        }

    def _generate_sec_ch_ua(self, user_agent: str) -> str:
        """生成Sec-CH-UA头"""
        if "Chrome" in user_agent:
            version = "120"  # 可以从user_agent中提取
            return f'"Not_A Brand";v="8", "Chromium";v="{version}", "Google Chrome";v="{version}"'
        elif "Edge" in user_agent:
            version = "120"
            return f'"Not_A Brand";v="8", "Chromium";v="{version}", "Microsoft Edge";v="{version}"'
        else:
            return '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"'

# 全局指纹管理器实例
_fingerprint_manager: Optional[FingerprintManager] = None

def get_fingerprint_manager(config_file: str = "config/fingerprint_config.json") -> FingerprintManager:
    """
    获取全局指纹管理器实例

    Args:
        config_file: 配置文件路径

    Returns:
        指纹管理器实例
    """
    global _fingerprint_manager
    if _fingerprint_manager is None:
        _fingerprint_manager = FingerprintManager(config_file)
    return _fingerprint_manager