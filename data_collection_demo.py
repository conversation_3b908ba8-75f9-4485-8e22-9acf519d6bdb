#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据收集测试脚本
测试爬虫系统的真实数据收集能力，不包含任何模拟数据
"""

import sys
import os
from pathlib import Path
import pandas as pd
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_real_data_collection():
    """测试真实数据收集功能"""
    print("🚀 启动真实数据收集测试")
    print("=" * 80)
    print("⚠️  注意：此测试只收集真实数据，不生成任何模拟数据")
    print("=" * 80)

    try:
        # 导入必要的模块
        from src.enhanced_real_crawler import EnhancedRealCrawler

        print("✅ 模块导入成功")

        # 初始化增强爬虫
        print("\n🔧 初始化真实数据爬虫...")
        crawler = EnhancedRealCrawler()
        print("✅ 真实数据爬虫初始化完成")

        # 测试参数设置 - 使用更具体的搜索条件
        test_cases = [
            {
                "industry": "酒店管理",
                "province": "北京市",
                "city": "朝阳区",
                "max_results": 20
            },
            {
                "industry": "房地产开发",
                "province": "上海市",
                "city": "浦东新区",
                "max_results": 20
            },
            {
                "industry": "建筑装饰",
                "province": "广东省",
                "city": "深圳市",
                "max_results": 20
            }
        ]

        all_real_results = []
        total_real_count = 0

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试案例 {i}: {test_case['industry']}")
            print(f"   地区: {test_case['province']} {test_case['city']}")
            print(f"   目标数量: {test_case['max_results']}")

            try:
                # 执行真实数据收集
                print("   🔍 开始真实数据收集...")
                results = crawler.search_real_business_data(
                    industry=test_case['industry'],
                    province=test_case['province'],
                    city=test_case['city'],
                    max_results=test_case['max_results']
                )

                # 过滤掉任何可能的模拟数据
                real_results = []
                if hasattr(results, 'empty') and not results.empty:  # DataFrame
                    # 过滤掉模拟数据来源
                    filtered_df = results[~results['source'].str.contains('demo|mock|fake|simulate|pattern|generated', case=False, na=False)]
                    if not filtered_df.empty:
                        real_results = filtered_df
                        all_real_results.append(filtered_df)
                elif isinstance(results, list) and results:  # List
                    # 过滤掉模拟数据来源
                    filtered_list = [item for item in results
                                   if not any(keyword in str(item.get('source', '')).lower()
                                            for keyword in ['demo', 'mock', 'fake', 'simulate', 'pattern', 'generated'])]
                    if filtered_list:
                        real_results = filtered_list
                        df = pd.DataFrame(filtered_list)
                        all_real_results.append(df)

                real_count = len(real_results) if real_results is not None else 0
                total_real_count += real_count

                if real_count > 0:
                    print(f"   ✅ 真实数据收集完成: 获得 {real_count} 条真实数据")

                    # 显示真实数据样例
                    print("   📋 真实数据样例:")
                    if hasattr(real_results, 'empty'):  # DataFrame
                        for idx, (_, row) in enumerate(real_results.head(3).iterrows(), 1):
                            print(f"      {idx}. {row.get('company_name', 'N/A')}")
                            print(f"         来源: {row.get('source', 'N/A')}")
                            print(f"         数据类型: {row.get('data_type', 'N/A')}")
                    elif isinstance(real_results, list):  # List
                        for idx, item in enumerate(real_results[:3], 1):
                            print(f"      {idx}. {item.get('company_name', 'N/A')}")
                            print(f"         来源: {item.get('source', 'N/A')}")
                            print(f"         数据类型: {item.get('data_type', 'N/A')}")
                else:
                    print(f"   ⚠️ 未收集到真实数据")

                # 添加延时避免过于频繁的请求
                time.sleep(3)

            except Exception as e:
                print(f"   ❌ 真实数据收集失败: {str(e)}")
                continue
        
        # 汇总结果
        print(f"\n📊 真实数据收集汇总")
        print("=" * 60)

        if all_real_results:
            # 合并所有真实结果
            combined_df = pd.concat(all_real_results, ignore_index=True)
            
            print(f"✅ 总计收集数据: {len(combined_df)} 条")
            print(f"📈 数据来源分布:")
            
            if 'source' in combined_df.columns:
                source_counts = combined_df['source'].value_counts()
                for source, count in source_counts.items():
                    print(f"   - {source}: {count} 条")
            
            print(f"\n🏢 行业分布:")
            if 'industry' in combined_df.columns:
                industry_counts = combined_df['industry'].value_counts()
                for industry, count in industry_counts.items():
                    print(f"   - {industry}: {count} 条")
            
            # 保存真实数据到文件
            output_file = "data/real_data_collection.csv"
            os.makedirs("data", exist_ok=True)
            combined_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n💾 真实数据已保存到: {output_file}")

            # 显示详细真实数据样例
            print(f"\n📋 详细真实数据样例 (前5条):")
            print("-" * 80)
            for idx, (_, row) in enumerate(combined_df.head(5).iterrows(), 1):
                print(f"{idx}. 企业名称: {row.get('company_name', 'N/A')}")
                print(f"   行业: {row.get('industry', 'N/A')}")
                print(f"   来源: {row.get('source', 'N/A')}")
                print(f"   数据类型: {row.get('data_type', 'N/A')}")
                if row.get('contact'):
                    print(f"   联系方式: {row.get('contact')}")
                if row.get('address'):
                    print(f"   地址: {row.get('address')}")
                print()

            return True, len(combined_df)
        else:
            print("❌ 未收集到任何真实数据")
            print("💡 建议：检查网络连接和数据源配置")
            return False, 0
            
    except Exception as e:
        print(f"❌ 数据收集测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, 0

def test_real_crawler_integration():
    """测试真实爬虫集成的数据收集"""
    print(f"\n🔍 测试真实爬虫集成数据收集")
    print("=" * 60)
    
    try:
        from real_crawler_integration import crawl_real_data
        
        # 测试企业信息查询
        test_companies = [
            "万达集团",
            "恒大地产", 
            "碧桂园",
            "中建集团",
            "中铁建设"
        ]
        
        collected_data = []
        
        for company in test_companies:
            print(f"\n🏢 查询企业: {company}")
            try:
                result = crawl_real_data("company", company)
                
                if result['success'] and result.get('data'):
                    data_count = len(result['data'])
                    print(f"   ✅ 查询成功: 获得 {data_count} 条数据")
                    collected_data.extend(result['data'])
                else:
                    print(f"   ⚠️ 查询失败: {result.get('message', '未知错误')}")
                    
                time.sleep(1)  # 添加延时
                
            except Exception as e:
                print(f"   ❌ 查询出错: {str(e)}")
                continue
        
        if collected_data:
            print(f"\n📊 企业查询汇总: 共收集 {len(collected_data)} 条数据")
            
            # 保存企业数据
            df = pd.DataFrame(collected_data)
            output_file = "data/company_data_demo.csv"
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"💾 企业数据已保存到: {output_file}")
            
            return True, len(collected_data)
        else:
            print("❌ 未收集到企业数据")
            return False, 0
            
    except Exception as e:
        print(f"❌ 企业查询测试失败: {str(e)}")
        return False, 0

def main():
    """主函数"""
    print("🎯 数据收集能力演示")
    print("=" * 80)
    print("本演示将测试爬虫系统的实际数据收集能力")
    print("=" * 80)
    
    # 1. 测试真实数据收集
    success1, count1 = test_real_data_collection()
    
    # 2. 测试真实爬虫集成
    success2, count2 = test_real_crawler_integration()
    
    # 总结
    print(f"\n🎉 数据收集演示完成")
    print("=" * 80)
    print(f"📊 收集结果统计:")
    print(f"   增强爬虫: {'成功' if success1 else '失败'} - {count1} 条数据")
    print(f"   企业查询: {'成功' if success2 else '失败'} - {count2} 条数据")
    print(f"   总计数据: {count1 + count2} 条")
    
    if success1 or success2:
        print(f"\n✅ 爬虫系统具备数据收集能力!")
        print(f"💡 建议: 可以通过优化配置和增加数据源来提升收集效果")
    else:
        print(f"\n⚠️ 数据收集能力有限，需要进一步优化")
    
    print(f"\n📁 数据文件位置:")
    print(f"   - data/collected_data_demo.csv (综合数据)")
    print(f"   - data/company_data_demo.csv (企业数据)")

if __name__ == "__main__":
    main()
