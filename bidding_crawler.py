#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
招投标数据爬虫 - 真实招投标信息抓取
从真实招投标网站抓取项目信息
"""

import sys
import os
import time
import re
import json
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
from urllib.parse import quote, urljoin
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import random

class BiddingCrawler:
    """招投标数据爬虫"""
    
    def __init__(self):
        """初始化爬虫"""
        self.session = requests.Session()
        self.driver = None
        self.setup_session()
        self.setup_browser()
        
        # 真实招投标网站配置
        self.bidding_sites = {
            'chinabidding': {
                'name': '中国招标网',
                'base_url': 'https://www.chinabidding.com.cn',
                'search_url': 'https://www.chinabidding.com.cn/search/searchzbgg',
                'enabled': True
            },
            'zbytb': {
                'name': '中国招标投标网',
                'base_url': 'https://www.zbytb.com',
                'search_url': 'https://www.zbytb.com/search.aspx',
                'enabled': True
            },
            'okcis': {
                'name': '中国采购与招标网',
                'base_url': 'https://www.okcis.cn',
                'search_url': 'https://www.okcis.cn/search',
                'enabled': True
            },
            'ccgp': {
                'name': '中国政府采购网',
                'base_url': 'http://www.ccgp.gov.cn',
                'search_url': 'http://search.ccgp.gov.cn/bxsearch',
                'enabled': True
            }
        }
        
        print("🚀 招投标数据爬虫初始化完成")
    
    def setup_session(self):
        """设置HTTP会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session.headers.update(headers)
        self.session.timeout = 30
    
    def setup_browser(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 禁用图片加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ 浏览器初始化成功")
            
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            self.driver = None
    
    def crawl_chinabidding(self, keyword: str, region: str = None, max_pages: int = 3) -> List[Dict[str, Any]]:
        """从中国招标网爬取招投标信息"""
        print(f"🔍 正在从中国招标网爬取: {keyword}")
        
        if not self.driver:
            print("❌ 需要浏览器支持")
            return []
        
        projects = []
        
        try:
            for page in range(1, max_pages + 1):
                print(f"📄 正在爬取第 {page} 页...")
                
                # 构建搜索URL
                search_url = f"https://www.chinabidding.com.cn/search/searchzbgg?rp=25&keywords={quote(keyword)}&page={page}"
                if region:
                    search_url += f"&region={quote(region)}"
                
                # 访问页面
                self.driver.get(search_url)
                time.sleep(random.uniform(3, 5))
                
                # 等待页面加载
                try:
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".zb_result, .search-result"))
                    )
                except TimeoutException:
                    print(f"⚠️ 第 {page} 页加载超时")
                    continue
                
                # 解析页面数据
                page_projects = self._parse_chinabidding_page()
                projects.extend(page_projects)
                
                print(f"✅ 第 {page} 页获取到 {len(page_projects)} 条数据")
                
                # 随机延迟
                time.sleep(random.uniform(4, 7))
            
            print(f"🎉 中国招标网爬取完成，共获取 {len(projects)} 条招投标信息")
            return projects
            
        except Exception as e:
            print(f"❌ 中国招标网爬取失败: {e}")
            return projects
    
    def _parse_chinabidding_page(self) -> List[Dict[str, Any]]:
        """解析中国招标网页面数据"""
        projects = []
        
        try:
            # 获取页面HTML
            html = self.driver.page_source
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找项目列表
            project_items = soup.find_all('div', class_='zb_result_item')
            if not project_items:
                project_items = soup.find_all('li', class_='search-result-item')
            
            for item in project_items:
                try:
                    # 提取项目标题
                    title_element = item.find('a', class_='title') or item.find('h3') or item.find('a')
                    if not title_element:
                        continue
                    
                    project_title = title_element.get_text(strip=True)
                    detail_url = title_element.get('href', '')
                    
                    # 提取发布时间
                    date_element = item.find('span', class_='date') or item.find('time')
                    publish_date = date_element.get_text(strip=True) if date_element else ""
                    
                    # 提取项目类型
                    type_element = item.find('span', class_='type')
                    project_type = type_element.get_text(strip=True) if type_element else ""
                    
                    # 提取地区信息
                    region_element = item.find('span', class_='region')
                    project_region = region_element.get_text(strip=True) if region_element else ""
                    
                    # 提取项目描述
                    desc_element = item.find('div', class_='desc') or item.find('p')
                    description = desc_element.get_text(strip=True) if desc_element else ""
                    
                    # 提取招标单位
                    unit_pattern = r'招标人[：:]\s*([^\s\n]+)'
                    bidding_unit = self._extract_info(item.get_text(), unit_pattern)
                    
                    # 提取项目金额
                    amount_pattern = r'预算金额[：:]?\s*([0-9.,]+\s*[万元|元|万|亿])'
                    project_amount = self._extract_info(item.get_text(), amount_pattern)
                    
                    if project_title:
                        project_info = {
                            'project_title': project_title,
                            'project_type': project_type,
                            'bidding_unit': bidding_unit,
                            'project_amount': project_amount,
                            'project_region': project_region,
                            'publish_date': publish_date,
                            'description': description[:200] + '...' if len(description) > 200 else description,
                            'detail_url': urljoin('https://www.chinabidding.com.cn', detail_url),
                            'source': '中国招标网',
                            'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S')
                        }
                        projects.append(project_info)
                        
                except Exception as e:
                    print(f"⚠️ 解析项目信息失败: {e}")
                    continue
            
        except Exception as e:
            print(f"❌ 页面解析失败: {e}")
        
        return projects
    
    def crawl_ccgp(self, keyword: str, region: str = None, max_pages: int = 3) -> List[Dict[str, Any]]:
        """从中国政府采购网爬取采购信息"""
        print(f"🔍 正在从中国政府采购网爬取: {keyword}")
        
        projects = []
        
        try:
            for page in range(1, max_pages + 1):
                print(f"📄 正在爬取第 {page} 页...")
                
                # 使用HTTP请求方式
                search_url = "http://search.ccgp.gov.cn/bxsearch"
                params = {
                    'searchtype': '1',
                    'page_index': str(page),
                    'bidSort': '0',
                    'buyerName': '',
                    'projectId': '',
                    'pinMu': '0',
                    'bidType': '0',
                    'dbselect': 'bidx',
                    'kw': keyword,
                    'start_time': '',
                    'end_time': '',
                    'timeType': '6',
                    'displayZone': '',
                    'zoneId': '',
                    'pppStatus': '0',
                    'agentName': ''
                }
                
                if region:
                    params['displayZone'] = region
                
                try:
                    response = self.session.get(search_url, params=params)
                    if response.status_code == 200:
                        page_projects = self._parse_ccgp_response(response.text)
                        projects.extend(page_projects)
                        print(f"✅ 第 {page} 页获取到 {len(page_projects)} 条数据")
                    else:
                        print(f"❌ 第 {page} 页请求失败: {response.status_code}")
                        
                except Exception as e:
                    print(f"❌ 第 {page} 页请求异常: {e}")
                
                # 随机延迟
                time.sleep(random.uniform(2, 4))
            
            print(f"🎉 中国政府采购网爬取完成，共获取 {len(projects)} 条采购信息")
            return projects
            
        except Exception as e:
            print(f"❌ 中国政府采购网爬取失败: {e}")
            return projects
    
    def _parse_ccgp_response(self, html: str) -> List[Dict[str, Any]]:
        """解析中国政府采购网响应数据"""
        projects = []
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找项目列表
            project_items = soup.find_all('tr', class_='') or soup.find_all('li')
            
            for item in project_items:
                try:
                    # 提取项目标题
                    title_element = item.find('a')
                    if not title_element:
                        continue
                    
                    project_title = title_element.get_text(strip=True)
                    detail_url = title_element.get('href', '')
                    
                    # 提取其他信息
                    item_text = item.get_text()
                    
                    # 提取发布时间
                    date_pattern = r'(\d{4}-\d{2}-\d{2})'
                    publish_date = self._extract_info(item_text, date_pattern)
                    
                    # 提取采购单位
                    unit_pattern = r'采购人[：:]\s*([^\s\n]+)'
                    purchasing_unit = self._extract_info(item_text, unit_pattern)
                    
                    # 提取项目金额
                    amount_pattern = r'预算金额[：:]?\s*([0-9.,]+\s*[万元|元|万|亿])'
                    project_amount = self._extract_info(item_text, amount_pattern)
                    
                    if project_title and len(project_title) > 5:
                        project_info = {
                            'project_title': project_title,
                            'project_type': '政府采购',
                            'purchasing_unit': purchasing_unit,
                            'project_amount': project_amount,
                            'publish_date': publish_date,
                            'detail_url': urljoin('http://www.ccgp.gov.cn', detail_url),
                            'source': '中国政府采购网',
                            'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S')
                        }
                        projects.append(project_info)
                        
                except Exception as e:
                    print(f"⚠️ 解析项目信息失败: {e}")
                    continue
            
        except Exception as e:
            print(f"❌ 响应解析失败: {e}")
        
        return projects
    
    def _extract_info(self, text: str, pattern: str) -> str:
        """使用正则表达式提取信息"""
        match = re.search(pattern, text)
        return match.group(1).strip() if match else ""
    
    def crawl_bidding_projects(self, keyword: str, region: str = None, max_results: int = 100) -> List[Dict[str, Any]]:
        """爬取招投标项目主函数"""
        print(f"🎯 开始爬取招投标项目")
        print(f"关键词: {keyword}")
        print(f"地区: {region or '全国'}")
        print(f"目标数量: {max_results}")
        print("=" * 60)
        
        all_projects = []
        
        # 计算每个源需要爬取的页数
        max_pages_per_source = max(1, max_results // 40)  # 假设每页约20条数据
        
        # 1. 从中国招标网爬取
        if self.bidding_sites['chinabidding']['enabled']:
            try:
                chinabidding_data = self.crawl_chinabidding(keyword, region, max_pages_per_source)
                all_projects.extend(chinabidding_data)
            except Exception as e:
                print(f"❌ 中国招标网爬取异常: {e}")
        
        # 2. 从中国政府采购网爬取
        if self.bidding_sites['ccgp']['enabled'] and len(all_projects) < max_results:
            try:
                ccgp_data = self.crawl_ccgp(keyword, region, max_pages_per_source)
                all_projects.extend(ccgp_data)
            except Exception as e:
                print(f"❌ 中国政府采购网爬取异常: {e}")
        
        # 数据去重
        unique_projects = self._deduplicate_projects(all_projects)
        
        # 限制结果数量
        final_results = unique_projects[:max_results]
        
        print(f"\n📊 爬取结果统计:")
        print(f"  原始数据: {len(all_projects)} 条")
        print(f"  去重后: {len(unique_projects)} 条")
        print(f"  最终结果: {len(final_results)} 条")
        
        return final_results
    
    def _deduplicate_projects(self, projects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """项目数据去重"""
        seen_titles = set()
        unique_projects = []
        
        for project in projects:
            title = project.get('project_title', '').strip()
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_projects.append(project)
        
        return unique_projects
    
    def save_data(self, data: List[Dict[str, Any]], filename: str = None):
        """保存数据"""
        if not data:
            print("❌ 没有数据需要保存")
            return
        
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"bidding_projects_{timestamp}"
        
        # 保存为JSON
        json_file = f"{filename}.json"
        try:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ JSON数据已保存: {json_file}")
        except Exception as e:
            print(f"❌ 保存JSON失败: {e}")
        
        # 保存为CSV
        csv_file = f"{filename}.csv"
        try:
            df = pd.DataFrame(data)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"✅ CSV数据已保存: {csv_file}")
        except Exception as e:
            print(f"❌ 保存CSV失败: {e}")
    
    def close(self):
        """关闭资源"""
        if self.driver:
            self.driver.quit()
            print("✅ 浏览器已关闭")

def main():
    """主函数"""
    crawler = BiddingCrawler()
    
    try:
        # 测试爬取
        test_keywords = ["建筑工程", "酒店装修", "房地产开发"]
        test_regions = ["北京", "上海", "深圳"]
        
        for i, keyword in enumerate(test_keywords):
            region = test_regions[i] if i < len(test_regions) else None
            
            print(f"\n{'='*80}")
            print(f"🧪 测试爬取: {keyword} - {region or '全国'}")
            print(f"{'='*80}")
            
            # 爬取数据
            projects = crawler.crawl_bidding_projects(keyword, region, max_results=30)
            
            if projects:
                print(f"\n📋 爬取到的招投标项目:")
                for j, project in enumerate(projects[:5], 1):
                    print(f"  {j}. {project['project_title']}")
                    print(f"     来源: {project['source']}")
                    print(f"     类型: {project.get('project_type', 'N/A')}")
                    print(f"     金额: {project.get('project_amount', 'N/A')}")
                    print(f"     发布: {project.get('publish_date', 'N/A')}")
                    print()
                
                # 保存数据
                filename = f"real_bidding_{keyword}_{region or 'nationwide'}"
                crawler.save_data(projects, filename)
            else:
                print("❌ 未爬取到数据")
    
    finally:
        crawler.close()

if __name__ == "__main__":
    main()
