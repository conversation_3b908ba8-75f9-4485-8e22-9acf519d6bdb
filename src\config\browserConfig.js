const { chromium, firefox, webkit } = require('playwright');
require('dotenv').config();

class BrowserManager {
    constructor() {
        this.browsers = new Map();
        this.userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ];
    }

    getRandomUserAgent() {
        return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
    }

    async createBrowser(platform = 'chromium', options = {}) {
        const browserOptions = {
            headless: process.env.HEADLESS === 'true',
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ],
            ...options
        };

        // 添加代理配置
        if (process.env.PROXY_HOST && process.env.PROXY_PORT) {
            browserOptions.proxy = {
                server: `http://${process.env.PROXY_HOST}:${process.env.PROXY_PORT}`,
                username: process.env.PROXY_USERNAME,
                password: process.env.PROXY_PASSWORD
            };
        }

        let browser;
        switch (platform) {
            case 'firefox':
                browser = await firefox.launch(browserOptions);
                break;
            case 'webkit':
                browser = await webkit.launch(browserOptions);
                break;
            default:
                browser = await chromium.launch(browserOptions);
        }

        this.browsers.set(browser, platform);
        return browser;
    }

    async createPage(browser, options = {}) {
        const page = await browser.newPage({
            userAgent: this.getRandomUserAgent(),
            viewport: { width: 1920, height: 1080 },
            ...options
        });

        // 设置请求拦截
        await page.route('**/*', (route) => {
            const resourceType = route.request().resourceType();
            if (['image', 'stylesheet', 'font'].includes(resourceType)) {
                route.abort();
            } else {
                route.continue();
            }
        });

        return page;
    }

    async closeBrowser(browser) {
        if (this.browsers.has(browser)) {
            await browser.close();
            this.browsers.delete(browser);
        }
    }

    async closeAllBrowsers() {
        for (const browser of this.browsers.keys()) {
            await browser.close();
        }
        this.browsers.clear();
    }
}

module.exports = BrowserManager;
