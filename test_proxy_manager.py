#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理池管理器测试脚本
验证代理池的各项功能
"""

import asyncio
import sys
import logging
from src.proxy_manager import ProxyManager, get_proxy_manager, ProxyInfo, ProxyStatus
from src.browser_manager import get_browser_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_proxy_manager_basic():
    """测试代理管理器基础功能"""
    print("🔍 测试代理管理器基础功能...")
    
    try:
        # 创建代理管理器
        proxy_manager = ProxyManager()
        print("✅ 代理管理器创建成功")
        
        # 初始化代理池
        await proxy_manager.initialize()
        print("✅ 代理池初始化完成")
        
        # 获取统计信息
        stats = proxy_manager.get_proxy_stats()
        print(f"📊 代理池统计:")
        print(f"   总数: {stats['total']}")
        print(f"   可用: {stats['active']}")
        print(f"   失败: {stats['failed']}")
        print(f"   成功率: {stats['success_rate']:.2%}")
        print(f"   平均响应时间: {stats['avg_response_time']:.2f}s")
        
        # 测试获取代理
        proxy = await proxy_manager.get_proxy()
        if proxy:
            print(f"✅ 获取代理成功: {proxy.host}:{proxy.port}")
        else:
            print("⚠️ 没有可用代理")
        
        return True
        
    except Exception as e:
        print(f"❌ 代理管理器测试失败: {e}")
        return False

async def test_proxy_operations():
    """测试代理操作功能"""
    print("\n🔍 测试代理操作功能...")
    
    try:
        proxy_manager = ProxyManager()
        
        # 添加测试代理
        proxy_manager.add_proxy("127.0.0.1", 8888, protocol="http")
        proxy_manager.add_proxy("127.0.0.1", 9999, protocol="http")
        print("✅ 添加测试代理成功")
        
        # 获取统计信息
        stats = proxy_manager.get_proxy_stats()
        print(f"📊 添加后代理数量: {stats['total']}")
        
        # 移除代理
        proxy_manager.remove_proxy("127.0.0.1", 9999)
        print("✅ 移除代理成功")
        
        # 再次获取统计信息
        stats = proxy_manager.get_proxy_stats()
        print(f"📊 移除后代理数量: {stats['total']}")
        
        # 保存配置
        proxy_manager.save_config()
        print("✅ 配置保存成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 代理操作测试失败: {e}")
        return False

async def test_browser_with_proxy():
    """测试浏览器与代理集成"""
    print("\n🔍 测试浏览器与代理集成...")
    
    try:
        # 创建支持代理的浏览器管理器
        browser_manager = get_browser_manager(headless=True, use_proxy=False)  # 先不使用代理测试
        print("✅ 浏览器管理器创建成功")
        
        # 启动浏览器
        await browser_manager.start()
        
        # 创建浏览器实例
        browser = await browser_manager.get_or_create_browser()
        
        # 创建上下文（不使用代理）
        context = await browser_manager.create_context(browser)
        print("✅ 浏览器上下文创建成功")
        
        # 创建页面并测试
        page = await browser_manager.create_page(context)
        await page.goto("http://httpbin.org/ip", timeout=15000)
        
        # 获取IP信息
        ip_info = await page.evaluate('document.body.textContent')
        print(f"✅ 当前IP信息: {ip_info.strip()}")
        
        # 清理
        await page.close()
        await browser_manager.close_context(context)
        
        from src.browser_manager import cleanup_browser_manager
        await cleanup_browser_manager()
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器代理集成测试失败: {e}")
        return False

async def test_proxy_validation():
    """测试代理验证功能"""
    print("\n🔍 测试代理验证功能...")
    
    try:
        # 创建测试代理
        test_proxy = ProxyInfo(
            host="127.0.0.1",
            port=8080,
            protocol="http"
        )
        
        proxy_manager = ProxyManager()
        
        # 验证代理
        result = await proxy_manager._validate_proxy(test_proxy)
        print(f"✅ 代理验证结果: {result}")
        print(f"   状态: {test_proxy.status}")
        print(f"   响应时间: {test_proxy.response_time:.2f}s")
        print(f"   成功次数: {test_proxy.success_count}")
        print(f"   失败次数: {test_proxy.failure_count}")
        
        # 测试标记功能
        await proxy_manager.mark_proxy_success(test_proxy)
        print("✅ 标记代理成功")
        
        await proxy_manager.mark_proxy_failed(test_proxy, "测试失败")
        print("✅ 标记代理失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 代理验证测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("代理池管理器功能测试")
    print("=" * 60)
    
    async def run_all_tests():
        # 测试基础功能
        test1 = await test_proxy_manager_basic()
        
        # 测试代理操作
        test2 = await test_proxy_operations()
        
        # 测试浏览器集成
        test3 = await test_browser_with_proxy()
        
        # 测试代理验证
        test4 = await test_proxy_validation()
        
        return test1, test2, test3, test4
    
    # 运行所有测试
    results = asyncio.run(run_all_tests())
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 3:  # 至少3个测试通过
        print("✅ 代理池管理器基本功能正常")
        print("💡 建议：可以配置真实代理进行进一步测试")
        sys.exit(0)
    else:
        print("❌ 代理池管理器测试失败，需要检查实现")
        sys.exit(1)

if __name__ == "__main__":
    main()
