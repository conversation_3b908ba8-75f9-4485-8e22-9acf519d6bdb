#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音真实数据爬虫
"""

import asyncio
import json
import re
from typing import Dict, List, Optional
from urllib.parse import urlparse, parse_qs

from base_crawler import BaseCrawler

class DouyinCrawler(BaseCrawler):
    """抖音数据爬虫"""
    
    def __init__(self):
        super().__init__("douyin")
        self.base_url = "https://www.douyin.com"
        
    async def crawl_user_profile(self, user_url: str) -> Dict:
        """爬取用户主页信息"""
        try:
            page = await self.create_page()
            
            print(f"🔍 正在访问抖音用户页面: {user_url}")
            await page.goto(user_url, wait_until='networkidle')
            
            # 等待页面加载
            await self.wait_random(3, 5)
            
            # 检查是否遇到反爬
            if await self.check_anti_bot(page):
                if not await self.handle_captcha(page):
                    raise Exception("验证码处理失败")
            
            # 提取用户基本信息
            user_info = await self._extract_user_info(page)
            
            # 滚动加载更多视频
            await self.scroll_page(page, scrolls=5)
            
            # 提取视频列表
            videos = await self._extract_video_list(page)
            
            result = {
                'user_info': user_info,
                'videos': videos,
                'crawl_time': self._get_timestamp(),
                'source_url': user_url,
                'total_videos': len(videos)
            }
            
            # 保存数据
            self.save_data(result, f"douyin_user_{user_info.get('username', 'unknown')}")
            
            self.log_result(True, f"成功爬取用户 {user_info.get('username', 'unknown')}", len(videos))
            return result
            
        except Exception as e:
            self.log_result(False, f"用户爬取失败: {str(e)}")
            raise e
        finally:
            await self.close()

    async def _extract_user_info(self, page) -> Dict:
        """提取用户基本信息"""
        user_info = await page.evaluate("""
            () => {
                // 尝试多种选择器
                const selectors = {
                    username: [
                        '[data-e2e="user-title"]',
                        '.account-name',
                        '.user-name',
                        'h1'
                    ],
                    description: [
                        '[data-e2e="user-desc"]',
                        '.account-desc',
                        '.user-desc'
                    ],
                    followers: [
                        '[data-e2e="followers-count"]',
                        '.count:nth-child(2)',
                        '.follower-count'
                    ],
                    following: [
                        '[data-e2e="following-count"]',
                        '.count:nth-child(3)',
                        '.following-count'
                    ],
                    likes: [
                        '[data-e2e="likes-count"]',
                        '.count:nth-child(1)',
                        '.like-count'
                    ]
                };
                
                function getTextBySelectors(selectorList) {
                    for (const selector of selectorList) {
                        const element = document.querySelector(selector);
                        if (element) {
                            return element.textContent.trim();
                        }
                    }
                    return '';
                }
                
                return {
                    username: getTextBySelectors(selectors.username),
                    description: getTextBySelectors(selectors.description),
                    followers: getTextBySelectors(selectors.followers),
                    following: getTextBySelectors(selectors.following),
                    likes: getTextBySelectors(selectors.likes),
                    avatar: document.querySelector('img[alt*="头像"], .avatar img')?.src || ''
                };
            }
        """)
        
        print(f"✅ 用户信息: {user_info.get('username', '未知用户')}")
        return user_info

    async def _extract_video_list(self, page) -> List[Dict]:
        """提取视频列表"""
        videos = await page.evaluate("""
            () => {
                // 尝试多种视频容器选择器
                const videoSelectors = [
                    '[data-e2e="user-post-item"]',
                    '.video-feed-item',
                    '.aweme-video-item',
                    '.video-item'
                ];
                
                let videoElements = [];
                for (const selector of videoSelectors) {
                    videoElements = document.querySelectorAll(selector);
                    if (videoElements.length > 0) break;
                }
                
                const videos = [];
                
                videoElements.forEach((element, index) => {
                    try {
                        const linkEl = element.querySelector('a');
                        const imgEl = element.querySelector('img');
                        const titleEl = element.querySelector('[data-e2e="video-title"], .video-title, .aweme-video-title');
                        const statsEl = element.querySelector('.video-count, .video-stats, .aweme-video-stats');
                        const durationEl = element.querySelector('.duration, .video-duration');
                        
                        const video = {
                            id: index + 1,
                            title: titleEl?.textContent?.trim() || imgEl?.alt || `视频${index + 1}`,
                            url: linkEl?.href || '',
                            cover: imgEl?.src || '',
                            duration: durationEl?.textContent?.trim() || '',
                            stats: statsEl?.textContent?.trim() || '',
                            extract_time: new Date().toISOString()
                        };
                        
                        // 只添加有效的视频
                        if (video.url && video.url.includes('douyin.com')) {
                            videos.push(video);
                        }
                    } catch (error) {
                        console.warn(`视频 ${index + 1} 提取失败:`, error);
                    }
                });
                
                return videos;
            }
        """)
        
        print(f"✅ 提取到 {len(videos)} 个视频")
        return videos

    async def search_users(self, keyword: str, limit: int = 10) -> List[Dict]:
        """搜索用户"""
        try:
            page = await self.create_page()
            
            search_url = f"{self.base_url}/search/user?keyword={keyword}"
            print(f"🔍 搜索用户: {keyword}")
            
            await page.goto(search_url, wait_until='networkidle')
            await self.wait_random(3, 5)
            
            # 提取搜索结果
            users = await page.evaluate(f"""
                (limit) => {{
                    const userCards = document.querySelectorAll('.user-card, .search-user-item');
                    const results = [];
                    
                    for (let i = 0; i < Math.min(userCards.length, limit); i++) {{
                        const card = userCards[i];
                        const nameEl = card.querySelector('.user-name, .username');
                        const linkEl = card.querySelector('a');
                        const avatarEl = card.querySelector('.user-avatar img, .avatar img');
                        const descEl = card.querySelector('.user-desc, .description');
                        
                        if (nameEl && linkEl) {{
                            results.push({{
                                name: nameEl.textContent.trim(),
                                url: linkEl.href,
                                avatar: avatarEl?.src || '',
                                description: descEl?.textContent.trim() || ''
                            }});
                        }}
                    }}
                    
                    return results;
                }}
            """, limit)
            
            self.log_result(True, f"搜索到 {len(users)} 个用户", len(users))
            return users
            
        except Exception as e:
            self.log_result(False, f"用户搜索失败: {str(e)}")
            return []
        finally:
            await self.close()

    async def crawl_video_comments(self, video_url: str, limit: int = 50) -> List[Dict]:
        """爬取视频评论（需要登录）"""
        try:
            page = await self.create_page()
            
            print(f"🔍 正在爬取视频评论: {video_url}")
            await page.goto(video_url, wait_until='networkidle')
            await self.wait_random(3, 5)
            
            # 滚动加载更多评论
            await self.scroll_page(page, scrolls=3)
            
            comments = await page.evaluate(f"""
                (limit) => {{
                    const commentElements = document.querySelectorAll('.comment-item, .aweme-comment');
                    const comments = [];
                    
                    for (let i = 0; i < Math.min(commentElements.length, limit); i++) {{
                        const element = commentElements[i];
                        const userEl = element.querySelector('.comment-user, .user-name');
                        const contentEl = element.querySelector('.comment-content, .comment-text');
                        const timeEl = element.querySelector('.comment-time, .time');
                        const likeEl = element.querySelector('.like-count, .digg-count');
                        
                        if (userEl && contentEl) {{
                            comments.push({{
                                user: userEl.textContent.trim(),
                                content: contentEl.textContent.trim(),
                                time: timeEl?.textContent.trim() || '',
                                likes: likeEl?.textContent.trim() || '0'
                            }});
                        }}
                    }}
                    
                    return comments;
                }}
            """, limit)
            
            self.log_result(True, f"爬取到 {len(comments)} 条评论", len(comments))
            return comments
            
        except Exception as e:
            self.log_result(False, f"评论爬取失败: {str(e)}")
            return []
        finally:
            await self.close()

    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

    async def batch_crawl_users(self, user_urls: List[str]) -> List[Dict]:
        """批量爬取用户"""
        results = []
        
        for i, url in enumerate(user_urls):
            print(f"\n📊 进度: {i+1}/{len(user_urls)}")
            
            try:
                result = await self.crawl_user_profile(url)
                results.append({
                    'success': True,
                    'url': url,
                    'data': result
                })
            except Exception as e:
                results.append({
                    'success': False,
                    'url': url,
                    'error': str(e)
                })
            
            # 添加延迟避免被封
            if i < len(user_urls) - 1:
                await asyncio.sleep(5)
        
        return results
