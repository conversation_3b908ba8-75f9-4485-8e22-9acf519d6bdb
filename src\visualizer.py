#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化模块 - 生成交互式图表和分析报告
专家级可视化，提供多维度数据分析
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
from typing import Dict, List, Any
import numpy as np
from collections import Counter
import re

from .utils.logger import get_logger
from .map_visualizer import MapVisualizer

class DataVisualizer:
    """数据可视化器类"""
    
    def __init__(self):
        """初始化可视化器"""
        self.logger = get_logger(__name__)
        self.map_visualizer = MapVisualizer()

        # 设置图表主题
        self.theme = {
            'color_palette': [
                '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
            ],
            'background_color': 'white',
            'grid_color': '#f0f0f0',
            'text_color': '#333333'
        }
    
    def create_charts(self, df: pd.DataFrame) -> Dict[str, go.Figure]:
        """创建所有图表"""
        self.logger.info(f"开始生成可视化图表，数据量: {len(df)}")
        
        if df.empty:
            self.logger.warning("数据为空，无法生成图表")
            return {}
        
        charts = {}
        
        try:
            # 1. 行业分布图
            if '行业' in df.columns:
                charts['industry_distribution'] = self._create_industry_distribution(df)
            
            # 2. 公司规模分析
            if 'company_size' in df.columns:
                charts['company_size_analysis'] = self._create_company_size_chart(df)
            
            # 3. 数据完整度分析
            if 'data_completeness' in df.columns:
                charts['data_completeness'] = self._create_completeness_chart(df)
            
            # 4. 数据源分布
            if 'source' in df.columns:
                charts['data_source_distribution'] = self._create_source_distribution(df)
            
            # 5. 联系信息统计
            charts['contact_info_stats'] = self._create_contact_stats(df)
            
            # 6. 综合仪表板
            charts['dashboard'] = self._create_dashboard(df)

            # 7. 地图可视化
            map_charts = self.map_visualizer.create_all_maps(df)
            charts.update(map_charts)

            self.logger.info(f"成功生成 {len(charts)} 个图表")

        except Exception as e:
            self.logger.error(f"生成图表时出错: {e}")

        return charts
    
    def _create_industry_distribution(self, df: pd.DataFrame) -> go.Figure:
        """创建行业分布图"""
        if '行业' not in df.columns:
            return go.Figure()
        
        # 统计行业分布
        industry_counts = df['行业'].value_counts()
        
        # 创建饼图
        fig = go.Figure(data=[
            go.Pie(
                labels=industry_counts.index,
                values=industry_counts.values,
                hole=0.4,
                textinfo='label+percent',
                textposition='outside',
                marker=dict(
                    colors=self.theme['color_palette'][:len(industry_counts)],
                    line=dict(color='white', width=2)
                )
            )
        ])
        
        fig.update_layout(
            title={
                'text': '行业分布统计',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'color': self.theme['text_color']}
            },
            showlegend=True,
            legend=dict(
                orientation="v",
                yanchor="middle",
                y=0.5,
                xanchor="left",
                x=1.01
            ),
            margin=dict(t=60, b=40, l=40, r=120),
            paper_bgcolor=self.theme['background_color'],
            plot_bgcolor=self.theme['background_color']
        )
        
        return fig
    
    def _create_company_size_chart(self, df: pd.DataFrame) -> go.Figure:
        """创建公司规模分析图"""
        if 'company_size' not in df.columns:
            return go.Figure()
        
        size_counts = df['company_size'].value_counts()
        
        # 创建柱状图
        fig = go.Figure(data=[
            go.Bar(
                x=size_counts.index,
                y=size_counts.values,
                marker=dict(
                    color=self.theme['color_palette'][0],
                    opacity=0.8,
                    line=dict(color='white', width=1)
                ),
                text=size_counts.values,
                textposition='auto'
            )
        ])
        
        fig.update_layout(
            title={
                'text': '公司规模分布',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'color': self.theme['text_color']}
            },
            xaxis_title='公司规模',
            yaxis_title='数量',
            paper_bgcolor=self.theme['background_color'],
            plot_bgcolor=self.theme['background_color'],
            margin=dict(t=60, b=40, l=40, r=40)
        )
        
        return fig
    
    def _create_completeness_chart(self, df: pd.DataFrame) -> go.Figure:
        """创建数据完整度分析图"""
        if 'data_completeness' not in df.columns:
            return go.Figure()
        
        # 创建完整度分布直方图
        fig = go.Figure(data=[
            go.Histogram(
                x=df['data_completeness'],
                nbinsx=20,
                marker=dict(
                    color=self.theme['color_palette'][2],
                    opacity=0.7,
                    line=dict(color='white', width=1)
                )
            )
        ])
        
        fig.update_layout(
            title={
                'text': '数据完整度分布',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'color': self.theme['text_color']}
            },
            xaxis_title='完整度 (%)',
            yaxis_title='公司数量',
            paper_bgcolor=self.theme['background_color'],
            plot_bgcolor=self.theme['background_color'],
            margin=dict(t=60, b=40, l=40, r=40)
        )
        
        # 添加平均线
        avg_completeness = df['data_completeness'].mean()
        fig.add_vline(
            x=avg_completeness,
            line_dash="dash",
            line_color="red",
            annotation_text=f"平均: {avg_completeness:.1f}%"
        )
        
        return fig
    
    def _create_source_distribution(self, df: pd.DataFrame) -> go.Figure:
        """创建数据源分布图"""
        if 'source' not in df.columns:
            return go.Figure()
        
        source_counts = df['source'].value_counts()
        
        # 创建环形图
        fig = go.Figure(data=[
            go.Pie(
                labels=source_counts.index,
                values=source_counts.values,
                hole=0.6,
                textinfo='label+value',
                textposition='outside',
                marker=dict(
                    colors=self.theme['color_palette'][:len(source_counts)],
                    line=dict(color='white', width=2)
                )
            )
        ])
        
        fig.update_layout(
            title={
                'text': '数据源分布',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'color': self.theme['text_color']}
            },
            showlegend=True,
            paper_bgcolor=self.theme['background_color'],
            plot_bgcolor=self.theme['background_color'],
            margin=dict(t=60, b=40, l=40, r=40)
        )
        
        return fig
    
    def _create_contact_stats(self, df: pd.DataFrame) -> go.Figure:
        """创建联系信息统计图"""
        contact_fields = ['电话', '邮箱', '官网', '地址']
        available_fields = [field for field in contact_fields if field in df.columns]
        
        if not available_fields:
            return go.Figure()
        
        # 统计各字段的完整性
        completeness_stats = {}
        for field in available_fields:
            total_count = len(df)
            valid_count = df[field].notna().sum()
            completeness_stats[field] = (valid_count / total_count) * 100
        
        # 创建柱状图
        fig = go.Figure(data=[
            go.Bar(
                x=list(completeness_stats.keys()),
                y=list(completeness_stats.values()),
                marker=dict(
                    color=self.theme['color_palette'][:len(completeness_stats)],
                    opacity=0.8,
                    line=dict(color='white', width=1)
                ),
                text=[f"{v:.1f}%" for v in completeness_stats.values()],
                textposition='auto'
            )
        ])
        
        fig.update_layout(
            title={
                'text': '联系信息完整度统计',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'color': self.theme['text_color']}
            },
            xaxis_title='信息类型',
            yaxis_title='完整度 (%)',
            yaxis=dict(range=[0, 100]),
            paper_bgcolor=self.theme['background_color'],
            plot_bgcolor=self.theme['background_color'],
            margin=dict(t=60, b=40, l=40, r=40)
        )
        
        return fig
    
    def _create_dashboard(self, df: pd.DataFrame) -> go.Figure:
        """创建综合仪表板"""
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('数据概览', '行业TOP5', '完整度分布', '数据源占比'),
            specs=[[{"type": "indicator"}, {"type": "bar"}],
                   [{"type": "histogram"}, {"type": "pie"}]]
        )
        
        # 1. 数据概览指标
        total_companies = len(df)
        avg_completeness = df.get('data_completeness', pd.Series([0])).mean()
        
        fig.add_trace(
            go.Indicator(
                mode="number+gauge",
                value=total_companies,
                title={"text": "收集企业总数"},
                gauge={
                    'axis': {'range': [None, total_companies * 1.2]},
                    'bar': {'color': self.theme['color_palette'][0]},
                    'steps': [
                        {'range': [0, total_companies * 0.5], 'color': "lightgray"},
                        {'range': [total_companies * 0.5, total_companies], 'color': "gray"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': total_companies * 0.9
                    }
                }
            ),
            row=1, col=1
        )
        
        # 2. 行业TOP5
        if '行业' in df.columns:
            industry_top5 = df['行业'].value_counts().head(5)
            fig.add_trace(
                go.Bar(
                    x=industry_top5.values,
                    y=industry_top5.index,
                    orientation='h',
                    marker=dict(color=self.theme['color_palette'][1])
                ),
                row=1, col=2
            )
        
        # 3. 完整度分布
        if 'data_completeness' in df.columns:
            fig.add_trace(
                go.Histogram(
                    x=df['data_completeness'],
                    nbinsx=10,
                    marker=dict(color=self.theme['color_palette'][2])
                ),
                row=2, col=1
            )
        
        # 4. 数据源占比
        if 'source' in df.columns:
            source_counts = df['source'].value_counts()
            fig.add_trace(
                go.Pie(
                    labels=source_counts.index,
                    values=source_counts.values,
                    marker=dict(colors=self.theme['color_palette'][:len(source_counts)])
                ),
                row=2, col=2
            )
        
        fig.update_layout(
            title={
                'text': '客户信息收集综合仪表板',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 24, 'color': self.theme['text_color']}
            },
            height=800,
            showlegend=False,
            paper_bgcolor=self.theme['background_color'],
            plot_bgcolor=self.theme['background_color']
        )
        
        return fig
    
    def create_summary_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """生成数据摘要报告"""
        if df.empty:
            return {"error": "数据为空"}
        
        report = {
            "总体统计": {
                "企业总数": len(df),
                "数据字段数": len(df.columns),
                "平均完整度": f"{df.get('data_completeness', pd.Series([0])).mean():.1f}%"
            }
        }
        
        # 行业分析
        if '行业' in df.columns:
            industry_stats = df['行业'].value_counts()
            report["行业分析"] = {
                "行业总数": len(industry_stats),
                "最大行业": industry_stats.index[0] if len(industry_stats) > 0 else "无",
                "最大行业企业数": industry_stats.iloc[0] if len(industry_stats) > 0 else 0
            }
        
        # 联系信息统计
        contact_fields = ['电话', '邮箱', '官网', '地址']
        contact_stats = {}
        for field in contact_fields:
            if field in df.columns:
                valid_count = df[field].notna().sum()
                contact_stats[field] = f"{valid_count}/{len(df)} ({valid_count/len(df)*100:.1f}%)"
        
        if contact_stats:
            report["联系信息统计"] = contact_stats
        
        return report
