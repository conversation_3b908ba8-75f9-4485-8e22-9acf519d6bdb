#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据爬取系统 - 基础爬虫类
"""

import asyncio
import json
import os
import time
import random
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from playwright.async_api import async_playwright, Browser, Page
from fake_useragent import UserAgent
import requests
from bs4 import BeautifulSoup
import pandas as pd
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class BaseCrawler:
    """基础爬虫类，提供通用的爬取功能"""
    
    def __init__(self, platform_name: str):
        self.platform_name = platform_name
        self.ua = UserAgent()
        self.session = requests.Session()
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        
        # 配置
        self.headless = os.getenv('HEADLESS', 'false').lower() == 'true'
        self.request_delay = int(os.getenv('REQUEST_DELAY', '2000'))
        self.proxy_config = self._get_proxy_config()
        
        # 数据存储
        self.data_dir = Path(f"data/{platform_name}")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🚀 {platform_name} 爬虫初始化完成")

    def _get_proxy_config(self) -> Optional[Dict]:
        """获取代理配置"""
        proxy_host = os.getenv('PROXY_HOST')
        proxy_port = os.getenv('PROXY_PORT')
        
        if proxy_host and proxy_port:
            return {
                'server': f"http://{proxy_host}:{proxy_port}",
                'username': os.getenv('PROXY_USERNAME'),
                'password': os.getenv('PROXY_PASSWORD')
            }
        return None

    async def init_browser(self) -> Browser:
        """初始化浏览器"""
        playwright = await async_playwright().start()
        
        browser_args = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-blink-features=AutomationControlled'
        ]
        
        launch_options = {
            'headless': self.headless,
            'args': browser_args
        }
        
        if self.proxy_config:
            launch_options['proxy'] = self.proxy_config
            
        self.browser = await playwright.chromium.launch(**launch_options)
        return self.browser

    async def create_page(self) -> Page:
        """创建新页面"""
        if not self.browser:
            await self.init_browser()
            
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        await self.page.set_extra_http_headers({'User-Agent': self.ua.random})
        
        # 设置视口
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
        # 拦截不必要的资源
        await self.page.route("**/*", self._route_handler)
        
        return self.page

    async def _route_handler(self, route):
        """路由处理器，拦截不必要的资源"""
        resource_type = route.request.resource_type
        if resource_type in ['image', 'stylesheet', 'font', 'media']:
            await route.abort()
        else:
            await route.continue_()

    async def wait_random(self, min_seconds: float = 1, max_seconds: float = 3):
        """随机等待"""
        wait_time = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(wait_time)

    async def scroll_page(self, page: Page, scrolls: int = 3):
        """滚动页面加载更多内容"""
        for i in range(scrolls):
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await self.wait_random(2, 4)
            print(f"📜 第 {i+1} 次滚动完成")

    def save_data(self, data: Any, filename: str, format: str = 'json') -> str:
        """保存数据"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format == 'json':
            filepath = self.data_dir / f"{filename}_{timestamp}.json"
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        elif format == 'csv':
            filepath = self.data_dir / f"{filename}_{timestamp}.csv"
            if isinstance(data, list) and data:
                df = pd.DataFrame(data)
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
        elif format == 'excel':
            filepath = self.data_dir / f"{filename}_{timestamp}.xlsx"
            if isinstance(data, list) and data:
                df = pd.DataFrame(data)
                df.to_excel(filepath, index=False)
        
        print(f"💾 数据已保存到: {filepath}")
        return str(filepath)

    async def close(self):
        """关闭浏览器"""
        if self.page:
            await self.page.close()
        if self.browser:
            await self.browser.close()

    def log_result(self, success: bool, message: str, data_count: int = 0):
        """记录结果"""
        status = "✅" if success else "❌"
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        log_message = f"{status} [{timestamp}] {self.platform_name}: {message}"
        if data_count > 0:
            log_message += f" (数据量: {data_count})"
            
        print(log_message)
        
        # 保存到日志文件
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        with open(log_dir / f"{self.platform_name}_crawler.log", 'a', encoding='utf-8') as f:
            f.write(log_message + "\n")

    async def extract_with_retry(self, extract_func, max_retries: int = 3):
        """带重试的数据提取"""
        for attempt in range(max_retries):
            try:
                result = await extract_func()
                return result
            except Exception as e:
                print(f"⚠️ 第 {attempt + 1} 次尝试失败: {str(e)}")
                if attempt < max_retries - 1:
                    await self.wait_random(3, 6)
                else:
                    raise e

    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    async def check_anti_bot(self, page: Page) -> bool:
        """检查是否遇到反爬机制"""
        # 检查常见的反爬页面元素
        anti_bot_selectors = [
            'div[class*="captcha"]',
            'div[class*="verify"]',
            'div[class*="robot"]',
            'iframe[src*="captcha"]',
            'div[id*="slider"]'
        ]
        
        for selector in anti_bot_selectors:
            element = await page.query_selector(selector)
            if element:
                print(f"⚠️ 检测到反爬机制: {selector}")
                return True
        
        return False

    async def handle_captcha(self, page: Page) -> bool:
        """处理验证码（基础实现）"""
        print("🔍 检测到验证码，等待手动处理...")
        
        # 等待用户手动处理验证码
        for i in range(30):
            await asyncio.sleep(1)
            
            # 检查验证码是否消失
            if not await self.check_anti_bot(page):
                print("✅ 验证码处理完成")
                return True
                
        print("❌ 验证码处理超时")
        return False
