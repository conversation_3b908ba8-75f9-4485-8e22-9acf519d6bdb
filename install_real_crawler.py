#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实爬虫依赖安装脚本 - 安装完整的爬虫功能所需的依赖包
"""

import subprocess
import sys
import importlib

def install_package(package_name):
    """安装包"""
    try:
        print(f"🔄 正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            '-i', 'https://mirrors.aliyun.com/pypi/simple/',
            '--trusted-host', 'mirrors.aliyun.com',
            package_name
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出错: {e}")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("🕷️ 真实爬虫功能依赖安装")
    print("=" * 60)
    print("这将安装完整的爬虫功能所需的依赖包，包括:")
    print("• BeautifulSoup4 - HTML解析")
    print("• requests - HTTP请求")
    print("• fake-useragent - 用户代理轮换")
    print("• fuzzywuzzy - 模糊匹配去重")
    print("• python-levenshtein - 字符串相似度计算")
    print("• lxml - 高性能XML/HTML解析器")
    print("=" * 60)
    
    # 真实爬虫所需的包
    real_crawler_packages = [
        ('beautifulsoup4', 'bs4'),
        ('requests', 'requests'),
        ('fake-useragent', 'fake_useragent'),
        ('fuzzywuzzy', 'fuzzywuzzy'),
        ('python-levenshtein', 'Levenshtein'),
        ('lxml', 'lxml'),
    ]
    
    # 检查当前安装状态
    print("\n🔍 检查当前安装状态:")
    missing_packages = []
    
    for package_name, import_name in real_crawler_packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    if not missing_packages:
        print("\n🎉 所有真实爬虫依赖都已安装！")
        print("现在可以获取真实的企业数据了。")
        return True
    
    print(f"\n⚠️ 需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    # 询问是否安装
    choice = input("\n是否现在安装这些包? (y/n): ").lower().strip()
    
    if choice not in ['y', 'yes', '是']:
        print("取消安装。")
        return False
    
    # 开始安装
    print("\n🔄 开始安装缺失的包...")
    success_count = 0
    
    for package in missing_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 安装结果: {success_count}/{len(missing_packages)} 个包安装成功")
    
    if success_count == len(missing_packages):
        print("\n🎉 所有依赖安装完成！")
        print("\n✨ 现在您可以:")
        print("1. 获取真实的企业搜索结果")
        print("2. 提取真实的联系信息")
        print("3. 使用智能去重功能")
        print("4. 获得更准确的数据")
        print("\n🚀 重新启动系统以使用真实爬虫功能:")
        print("python -m streamlit run main.py")
        return True
    else:
        print("\n⚠️ 部分依赖安装失败")
        print("系统仍可运行，但可能使用演示数据")
        return False

if __name__ == "__main__":
    main()
