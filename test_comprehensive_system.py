#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合系统测试
整合所有数据抓取功能进行测试
"""

import sys
import os
from pathlib import Path
import pandas as pd
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_comprehensive_system():
    """测试综合数据抓取系统"""
    print("🚀 启动综合数据抓取系统测试")
    print("=" * 60)
    
    # 导入所有抓取器
    try:
        from simple_data_crawler import SimpleDataCrawler
        from advanced_real_crawler import AdvancedRealCrawler
        print("✅ 所有抓取器模块加载成功")
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return
    
    # 测试案例
    test_cases = [
        {
            "industry": "酒店管理",
            "province": "北京市",
            "city": "朝阳区"
        },
        {
            "industry": "房地产开发",
            "province": "上海市", 
            "city": "浦东新区"
        },
        {
            "industry": "建筑工程",
            "province": "广东省",
            "city": "深圳市"
        }
    ]
    
    all_results = []
    
    print("\n🔧 第一阶段：使用简化数据抓取器")
    print("-" * 40)
    
    # 1. 使用简化抓取器
    simple_crawler = SimpleDataCrawler()
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 简化抓取案例 {i}: {case['industry']}")
        print(f"地区: {case['province']} {case['city']}")
        
        try:
            results = simple_crawler.collect_comprehensive_data(
                industry=case['industry'],
                province=case['province'],
                city=case['city'],
                max_results=15
            )
            
            print(f"✅ 简化抓取收集到 {len(results)} 条数据")
            
            # 为结果添加标识
            for result in results:
                result['crawler_type'] = 'simple'
                result['test_case'] = f"案例{i}"
            
            all_results.extend(results)
            
        except Exception as e:
            print(f"❌ 简化抓取案例 {i} 失败: {e}")
    
    print("\n🔧 第二阶段：使用高级数据抓取器")
    print("-" * 40)
    
    # 2. 使用高级抓取器
    advanced_crawler = AdvancedRealCrawler()
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 高级抓取案例 {i}: {case['industry']}")
        print(f"地区: {case['province']} {case['city']}")
        
        try:
            results = advanced_crawler.collect_comprehensive_enterprise_data(
                industry=case['industry'],
                province=case['province'],
                city=case['city'],
                max_results=15
            )
            
            print(f"✅ 高级抓取收集到 {len(results)} 条数据")
            
            # 为结果添加标识
            for result in results:
                result['crawler_type'] = 'advanced'
                result['test_case'] = f"案例{i}"
            
            all_results.extend(results)
            
        except Exception as e:
            print(f"❌ 高级抓取案例 {i} 失败: {e}")
    
    print("\n📊 第三阶段：数据整合与分析")
    print("-" * 40)
    
    # 保存综合结果
    if all_results:
        df = pd.DataFrame(all_results)
        
        # 去重处理
        original_count = len(df)
        df = df.drop_duplicates(subset=['company_name'], keep='first')
        deduplicated_count = len(df)
        
        output_file = "data/comprehensive_test_results.csv"
        os.makedirs("data", exist_ok=True)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"💾 综合结果已保存到: {output_file}")
        print(f"📊 原始数据: {original_count} 条")
        print(f"📊 去重后数据: {deduplicated_count} 条")
        print(f"📊 去重率: {((original_count - deduplicated_count) / original_count * 100):.1f}%")
        
        # 显示统计信息
        print(f"\n📈 数据来源统计:")
        if 'source' in df.columns:
            source_counts = df['source'].value_counts()
            for source, count in source_counts.items():
                print(f"  {source}: {count} 条")
        
        print(f"\n📈 抓取器类型统计:")
        if 'crawler_type' in df.columns:
            crawler_counts = df['crawler_type'].value_counts()
            for crawler_type, count in crawler_counts.items():
                print(f"  {crawler_type}: {count} 条")
        
        print(f"\n📈 行业分布统计:")
        if 'industry' in df.columns:
            industry_counts = df['industry'].value_counts()
            for industry, count in industry_counts.items():
                print(f"  {industry}: {count} 条")
        
        print(f"\n📈 测试案例统计:")
        if 'test_case' in df.columns:
            case_counts = df['test_case'].value_counts()
            for case, count in case_counts.items():
                print(f"  {case}: {count} 条")
        
        # 显示样例数据
        print(f"\n📋 样例数据预览:")
        for idx, (_, row) in enumerate(df.head(8).iterrows(), 1):
            print(f"  {idx}. {row['company_name']}")
            print(f"     来源: {row.get('source', 'N/A')}")
            print(f"     行业: {row.get('industry', 'N/A')}")
            print(f"     地区: {row.get('region', 'N/A')}")
            print(f"     抓取器: {row.get('crawler_type', 'N/A')}")
            if idx < 8:
                print()
        
        # 数据质量分析
        print(f"\n🔍 数据质量分析:")
        
        # 检查必要字段的完整性
        required_fields = ['company_name', 'industry', 'source']
        for field in required_fields:
            if field in df.columns:
                missing_count = df[field].isna().sum()
                completeness = ((len(df) - missing_count) / len(df) * 100)
                print(f"  {field}: {completeness:.1f}% 完整度 ({missing_count} 条缺失)")
        
        # 检查企业名称长度分布
        if 'company_name' in df.columns:
            name_lengths = df['company_name'].str.len()
            print(f"  企业名称长度: 平均 {name_lengths.mean():.1f} 字符 (范围: {name_lengths.min()}-{name_lengths.max()})")
        
        # 检查数据类型分布
        if 'data_type' in df.columns:
            print(f"  数据类型分布:")
            type_counts = df['data_type'].value_counts()
            for data_type, count in type_counts.items():
                print(f"    {data_type}: {count} 条")
        
        print(f"\n✅ 综合系统测试完成！")
        print(f"🎯 成功收集并整合了 {deduplicated_count} 条高质量企业数据")
        print(f"📁 数据文件: {output_file}")
        
        # 生成测试报告
        generate_test_report(df, output_file)
        
    else:
        print("\n❌ 未收集到任何数据")

def generate_test_report(df, output_file):
    """生成测试报告"""
    print(f"\n📄 生成测试报告...")
    
    report_file = "data/test_report.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("综合数据抓取系统测试报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"数据文件: {output_file}\n")
        f.write(f"总数据量: {len(df)} 条\n\n")
        
        f.write("数据来源统计:\n")
        if 'source' in df.columns:
            source_counts = df['source'].value_counts()
            for source, count in source_counts.items():
                f.write(f"  {source}: {count} 条\n")
        
        f.write("\n抓取器类型统计:\n")
        if 'crawler_type' in df.columns:
            crawler_counts = df['crawler_type'].value_counts()
            for crawler_type, count in crawler_counts.items():
                f.write(f"  {crawler_type}: {count} 条\n")
        
        f.write("\n行业分布统计:\n")
        if 'industry' in df.columns:
            industry_counts = df['industry'].value_counts()
            for industry, count in industry_counts.items():
                f.write(f"  {industry}: {count} 条\n")
        
        f.write("\n数据质量评估:\n")
        required_fields = ['company_name', 'industry', 'source']
        for field in required_fields:
            if field in df.columns:
                missing_count = df[field].isna().sum()
                completeness = ((len(df) - missing_count) / len(df) * 100)
                f.write(f"  {field}: {completeness:.1f}% 完整度\n")
        
        f.write("\n样例数据:\n")
        for idx, (_, row) in enumerate(df.head(5).iterrows(), 1):
            f.write(f"  {idx}. {row['company_name']}\n")
            f.write(f"     来源: {row.get('source', 'N/A')}\n")
            f.write(f"     行业: {row.get('industry', 'N/A')}\n")
            f.write(f"     地区: {row.get('region', 'N/A')}\n\n")
    
    print(f"📄 测试报告已保存到: {report_file}")

if __name__ == "__main__":
    test_comprehensive_system()
