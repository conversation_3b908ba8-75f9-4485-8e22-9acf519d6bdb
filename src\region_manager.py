#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域管理模块 - 处理地理区域相关功能
支持省市区域选择和区域化搜索
"""

import yaml
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import pandas as pd

from .utils.logger import get_logger

class RegionManager:
    """区域管理器类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化区域管理器"""
        self.logger = get_logger(__name__)
        self.config = self._load_config(config_path)
        self.regions_data = self.config.get('regions', {}).get('china', {}).get('provinces', [])
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            return {}
    
    def get_all_provinces(self) -> List[str]:
        """获取所有省份列表"""
        provinces = []
        for province_data in self.regions_data:
            provinces.append(province_data['name'])
        return sorted(provinces)
    
    def get_cities_by_province(self, province: str) -> List[str]:
        """根据省份获取城市列表"""
        for province_data in self.regions_data:
            if province_data['name'] == province:
                return province_data.get('cities', [])
        return []
    
    def get_all_cities(self) -> List[str]:
        """获取所有城市列表"""
        cities = []
        for province_data in self.regions_data:
            cities.extend(province_data.get('cities', []))
        return sorted(cities)
    
    def get_province_by_city(self, city: str) -> Optional[str]:
        """根据城市获取所属省份"""
        for province_data in self.regions_data:
            if city in province_data.get('cities', []):
                return province_data['name']
        return None
    
    def generate_region_keywords(self, province: str = None, city: str = None, industry: str = None) -> List[str]:
        """生成区域化搜索关键词"""
        keywords = []
        
        if industry:
            base_keywords = [industry]
            
            # 添加行业相关词汇
            industry_extensions = [
                f"{industry}公司",
                f"{industry}企业",
                f"{industry}厂家",
                f"{industry}供应商",
                f"{industry}制造商"
            ]
            base_keywords.extend(industry_extensions)
        else:
            base_keywords = ["公司", "企业", "厂家"]
        
        # 组合区域关键词
        if city:
            for keyword in base_keywords:
                keywords.extend([
                    f"{city} {keyword}",
                    f"{city}{keyword}",
                    f"{keyword} {city}"
                ])
        
        if province and not city:
            for keyword in base_keywords:
                keywords.extend([
                    f"{province} {keyword}",
                    f"{province}{keyword}",
                    f"{keyword} {province}"
                ])
        
        # 去重并返回
        return list(set(keywords))
    
    def validate_region(self, province: str = None, city: str = None) -> Tuple[bool, str]:
        """验证区域选择的有效性"""
        if not province and not city:
            return True, "未选择区域，将进行全国搜索"
        
        if province and province not in self.get_all_provinces():
            return False, f"无效的省份: {province}"
        
        if city:
            if not province:
                # 如果只选择了城市，检查城市是否存在
                if city not in self.get_all_cities():
                    return False, f"无效的城市: {city}"
            else:
                # 如果同时选择了省份和城市，检查城市是否属于该省份
                province_cities = self.get_cities_by_province(province)
                if city not in province_cities:
                    return False, f"城市 {city} 不属于 {province}"
        
        return True, "区域选择有效"
    
    def get_region_display_name(self, province: str = None, city: str = None) -> str:
        """获取区域的显示名称"""
        if city and province:
            return f"{province} - {city}"
        elif province:
            return province
        elif city:
            # 如果只有城市，尝试找到所属省份
            found_province = self.get_province_by_city(city)
            if found_province:
                return f"{found_province} - {city}"
            return city
        else:
            return "全国"
    
    def get_region_statistics(self, df: pd.DataFrame) -> Dict:
        """获取数据中的区域统计信息"""
        stats = {
            'total_records': len(df),
            'provinces': {},
            'cities': {},
            'top_provinces': [],
            'top_cities': []
        }
        
        if 'address' in df.columns:
            # 分析地址字段中的省份和城市信息
            for _, row in df.iterrows():
                address = str(row.get('address', ''))
                
                # 匹配省份
                for province_data in self.regions_data:
                    province_name = province_data['name']
                    if province_name in address or province_name.replace('省', '').replace('市', '') in address:
                        stats['provinces'][province_name] = stats['provinces'].get(province_name, 0) + 1
                        
                        # 匹配城市
                        for city in province_data.get('cities', []):
                            if city in address:
                                stats['cities'][city] = stats['cities'].get(city, 0) + 1
        
        # 获取TOP排行
        if stats['provinces']:
            stats['top_provinces'] = sorted(
                stats['provinces'].items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
        
        if stats['cities']:
            stats['top_cities'] = sorted(
                stats['cities'].items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
        
        return stats
    
    def filter_data_by_region(self, df: pd.DataFrame, province: str = None, city: str = None) -> pd.DataFrame:
        """根据区域筛选数据"""
        if df.empty:
            return df
        
        if not province and not city:
            return df
        
        filtered_df = df.copy()
        
        if 'address' in df.columns:
            if city:
                # 按城市筛选
                mask = filtered_df['address'].str.contains(city, na=False)
                filtered_df = filtered_df[mask]
            elif province:
                # 按省份筛选
                province_short = province.replace('省', '').replace('市', '')
                mask = (filtered_df['address'].str.contains(province, na=False) | 
                       filtered_df['address'].str.contains(province_short, na=False))
                filtered_df = filtered_df[mask]
        
        self.logger.info(f"区域筛选: {self.get_region_display_name(province, city)}, "
                        f"筛选前: {len(df)} 条, 筛选后: {len(filtered_df)} 条")
        
        return filtered_df
