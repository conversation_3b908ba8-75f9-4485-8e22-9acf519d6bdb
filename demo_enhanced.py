#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版演示脚本 - 展示区域搜索功能
不依赖复杂库，可直接运行
"""

import json
import time
import csv
from datetime import datetime
from pathlib import Path

def create_demo_data_with_regions():
    """创建包含区域信息的演示数据"""
    demo_data = [
        {
            'company_name': '北京智能科技有限公司',
            'industry': '人工智能',
            'website': 'https://beijing-ai.com',
            'phone': '010-12345678',
            'email': '<EMAIL>',
            'address': '北京市海淀区中关村大街1号',
            'province': '北京市',
            'city': '北京市',
            'source': 'baidu_demo'
        },
        {
            'company_name': '上海云计算科技股份有限公司',
            'industry': '云计算',
            'website': 'https://shanghai-cloud.com',
            'phone': '021-87654321',
            'email': '<EMAIL>',
            'address': '上海市浦东新区张江高科技园区',
            'province': '上海市',
            'city': '上海市',
            'source': 'bing_demo'
        },
        {
            'company_name': '深圳新能源汽车有限公司',
            'industry': '新能源汽车',
            'website': 'https://shenzhen-ev.com',
            'phone': '0755-11111111',
            'email': '<EMAIL>',
            'address': '广东省深圳市南山区科技园',
            'province': '广东省',
            'city': '深圳市',
            'source': 'baidu_demo'
        },
        {
            'company_name': '杭州电商平台科技有限公司',
            'industry': '电子商务',
            'website': 'https://hangzhou-ecommerce.com',
            'phone': '0571-22222222',
            'email': '<EMAIL>',
            'address': '浙江省杭州市西湖区文三路',
            'province': '浙江省',
            'city': '杭州市',
            'source': 'business_dir'
        },
        {
            'company_name': '成都游戏开发工作室',
            'industry': '游戏开发',
            'website': 'https://chengdu-games.com',
            'phone': '028-33333333',
            'email': '<EMAIL>',
            'address': '四川省成都市高新区天府大道',
            'province': '四川省',
            'city': '成都市',
            'source': 'baidu_demo'
        },
        {
            'company_name': '广州生物医药研究院',
            'industry': '生物医药',
            'website': 'https://guangzhou-biotech.com',
            'phone': '020-44444444',
            'email': '<EMAIL>',
            'address': '广东省广州市天河区珠江新城',
            'province': '广东省',
            'city': '广州市',
            'source': 'bing_demo'
        },
        {
            'company_name': '南京智能制造有限公司',
            'industry': '智能制造',
            'website': 'https://nanjing-smart.com',
            'phone': '025-55555555',
            'email': '<EMAIL>',
            'address': '江苏省南京市江宁区开发区',
            'province': '江苏省',
            'city': '南京市',
            'source': 'business_dir'
        },
        {
            'company_name': '武汉光电技术股份有限公司',
            'industry': '光电技术',
            'website': 'https://wuhan-optics.com',
            'phone': '027-66666666',
            'email': '<EMAIL>',
            'address': '湖北省武汉市东湖高新区',
            'province': '湖北省',
            'city': '武汉市',
            'source': 'baidu_demo'
        }
    ]
    
    return demo_data

def analyze_region_distribution(data):
    """分析区域分布"""
    print("\n🌍 区域分布分析")
    print("=" * 50)
    
    # 省份统计
    province_stats = {}
    city_stats = {}
    
    for item in data:
        province = item.get('province', '未知')
        city = item.get('city', '未知')
        
        province_stats[province] = province_stats.get(province, 0) + 1
        city_stats[city] = city_stats.get(city, 0) + 1
    
    # 显示省份分布
    print("\n📊 省份分布:")
    for province, count in sorted(province_stats.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / len(data)) * 100
        print(f"  {province}: {count}家 ({percentage:.1f}%)")
    
    # 显示城市分布
    print("\n🏙️ 城市分布:")
    for city, count in sorted(city_stats.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / len(data)) * 100
        print(f"  {city}: {count}家 ({percentage:.1f}%)")
    
    return province_stats, city_stats

def filter_by_region(data, province=None, city=None):
    """按区域筛选数据"""
    filtered_data = []
    
    for item in data:
        match = True
        
        if province and item.get('province') != province:
            match = False
        
        if city and item.get('city') != city:
            match = False
        
        if match:
            filtered_data.append(item)
    
    return filtered_data

def export_data_enhanced(data, filename_prefix="enhanced_demo"):
    """增强版数据导出"""
    timestamp = int(time.time())
    
    # 确保数据目录存在
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # 导出CSV
    csv_file = data_dir / f"{filename_prefix}_{timestamp}.csv"
    with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
        if data:
            fieldnames = data[0].keys()
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
    
    print(f"✅ CSV文件已导出: {csv_file}")
    
    # 导出JSON
    json_file = data_dir / f"{filename_prefix}_{timestamp}.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ JSON文件已导出: {json_file}")
    
    # 生成统计报告
    report_file = data_dir / f"{filename_prefix}_report_{timestamp}.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"数据统计报告\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"=" * 50 + "\n\n")
        f.write(f"总记录数: {len(data)}\n\n")
        
        # 行业分布
        industry_stats = {}
        for item in data:
            industry = item.get('industry', '未知')
            industry_stats[industry] = industry_stats.get(industry, 0) + 1
        
        f.write("行业分布:\n")
        for industry, count in sorted(industry_stats.items(), key=lambda x: x[1], reverse=True):
            f.write(f"  {industry}: {count}家\n")
        
        # 区域分布
        province_stats = {}
        for item in data:
            province = item.get('province', '未知')
            province_stats[province] = province_stats.get(province, 0) + 1
        
        f.write("\n省份分布:\n")
        for province, count in sorted(province_stats.items(), key=lambda x: x[1], reverse=True):
            f.write(f"  {province}: {count}家\n")
    
    print(f"✅ 统计报告已生成: {report_file}")
    
    return csv_file, json_file, report_file

def demonstrate_region_search():
    """演示区域搜索功能"""
    print("🕷️ 智能客户信息收集系统 - 区域搜索演示")
    print("=" * 60)
    
    # 创建演示数据
    print("\n📦 创建演示数据...")
    demo_data = create_demo_data_with_regions()
    print(f"✅ 已创建 {len(demo_data)} 条演示数据")
    
    # 显示所有数据
    print("\n📋 所有企业信息:")
    for i, company in enumerate(demo_data, 1):
        print(f"  {i}. {company['company_name']} - {company['province']} {company['city']}")
    
    # 分析区域分布
    province_stats, city_stats = analyze_region_distribution(demo_data)
    
    # 演示区域筛选
    print("\n🔍 区域筛选演示")
    print("-" * 30)
    
    # 按省份筛选
    target_province = "广东省"
    guangdong_companies = filter_by_region(demo_data, province=target_province)
    print(f"\n📍 {target_province}的企业 ({len(guangdong_companies)}家):")
    for company in guangdong_companies:
        print(f"  • {company['company_name']} - {company['city']}")
    
    # 按城市筛选
    target_city = "北京市"
    beijing_companies = filter_by_region(demo_data, city=target_city)
    print(f"\n🏙️ {target_city}的企业 ({len(beijing_companies)}家):")
    for company in beijing_companies:
        print(f"  • {company['company_name']} - {company['industry']}")
    
    # 导出数据
    print("\n💾 数据导出演示")
    print("-" * 30)
    
    # 导出全部数据
    export_data_enhanced(demo_data, "all_companies")
    
    # 导出广东省数据
    if guangdong_companies:
        export_data_enhanced(guangdong_companies, "guangdong_companies")
    
    # 导出北京市数据
    if beijing_companies:
        export_data_enhanced(beijing_companies, "beijing_companies")
    
    print(f"\n🎉 演示完成！共处理 {len(demo_data)} 条企业数据")
    print("💡 提示: 实际使用时，系统会根据您选择的区域自动生成相应的搜索关键词")

if __name__ == "__main__":
    demonstrate_region_search()
