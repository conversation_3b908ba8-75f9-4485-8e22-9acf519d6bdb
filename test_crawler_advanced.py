#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫系统高级测试脚本
测试实际的爬虫功能和数据收集
"""

import sys
import os
from pathlib import Path
import traceback
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_crawler_real_data():
    """测试真实数据爬取"""
    print("🕷️ 测试真实数据爬取...")
    
    try:
        from src.crawler_engine import CrawlerEngine
        from src.data_processor import DataProcessor
        from src.database_manager import DatabaseManager
        
        # 初始化组件
        crawler = CrawlerEngine()
        processor = DataProcessor()
        db_manager = DatabaseManager()
        
        # 配置爬虫参数（使用小规模测试）
        crawler_config = {
            'industry': '人工智能',
            'province': '广东省',
            'city': '深圳市',
            'search_depth': 2,  # 只爬取2页，避免过度测试
            'use_baidu': True,
            'use_bing': False,  # 先只测试百度
            'use_business_dirs': False  # 先不测试企业目录
        }
        
        print(f"开始爬取: {crawler_config['industry']} - {crawler_config['province']} {crawler_config['city']}")
        
        # 开始爬取
        raw_data = crawler.start_crawling(crawler_config)
        
        if raw_data.empty:
            print("⚠️ 爬取结果为空，可能是网络问题或反爬机制")
            return False
        
        print(f"✅ 爬取成功，获得 {len(raw_data)} 条原始数据")
        
        # 数据处理
        processed_data = processor.process_data(raw_data)
        print(f"✅ 数据处理成功，处理后 {len(processed_data)} 条数据")
        
        # 保存到数据库
        db_manager.save_data(processed_data, crawler_config['industry'])
        print("✅ 数据保存成功")
        
        # 验证数据质量
        if not processed_data.empty:
            # 检查必要字段
            required_fields = ['company_name', 'industry']
            missing_fields = [field for field in required_fields if field not in processed_data.columns]
            
            if missing_fields:
                print(f"❌ 缺少必要字段: {missing_fields}")
                return False
            
            # 检查数据完整性
            non_empty_companies = processed_data['company_name'].notna().sum()
            print(f"✅ 有效公司名称: {non_empty_companies}/{len(processed_data)}")
            
            # 显示样本数据
            print("\n📋 样本数据:")
            print(processed_data[['company_name', 'industry', 'website', 'phone']].head(3).to_string())
            
            return True
        else:
            print("❌ 处理后数据为空")
            return False
            
    except Exception as e:
        print(f"❌ 真实数据爬取测试失败: {e}")
        traceback.print_exc()
        return False

def test_region_search():
    """测试区域搜索功能"""
    print("\n🌍 测试区域搜索功能...")
    
    try:
        from src.region_manager import RegionManager
        
        rm = RegionManager()
        
        # 测试不同区域的关键词生成
        test_cases = [
            ("广东省", "深圳市", "人工智能"),
            ("北京市", None, "新能源汽车"),
            (None, None, "生物医药")
        ]
        
        for province, city, industry in test_cases:
            keywords = rm.generate_region_keywords(province, city, industry)
            region_display = rm.get_region_display_name(province, city)
            
            print(f"✅ {region_display} - {industry}: 生成 {len(keywords)} 个关键词")
            if keywords:
                print(f"   示例: {keywords[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 区域搜索测试失败: {e}")
        traceback.print_exc()
        return False

def test_data_visualization():
    """测试数据可视化"""
    print("\n📊 测试数据可视化...")
    
    try:
        from src.visualizer import DataVisualizer
        import pandas as pd
        
        visualizer = DataVisualizer()
        
        # 创建测试数据
        test_data = pd.DataFrame([
            {
                'company_name': '深圳AI科技有限公司',
                'industry': '人工智能',
                'province': '广东省',
                'city': '深圳市',
                'website': 'https://ai-tech.com',
                'phone': '0755-12345678',
                'data_completeness': 85.5,
                'source': 'baidu'
            },
            {
                'company_name': '北京智能科技公司',
                'industry': '人工智能',
                'province': '北京市',
                'city': '海淀区',
                'website': 'https://beijing-ai.com',
                'phone': '010-87654321',
                'data_completeness': 92.3,
                'source': 'bing'
            },
            {
                'company_name': '上海机器人公司',
                'industry': '人工智能',
                'province': '上海市',
                'city': '浦东新区',
                'website': 'https://sh-robot.com',
                'phone': '021-11223344',
                'data_completeness': 78.9,
                'source': 'baidu'
            }
        ])
        
        # 生成图表
        charts = visualizer.create_charts(test_data)
        
        if charts:
            print(f"✅ 可视化成功，生成 {len(charts)} 个图表")
            chart_names = list(charts.keys())
            print(f"   图表类型: {', '.join(chart_names)}")
            return True
        else:
            print("❌ 可视化失败，未生成图表")
            return False
            
    except Exception as e:
        print(f"❌ 数据可视化测试失败: {e}")
        traceback.print_exc()
        return False

def test_export_functionality():
    """测试导出功能"""
    print("\n💾 测试导出功能...")
    
    try:
        from src.data_processor import DataProcessor
        import pandas as pd
        import tempfile
        import os
        
        processor = DataProcessor()
        
        # 创建测试数据
        test_data = pd.DataFrame([
            {
                'company_name': '测试公司A',
                'industry': '人工智能',
                'website': 'https://test-a.com',
                'phone': '010-12345678',
                'email': '<EMAIL>'
            },
            {
                'company_name': '测试公司B',
                'industry': '人工智能',
                'website': 'https://test-b.com',
                'phone': '021-87654321',
                'email': '<EMAIL>'
            }
        ])
        
        # 测试Excel导出
        excel_buffer = processor.to_excel(test_data)
        if excel_buffer:
            print("✅ Excel导出功能正常")
        else:
            print("❌ Excel导出失败")
            return False
        
        # 测试CSV导出
        csv_data = test_data.to_csv(index=False, encoding='utf-8-sig')
        if csv_data:
            print("✅ CSV导出功能正常")
        else:
            print("❌ CSV导出失败")
            return False
        
        # 测试JSON导出
        json_data = test_data.to_json(orient='records', force_ascii=False, indent=2)
        if json_data:
            print("✅ JSON导出功能正常")
        else:
            print("❌ JSON导出失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导出功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_database_operations():
    """测试数据库操作"""
    print("\n🗄️ 测试数据库操作...")
    
    try:
        from src.database_manager import DatabaseManager
        import pandas as pd
        
        db_manager = DatabaseManager()
        
        # 测试数据查询
        data = db_manager.get_data(limit=5)
        print(f"✅ 数据查询成功，获得 {len(data)} 条记录")
        
        # 测试历史记录
        history = db_manager.get_crawl_history(limit=3)
        print(f"✅ 历史记录查询成功，获得 {len(history)} 条记录")
        
        # 测试行业统计
        industries = db_manager.get_recent_industries(limit=3)
        print(f"✅ 行业统计查询成功，获得 {len(industries)} 个行业")
        
        # 测试搜索功能
        search_results = db_manager.search_companies("公司", industry="人工智能")
        print(f"✅ 搜索功能正常，找到 {len(search_results)} 条匹配记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始爬虫系统高级功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项高级测试
    test_results.append(("区域搜索功能", test_region_search()))
    test_results.append(("数据可视化", test_data_visualization()))
    test_results.append(("导出功能", test_export_functionality()))
    test_results.append(("数据库操作", test_database_operations()))
    
    # 询问是否进行真实爬取测试
    print("\n" + "=" * 60)
    print("⚠️ 真实数据爬取测试会访问外部网站")
    print("这可能需要较长时间，并且可能触发反爬机制")
    
    # 自动进行真实爬取测试（小规模）
    print("🔄 自动进行小规模真实爬取测试...")
    test_results.append(("真实数据爬取", test_crawler_real_data()))
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("📋 高级测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总测试项: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有高级测试通过！系统功能完整")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 项高级测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
