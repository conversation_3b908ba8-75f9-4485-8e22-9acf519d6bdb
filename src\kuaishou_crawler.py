#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手平台爬虫模块
基于Playwright实现快手平台的企业账号信息爬取
"""

import asyncio
import random
import time
import logging
import pandas as pd
from typing import List, Dict, Optional
from urllib.parse import quote
from .browser_manager import get_browser_manager

class KuaishouCrawler:
    """快手平台爬虫"""
    
    def __init__(self, headless: bool = True):
        """
        初始化快手爬虫
        
        Args:
            headless: 是否无头模式运行
        """
        self.headless = headless
        self.logger = logging.getLogger(__name__)
        self.browser_manager = get_browser_manager(headless=headless)
        
        # 快手平台配置
        self.base_url = "https://www.kuaishou.com"
        self.search_url = "https://www.kuaishou.com/search/user"
        
        # 搜索关键词模板
        self.keyword_templates = [
            "{industry}",
            "{industry}公司",
            "{industry}企业",
            "{industry}官方",
            "{region}{industry}",
            "{province}{industry}",
            "{city}{industry}"
        ]
        
        # 企业标识关键词
        self.business_indicators = [
            "公司", "企业", "集团", "有限", "股份",
            "官方", "品牌", "商家", "厂家", "供应商",
            "工作室", "团队", "机构", "中心"
        ]
    
    def _generate_search_keywords(self, industry: str, province: str = None, city: str = None) -> List[str]:
        """
        生成搜索关键词
        
        Args:
            industry: 行业关键词
            province: 省份
            city: 城市
            
        Returns:
            关键词列表
        """
        keywords = []
        region = f"{province}{city}" if province and city else (province or city or "")
        
        for template in self.keyword_templates:
            keyword = template.format(
                industry=industry,
                region=region,
                province=province or "",
                city=city or ""
            ).strip()
            
            if keyword and keyword not in keywords:
                keywords.append(keyword)
        
        return keywords[:5]  # 限制关键词数量
    
    async def search_users(self, keyword: str, max_results: int = 20) -> List[Dict]:
        """
        搜索快手用户
        
        Args:
            keyword: 搜索关键词
            max_results: 最大结果数量
            
        Returns:
            用户信息列表
        """
        self.logger.info(f"🔍 开始搜索快手用户: {keyword}")
        
        try:
            # 启动浏览器管理器
            await self.browser_manager.start()
            
            # 创建浏览器和页面
            browser = await self.browser_manager.get_or_create_browser()
            context = await self.browser_manager.create_context(browser)
            page = await self.browser_manager.create_page(context)
            
            # 构建搜索URL
            search_url = f"{self.search_url}?keyword={quote(keyword)}"
            self.logger.info(f"📍 访问搜索页面: {search_url}")
            
            # 访问搜索页面
            await page.goto(search_url, wait_until='networkidle', timeout=30000)
            
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            # 尝试等待用户卡片加载
            try:
                await page.wait_for_selector('.user-card, .user-item, [data-testid="user-card"]', timeout=10000)
                self.logger.info("✅ 用户卡片加载成功")
            except:
                self.logger.warning("⚠️ 未检测到标准用户卡片，尝试其他选择器")
            
            # 提取用户信息
            users = await self._extract_user_info(page, keyword, max_results)
            
            # 关闭页面和上下文
            await page.close()
            await self.browser_manager.close_context(context)
            
            self.logger.info(f"✅ 成功提取 {len(users)} 个用户信息")
            return users
            
        except Exception as e:
            self.logger.error(f"❌ 搜索用户失败: {e}")
            return []
    
    async def _extract_user_info(self, page, keyword: str, max_results: int) -> List[Dict]:
        """
        从页面提取用户信息
        
        Args:
            page: Playwright页面对象
            keyword: 搜索关键词
            max_results: 最大结果数量
            
        Returns:
            用户信息列表
        """
        users = []
        scroll_count = 0
        max_scrolls = 3
        
        while len(users) < max_results and scroll_count < max_scrolls:
            try:
                # 尝试多种选择器策略
                current_users = await page.evaluate('''
                    () => {
                        // 尝试多种可能的选择器
                        const selectors = [
                            '.user-card',
                            '.user-item', 
                            '[data-testid="user-card"]',
                            '.search-user-item',
                            '.user-info-card'
                        ];
                        
                        let userItems = [];
                        for (const selector of selectors) {
                            userItems = document.querySelectorAll(selector);
                            if (userItems.length > 0) break;
                        }
                        
                        if (userItems.length === 0) {
                            // 如果没有找到标准卡片，尝试通用方法
                            userItems = document.querySelectorAll('a[href*="/profile/"], a[href*="/user/"]');
                        }
                        
                        return Array.from(userItems).map(item => {
                            try {
                                // 提取用户名
                                const nameSelectors = [
                                    '.user-name', '.username', '.name', 
                                    '.user-title', '.profile-name',
                                    'h3', 'h4', '.title'
                                ];
                                let name = '';
                                for (const sel of nameSelectors) {
                                    const nameEl = item.querySelector(sel);
                                    if (nameEl && nameEl.textContent.trim()) {
                                        name = nameEl.textContent.trim();
                                        break;
                                    }
                                }
                                
                                // 提取链接
                                let url = '';
                                if (item.tagName === 'A') {
                                    url = item.href;
                                } else {
                                    const linkEl = item.querySelector('a[href*="/profile/"], a[href*="/user/"]');
                                    if (linkEl) url = linkEl.href;
                                }
                                
                                // 提取头像
                                const avatarEl = item.querySelector('img');
                                const avatar = avatarEl ? avatarEl.src : '';
                                
                                // 提取描述
                                const descSelectors = [
                                    '.user-desc', '.description', '.bio',
                                    '.user-signature', '.profile-desc'
                                ];
                                let description = '';
                                for (const sel of descSelectors) {
                                    const descEl = item.querySelector(sel);
                                    if (descEl && descEl.textContent.trim()) {
                                        description = descEl.textContent.trim();
                                        break;
                                    }
                                }
                                
                                // 提取粉丝数
                                const fansSelectors = [
                                    '.fans-count', '.follower-count', '.user-fans',
                                    '.follow-count', '.fan-num'
                                ];
                                let fans = '';
                                for (const sel of fansSelectors) {
                                    const fansEl = item.querySelector(sel);
                                    if (fansEl && fansEl.textContent.trim()) {
                                        fans = fansEl.textContent.trim();
                                        break;
                                    }
                                }
                                
                                return {
                                    name: name || '未知用户',
                                    url: url,
                                    avatar: avatar,
                                    description: description,
                                    fans: fans,
                                    platform: '快手'
                                };
                            } catch (e) {
                                return null;
                            }
                        }).filter(user => user && user.name && user.name !== '未知用户');
                    }
                ''')
                
                # 过滤和去重
                for user in current_users:
                    if user['url'] and not any(u['url'] == user['url'] for u in users):
                        # 检查是否可能是企业账号
                        if self._is_potential_business_account(user, keyword):
                            user['search_keyword'] = keyword
                            user['data_type'] = 'social_media'
                            user['source'] = 'kuaishou'
                            users.append(user)
                
                self.logger.info(f"📊 当前已提取 {len(users)} 个用户，滚动次数: {scroll_count + 1}")
                
                # 向下滚动加载更多
                await page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
                await page.wait_for_timeout(2000)
                scroll_count += 1
                
            except Exception as e:
                self.logger.error(f"❌ 提取用户信息时出错: {e}")
                break
        
        return users[:max_results]
    
    def _is_potential_business_account(self, user: Dict, keyword: str) -> bool:
        """
        判断是否可能是企业账号
        
        Args:
            user: 用户信息
            keyword: 搜索关键词
            
        Returns:
            是否可能是企业账号
        """
        name = user.get('name', '').lower()
        description = user.get('description', '').lower()
        
        # 检查名称和描述中是否包含企业标识
        text_to_check = f"{name} {description}"
        
        # 包含企业标识关键词
        for indicator in self.business_indicators:
            if indicator in text_to_check:
                return True
        
        # 包含搜索关键词
        if keyword.lower() in text_to_check:
            return True
        
        # 排除明显的个人账号
        personal_indicators = ["个人", "网红", "达人", "博主", "主播", "美女", "帅哥"]
        for indicator in personal_indicators:
            if indicator in text_to_check:
                return False
        
        return True
    
    async def search_by_industry(self, industry: str, province: str = None, city: str = None, 
                                max_results: int = 50) -> pd.DataFrame:
        """
        按行业搜索快手企业账号
        
        Args:
            industry: 行业关键词
            province: 省份
            city: 城市
            max_results: 最大结果数量
            
        Returns:
            包含企业信息的DataFrame
        """
        self.logger.info(f"🎯 开始搜索快手企业账号 - 行业: {industry}, 地区: {province} {city}")
        
        # 生成搜索关键词
        keywords = self._generate_search_keywords(industry, province, city)
        self.logger.info(f"📝 生成搜索关键词: {keywords}")
        
        all_users = []
        
        for keyword in keywords:
            try:
                # 搜索用户
                users = await self.search_users(keyword, max_results // len(keywords) + 5)
                all_users.extend(users)
                
                # 添加延迟避免请求过快
                await asyncio.sleep(random.uniform(2, 4))
                
            except Exception as e:
                self.logger.error(f"❌ 搜索关键词 '{keyword}' 失败: {e}")
                continue
        
        # 去重
        unique_users = []
        seen_urls = set()
        for user in all_users:
            if user['url'] not in seen_urls:
                seen_urls.add(user['url'])
                unique_users.append(user)
        
        # 转换为DataFrame
        if unique_users:
            df = pd.DataFrame(unique_users)
            
            # 添加统一字段
            df['company_name'] = df['name']
            df['industry'] = industry
            df['province'] = province
            df['city'] = city
            df['crawl_time'] = pd.Timestamp.now()
            
            # 限制结果数量
            df = df.head(max_results)
            
            self.logger.info(f"✅ 快手搜索完成，共获取 {len(df)} 条企业信息")
            return df
        else:
            self.logger.warning("⚠️ 未获取到任何企业信息")
            return pd.DataFrame()
    
    async def cleanup(self):
        """清理资源"""
        try:
            from .browser_manager import cleanup_browser_manager
            await cleanup_browser_manager()
            self.logger.info("✅ 快手爬虫资源清理完成")
        except Exception as e:
            self.logger.error(f"❌ 资源清理失败: {e}")

# 同步包装函数
def search_kuaishou_data(industry: str, province: str = None, city: str = None, 
                        max_results: int = 50, headless: bool = True) -> pd.DataFrame:
    """
    同步搜索快手数据的包装函数
    
    Args:
        industry: 行业关键词
        province: 省份
        city: 城市
        max_results: 最大结果数量
        headless: 是否无头模式
        
    Returns:
        包含企业信息的DataFrame
    """
    async def _search():
        crawler = KuaishouCrawler(headless=headless)
        try:
            return await crawler.search_by_industry(industry, province, city, max_results)
        finally:
            await crawler.cleanup()
    
    try:
        return asyncio.run(_search())
    except Exception as e:
        logging.error(f"❌ 快手数据搜索失败: {e}")
        return pd.DataFrame()
