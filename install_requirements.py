#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能依赖包安装脚本
根据用户需求选择合适的安装方案
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_pip():
    """检查pip是否可用"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip可用")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip不可用")
        return False

def install_requirements(requirements_file):
    """安装依赖包"""
    if not Path(requirements_file).exists():
        print(f"❌ 文件不存在: {requirements_file}")
        return False
    
    print(f"📦 安装依赖包: {requirements_file}")
    
    try:
        # 使用国内镜像源加速安装
        mirrors = [
            "https://pypi.tuna.tsinghua.edu.cn/simple/",
            "https://mirrors.aliyun.com/pypi/simple/",
            "https://pypi.douban.com/simple/"
        ]
        
        for mirror in mirrors:
            try:
                print(f"🔄 尝试使用镜像源: {mirror}")
                subprocess.run([
                    sys.executable, "-m", "pip", "install", 
                    "-r", requirements_file, 
                    "-i", mirror,
                    "--trusted-host", mirror.split("//")[1].split("/")[0]
                ], check=True, timeout=300)
                print("✅ 安装成功")
                return True
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                continue
        
        # 如果镜像源都失败，尝试默认源
        print("🔄 尝试使用默认源...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "-r", requirements_file
        ], check=True, timeout=600)
        print("✅ 安装成功")
        return True
        
    except Exception as e:
        print(f"❌ 安装失败: {e}")
        return False

def test_installation():
    """测试安装结果"""
    print("\n🧪 测试安装结果...")
    
    core_modules = [
        ("streamlit", "Web界面框架"),
        ("pandas", "数据处理"),
        ("requests", "HTTP请求"),
        ("plotly", "数据可视化"),
        ("yaml", "配置文件解析")
    ]
    
    failed_modules = []
    
    for module, description in core_modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description}")
            failed_modules.append(module)
    
    return len(failed_modules) == 0

def show_installation_options():
    """显示安装选项"""
    print("\n📋 安装选项:")
    print("1. 🚀 最小安装 (requirements_minimal.txt) - 快速体验")
    print("2. 🔧 标准安装 (requirements.txt) - 推荐大多数用户")
    print("3. 💪 完整安装 (requirements_complete.txt) - 包含所有功能")
    print("4. 🎯 真实爬取 (real_crawler_requirements.txt) - 专业数据爬取")
    print("5. 🛠️ 自定义安装")
    print("0. 退出")

def main():
    """主函数"""
    print("🔧 智能依赖包安装工具")
    print("=" * 50)
    
    # 检查环境
    if not check_python_version() or not check_pip():
        print("\n❌ 环境检查失败，请先解决上述问题")
        return
    
    # 显示选项
    show_installation_options()
    
    while True:
        try:
            choice = input("\n请选择安装方案 (0-5): ").strip()
            
            if choice == "0":
                print("👋 退出安装")
                break
            elif choice == "1":
                success = install_requirements("requirements_minimal.txt")
            elif choice == "2":
                success = install_requirements("requirements.txt")
            elif choice == "3":
                success = install_requirements("requirements_complete.txt")
            elif choice == "4":
                success = install_requirements("real_crawler_requirements.txt")
            elif choice == "5":
                custom_file = input("请输入requirements文件路径: ").strip()
                success = install_requirements(custom_file)
            else:
                print("❌ 无效选择，请重新输入")
                continue
            
            if success:
                print("\n🎉 安装完成！")
                
                # 测试安装
                if test_installation():
                    print("\n✅ 所有核心模块测试通过")
                    print("\n🚀 现在可以运行:")
                    print("python run_enhanced.py")
                    print("或")
                    print("python -m streamlit run main.py")
                else:
                    print("\n⚠️ 部分模块测试失败，但基础功能应该可用")
                
                break
            else:
                print("\n❌ 安装失败，请检查网络连接或尝试其他选项")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户取消安装")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
