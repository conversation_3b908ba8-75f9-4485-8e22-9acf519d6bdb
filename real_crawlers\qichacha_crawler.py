#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企查查真实数据爬虫
"""

import asyncio
import os
from typing import Dict, List, Optional
from urllib.parse import quote

from base_crawler import BaseCrawler

class Qi<PERSON>chaCrawler(BaseCrawler):
    """企查查数据爬虫"""
    
    def __init__(self):
        super().__init__("qichacha")
        self.base_url = "https://www.qcc.com"
        self.login_url = f"{self.base_url}/user_login"
        self.is_logged_in = False
        
    async def login(self) -> bool:
        """登录企查查"""
        username = os.getenv('QICHACHA_USERNAME')
        password = os.getenv('QICHACHA_PASSWORD')
        
        if not username or not password:
            raise Exception("请在.env文件中配置企查查账号密码")
        
        try:
            page = await self.create_page()
            
            print("🔐 开始登录企查查...")
            await page.goto(self.login_url, wait_until='networkidle')
            await self.wait_random(2, 3)
            
            # 输入用户名和密码
            await page.fill('#nameNormal', username)
            await page.fill('#pwdNormal', password)
            
            # 处理验证码
            await self._handle_login_captcha(page)
            
            # 点击登录按钮
            await page.click('#user_login_normal')
            await self.wait_random(3, 5)
            
            # 检查登录状态
            self.is_logged_in = await self._check_login_status(page)
            
            if self.is_logged_in:
                print("✅ 企查查登录成功")
                return True
            else:
                print("❌ 企查查登录失败")
                return False
                
        except Exception as e:
            print(f"❌ 登录过程出错: {str(e)}")
            return False

    async def _handle_login_captcha(self, page):
        """处理登录验证码"""
        try:
            # 检查是否有验证码
            captcha_img = await page.query_selector('#normalLoginCaptchaImg')
            if captcha_img:
                print("🔍 检测到验证码，请手动输入...")
                
                # 等待用户手动输入验证码
                await asyncio.sleep(10)
                
        except Exception as e:
            print(f"⚠️ 验证码处理失败: {str(e)}")

    async def _check_login_status(self, page) -> bool:
        """检查登录状态"""
        try:
            # 等待页面跳转
            await self.wait_random(2, 3)
            
            current_url = page.url
            
            # 如果还在登录页面，说明登录失败
            if 'user_login' in current_url:
                return False
            
            # 检查是否有用户信息元素
            user_info = await page.query_selector('.header-user-info, .user-info')
            return user_info is not None
            
        except Exception:
            return False

    async def search_company(self, company_name: str) -> Optional[str]:
        """搜索企业并返回第一个结果的链接"""
        try:
            if not self.is_logged_in:
                await self.login()
            
            page = await self.create_page()
            
            search_url = f"{self.base_url}/web/search?key={quote(company_name)}"
            print(f"🔍 搜索企业: {company_name}")
            
            await page.goto(search_url, wait_until='networkidle')
            await self.wait_random(3, 5)
            
            # 获取第一个搜索结果
            first_result = await page.query_selector('.search-result .title a, .company-item .title a')
            
            if first_result:
                company_url = await first_result.get_attribute('href')
                if company_url and not company_url.startswith('http'):
                    company_url = self.base_url + company_url
                
                print(f"✅ 找到企业链接: {company_url}")
                return company_url
            else:
                print(f"❌ 未找到企业: {company_name}")
                return None
                
        except Exception as e:
            print(f"❌ 搜索失败: {str(e)}")
            return None

    async def crawl_company_info(self, company_name: str) -> Dict:
        """爬取企业详细信息"""
        try:
            # 搜索企业
            company_url = await self.search_company(company_name)
            if not company_url:
                raise Exception(f"未找到企业: {company_name}")
            
            page = await self.create_page()
            
            print(f"📊 正在提取企业信息: {company_name}")
            await page.goto(company_url, wait_until='networkidle')
            await self.wait_random(3, 5)
            
            # 提取企业基本信息
            company_info = await self._extract_company_basic_info(page)
            
            # 提取更多详细信息
            detailed_info = await self._extract_company_detailed_info(page)
            
            # 合并信息
            result = {
                **company_info,
                **detailed_info,
                'crawl_time': self._get_timestamp(),
                'source_url': company_url,
                'source': 'qichacha'
            }
            
            # 保存数据
            safe_name = company_name.replace('/', '_').replace('\\', '_')
            self.save_data(result, f"company_{safe_name}")
            
            self.log_result(True, f"成功爬取企业信息: {company_name}")
            return result
            
        except Exception as e:
            self.log_result(False, f"企业信息爬取失败: {str(e)}")
            raise e

    async def _extract_company_basic_info(self, page) -> Dict:
        """提取企业基本信息"""
        basic_info = await page.evaluate("""
            () => {
                function getTextBySelectors(selectors) {
                    for (const selector of selectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            return element.textContent.trim();
                        }
                    }
                    return '';
                }
                
                function getTableValue(label) {
                    const rows = document.querySelectorAll('.company-base-info tr, .base-info tr, table tr');
                    for (const row of rows) {
                        const labelEl = row.querySelector('td:first-child, th:first-child');
                        const valueEl = row.querySelector('td:last-child');
                        if (labelEl && labelEl.textContent.includes(label) && valueEl) {
                            return valueEl.textContent.trim();
                        }
                    }
                    return '';
                }
                
                return {
                    company_name: getTextBySelectors([
                        '.company-top .title',
                        '.company-name',
                        'h1',
                        '.header-title'
                    ]),
                    legal_person: getTableValue('法定代表人') || getTextBySelectors([
                        '.legal-person',
                        '.legal-representative'
                    ]),
                    registered_capital: getTableValue('注册资本'),
                    establish_date: getTableValue('成立日期') || getTableValue('成立时间'),
                    business_status: getTableValue('经营状态') || getTableValue('企业状态'),
                    company_type: getTableValue('企业类型') || getTableValue('公司类型'),
                    credit_code: getTableValue('统一社会信用代码') || getTableValue('信用代码'),
                    registration_number: getTableValue('工商注册号') || getTableValue('注册号'),
                    registered_address: getTableValue('注册地址') || getTableValue('地址'),
                    business_scope: getTableValue('经营范围') || getTextBySelectors([
                        '.business-scope',
                        '.scope-content'
                    ]),
                    business_term: getTableValue('营业期限') || getTableValue('经营期限'),
                    industry: getTableValue('所属行业') || getTableValue('行业')
                };
            }
        """)
        
        return basic_info

    async def _extract_company_detailed_info(self, page) -> Dict:
        """提取企业详细信息"""
        detailed_info = await page.evaluate("""
            () => {
                function getContactInfo() {
                    const contactSelectors = {
                        phone: ['.contact-info .phone', '.phone-number', '.tel'],
                        email: ['.contact-info .email', '.email-address'],
                        website: ['.contact-info .website', '.company-website', '.website']
                    };
                    
                    const contact = {};
                    for (const [key, selectors] of Object.entries(contactSelectors)) {
                        for (const selector of selectors) {
                            const element = document.querySelector(selector);
                            if (element) {
                                contact[key] = element.textContent.trim();
                                break;
                            }
                        }
                    }
                    return contact;
                }
                
                function getShareholderInfo() {
                    const shareholders = [];
                    const shareholderRows = document.querySelectorAll('.shareholder-table tr, .equity-table tr');
                    
                    shareholderRows.forEach(row => {
                        const cells = row.querySelectorAll('td');
                        if (cells.length >= 2) {
                            shareholders.push({
                                name: cells[0]?.textContent.trim() || '',
                                ratio: cells[1]?.textContent.trim() || ''
                            });
                        }
                    });
                    
                    return shareholders;
                }
                
                return {
                    contact_info: getContactInfo(),
                    shareholders: getShareholderInfo(),
                    staff_size: document.querySelector('.staff-size, .employee-count')?.textContent.trim() || '',
                    annual_report_year: document.querySelector('.annual-report-year')?.textContent.trim() || ''
                };
            }
        """)
        
        return detailed_info

    async def batch_crawl_companies(self, company_names: List[str]) -> List[Dict]:
        """批量爬取企业信息"""
        results = []
        
        # 先登录一次
        if not self.is_logged_in:
            await self.login()
        
        for i, company_name in enumerate(company_names):
            print(f"\n📊 进度: {i+1}/{len(company_names)} - {company_name}")
            
            try:
                result = await self.crawl_company_info(company_name)
                results.append({
                    'success': True,
                    'company_name': company_name,
                    'data': result
                })
            except Exception as e:
                results.append({
                    'success': False,
                    'company_name': company_name,
                    'error': str(e)
                })
            
            # 添加延迟避免被封
            if i < len(company_names) - 1:
                await asyncio.sleep(5)
        
        return results

    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
