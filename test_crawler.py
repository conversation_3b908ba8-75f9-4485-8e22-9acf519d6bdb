#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫系统测试脚本
测试各个模块的功能是否正常
"""

import sys
import os
from pathlib import Path
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from src.crawler_engine import CrawlerEngine
        print("✅ CrawlerEngine 导入成功")
    except Exception as e:
        print(f"❌ CrawlerEngine 导入失败: {e}")
        return False
    
    try:
        from src.data_processor import DataProcessor
        print("✅ DataProcessor 导入成功")
    except Exception as e:
        print(f"❌ DataProcessor 导入失败: {e}")
        return False
    
    try:
        from src.database_manager import DatabaseManager
        print("✅ DatabaseManager 导入成功")
    except Exception as e:
        print(f"❌ DatabaseManager 导入失败: {e}")
        return False
    
    try:
        from src.visualizer import DataVisualizer
        print("✅ DataVisualizer 导入成功")
    except Exception as e:
        print(f"❌ DataVisualizer 导入失败: {e}")
        return False
    
    try:
        from src.region_manager import RegionManager
        print("✅ RegionManager 导入成功")
    except Exception as e:
        print(f"❌ RegionManager 导入失败: {e}")
        return False
    
    return True

def test_database():
    """测试数据库连接"""
    print("\n🗄️ 测试数据库连接...")
    
    try:
        from src.database_manager import DatabaseManager
        db = DatabaseManager()
        
        # 测试连接
        total_records = db.get_total_records()
        print(f"✅ 数据库连接成功，当前记录数: {total_records}")
        
        # 测试表结构
        tables = db.get_table_info()
        print(f"✅ 数据库表结构正常，共 {len(tables)} 个表")
        
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        traceback.print_exc()
        return False

def test_config():
    """测试配置文件"""
    print("\n⚙️ 测试配置文件...")
    
    try:
        import yaml
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必要的配置节
        required_sections = ['database', 'crawler', 'search_engines', 'regions']
        for section in required_sections:
            if section in config:
                print(f"✅ 配置节 {section} 存在")
            else:
                print(f"❌ 配置节 {section} 缺失")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_region_manager():
    """测试区域管理器"""
    print("\n🌍 测试区域管理器...")
    
    try:
        from src.region_manager import RegionManager
        rm = RegionManager()
        
        # 测试获取省份列表
        provinces = rm.get_all_provinces()
        print(f"✅ 获取省份列表成功，共 {len(provinces)} 个省份")
        
        # 测试获取城市列表
        if provinces:
            cities = rm.get_cities_by_province(provinces[0])
            print(f"✅ 获取 {provinces[0]} 城市列表成功，共 {len(cities)} 个城市")
        
        # 测试区域验证
        is_valid, message = rm.validate_region("广东省", "深圳市")
        if is_valid:
            print("✅ 区域验证功能正常")
        else:
            print(f"❌ 区域验证失败: {message}")
        
        return True
    except Exception as e:
        print(f"❌ 区域管理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_crawler_basic():
    """测试爬虫基本功能"""
    print("\n🕷️ 测试爬虫基本功能...")
    
    try:
        from src.crawler_engine import CrawlerEngine
        crawler = CrawlerEngine()
        
        # 测试配置加载
        if hasattr(crawler, 'config') and crawler.config:
            print("✅ 爬虫配置加载成功")
        else:
            print("❌ 爬虫配置加载失败")
            return False
        
        # 测试会话初始化
        if hasattr(crawler, 'session') and crawler.session:
            print("✅ 爬虫会话初始化成功")
        else:
            print("❌ 爬虫会话初始化失败")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 爬虫基本功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_data_processor():
    """测试数据处理器"""
    print("\n📊 测试数据处理器...")
    
    try:
        from src.data_processor import DataProcessor
        import pandas as pd
        
        processor = DataProcessor()
        
        # 创建测试数据
        test_data = [
            {
                'company_name': '测试公司A',
                'industry': '人工智能',
                'website': 'https://test-a.com',
                'phone': '010-12345678',
                'email': '<EMAIL>',
                'address': '北京市朝阳区',
                'source': 'test'
            },
            {
                'company_name': '测试公司B',
                'industry': '人工智能',
                'website': 'https://test-b.com',
                'phone': '021-87654321',
                'email': '<EMAIL>',
                'address': '上海市浦东新区',
                'source': 'test'
            }
        ]
        
        df = pd.DataFrame(test_data)
        processed_df = processor.process_data(df)
        
        if not processed_df.empty:
            print(f"✅ 数据处理成功，处理了 {len(processed_df)} 条数据")
            return True
        else:
            print("❌ 数据处理失败，返回空数据")
            return False
            
    except Exception as e:
        print(f"❌ 数据处理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_network():
    """测试网络连接"""
    print("\n🌐 测试网络连接...")
    
    try:
        import requests
        
        # 测试百度连接
        response = requests.get("https://www.baidu.com", timeout=10)
        if response.status_code == 200:
            print("✅ 百度连接正常")
        else:
            print(f"❌ 百度连接异常: {response.status_code}")
        
        # 测试必应连接
        response = requests.get("https://www.bing.com", timeout=10)
        if response.status_code == 200:
            print("✅ 必应连接正常")
        else:
            print(f"❌ 必应连接异常: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ 网络连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始爬虫系统全面测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("模块导入", test_imports()))
    test_results.append(("配置文件", test_config()))
    test_results.append(("数据库连接", test_database()))
    test_results.append(("区域管理器", test_region_manager()))
    test_results.append(("爬虫基本功能", test_crawler_basic()))
    test_results.append(("数据处理器", test_data_processor()))
    test_results.append(("网络连接", test_network()))
    
    # 汇总测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总测试项: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统运行正常")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 项测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
