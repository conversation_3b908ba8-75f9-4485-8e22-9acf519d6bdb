#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能爬虫系统演示脚本
展示升级后的智能分析和数据采集功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_intelligent_analysis():
    """演示智能分析功能"""
    print("🧠 智能行业分析演示")
    print("=" * 50)
    
    from intelligent_analysis_framework import IntelligentAnalyzer
    
    analyzer = IntelligentAnalyzer()
    
    # 演示不同行业的分析
    demo_industries = [
        "酒店建设",
        "房地产开发", 
        "星级酒店装修工程",
        "商业地产投资"
    ]
    
    for industry in demo_industries:
        print(f"\n🎯 分析行业: {industry}")
        print("-" * 30)
        
        try:
            result = analyzer.analyze_industry_input(industry)
            
            # 显示分析结果
            industry_info = result['industry_info']
            print(f"识别行业: {industry_info['primary_industry']}")
            print(f"业务模式: {industry_info['business_model']}")
            
            # 显示数据源
            data_sources = result['data_sources']
            print(f"专业数据源: {len(data_sources)} 个")
            
            # 显示搜索策略
            search_strategy = result['search_strategy']
            print(f"主要关键词: {', '.join(search_strategy.primary_keywords[:3])}")
            
            # 显示产业链
            value_chain = result['value_chain']
            if value_chain:
                print(f"产业链环节: {', '.join(value_chain.keys())}")
            
        except Exception as e:
            print(f"分析失败: {e}")

def demo_enhanced_crawler():
    """演示增强爬虫功能"""
    print("\n\n🕷️ 增强爬虫系统演示")
    print("=" * 50)
    
    try:
        from src.crawler_engine import CrawlerEngine
        from src.data_processor import DataProcessor
        
        crawler = CrawlerEngine()
        processor = DataProcessor()
        
        # 检查集成的组件
        components = []
        if hasattr(crawler, 'professional_crawler') and crawler.professional_crawler:
            components.append("专业爬虫")
        if hasattr(crawler, 'enhanced_search') and crawler.enhanced_search:
            components.append("增强搜索")
        
        print(f"✅ 集成组件: {', '.join(components)}")
        
        # 演示配置
        demo_config = {
            'industry': '酒店建设',
            'province': '广东省',
            'city': '深圳市',
            'search_depth': 1,
            'use_baidu': False,
            'use_bing': False,
            'use_business_dirs': False,
            'use_professional_sources': False,
            'use_enhanced_search': True
        }
        
        print(f"\n🔧 演示配置:")
        for key, value in demo_config.items():
            print(f"  {key}: {value}")
        
        print(f"\n🚀 开始小规模数据采集演示...")
        
        # 执行采集
        raw_data = crawler.start_crawling(demo_config)
        
        if raw_data.empty:
            print("⚠️ 演示采集未获得数据（这是正常的，可能是网络限制）")
        else:
            print(f"✅ 演示采集成功，获得 {len(raw_data)} 条数据")
            
            # 数据处理演示
            processed_data = processor.process_data(raw_data)
            print(f"✅ 数据处理完成，处理后 {len(processed_data)} 条数据")
            
            # 显示数据样本
            if not processed_data.empty:
                print("\n📋 数据样本:")
                for i, row in processed_data.head(2).iterrows():
                    print(f"{i+1}. {row.get('公司名称', 'N/A')}")
                    print(f"   行业: {row.get('行业', 'N/A')}")
                    print(f"   来源: {row.get('数据源', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 增强爬虫演示失败: {e}")
        import traceback
        traceback.print_exc()

def demo_web_interface():
    """演示Web界面功能"""
    print("\n\n🌐 Web界面功能演示")
    print("=" * 50)
    
    print("✅ Streamlit Web界面已启动")
    print("📍 访问地址: http://localhost:8501")
    
    print("\n🎯 新增智能功能:")
    print("1. 🧠 智能行业分析")
    print("   - 自动识别行业类型和特征")
    print("   - 智能推荐专业数据源")
    print("   - 生成针对性搜索策略")
    
    print("\n2. 📡 智能数据源配置")
    print("   - 专业行业网站优先推荐")
    print("   - 增强搜索引擎集成")
    print("   - 多层次数据质量控制")
    
    print("\n3. 🔍 实时分析展示")
    print("   - 产业链结构分析")
    print("   - 关键参与者识别")
    print("   - 搜索策略预览")
    
    print("\n4. 📊 智能数据处理")
    print("   - 多源信息验证")
    print("   - 智能去重合并")
    print("   - 关系图谱构建")

def demo_system_advantages():
    """演示系统优势"""
    print("\n\n🚀 系统优势总结")
    print("=" * 50)
    
    advantages = [
        {
            "title": "🧠 智能化程度高",
            "description": "根据行业特点自动调整采集策略，无需手动配置"
        },
        {
            "title": "🎯 专业性强",
            "description": "专注招标、房地产、酒店等高价值商业领域"
        },
        {
            "title": "📡 数据源丰富",
            "description": "集成政府网站、行业协会、专业媒体等多种数据源"
        },
        {
            "title": "🔍 精准度高",
            "description": "智能过滤无关信息，提取关键商业数据"
        },
        {
            "title": "🛡️ 稳定性强",
            "description": "多重反爬策略，绕过访问限制"
        },
        {
            "title": "📊 分析能力强",
            "description": "构建企业关系图谱，识别商业机会"
        }
    ]
    
    for i, advantage in enumerate(advantages, 1):
        print(f"{i}. {advantage['title']}")
        print(f"   {advantage['description']}")
        print()

def main():
    """主演示函数"""
    print("🎉 智能爬虫系统全功能演示")
    print("=" * 60)
    print("基于您的深化思路：输入分析 → 资源识别 → 策略制定 → 分层采集 → 智能整合")
    print("=" * 60)
    
    # 运行各项演示
    demo_intelligent_analysis()
    demo_enhanced_crawler()
    demo_web_interface()
    demo_system_advantages()
    
    print("\n" + "=" * 60)
    print("🎯 使用建议")
    print("=" * 60)
    
    suggestions = [
        "1. 🌐 访问 http://localhost:8501 使用Web界面",
        "2. 🎯 输入专业行业词汇（如：酒店建设、房地产开发）",
        "3. 🧠 查看智能分析结果，了解行业特征",
        "4. 📡 启用专业数据源和增强搜索",
        "5. 🔍 开始智能数据采集",
        "6. 📊 查看分析结果和可视化图表",
        "7. 💾 导出高质量的企业联系信息"
    ]
    
    for suggestion in suggestions:
        print(suggestion)
    
    print("\n🎉 系统已完全就绪，可以开始实际使用！")

if __name__ == "__main__":
    main()
