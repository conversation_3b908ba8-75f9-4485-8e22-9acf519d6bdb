#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于浏览器自动化的数据抓取器
使用Selenium获取真实的搜索结果
"""

import sys
import os
from pathlib import Path
import time
import random
import re
import pandas as pd
from urllib.parse import quote

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("⚠️ Selenium未安装，将使用备用方案")

class BrowserCrawler:
    """基于浏览器的数据抓取器"""
    
    def __init__(self):
        self.driver = None
        self.setup_driver()
        
    def setup_driver(self):
        """设置浏览器驱动"""
        if not SELENIUM_AVAILABLE:
            print("❌ Selenium不可用，无法使用浏览器抓取")
            return

        try:
            chrome_options = Options()
            # 不使用无头模式，以便调试
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 尝试创建Chrome驱动
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            print("✅ Chrome浏览器驱动初始化成功")

        except Exception as e:
            print(f"❌ 浏览器驱动初始化失败: {e}")
            print("💡 请确保已安装Chrome浏览器和ChromeDriver")
            print("💡 或者下载ChromeDriver并放在PATH中")
            self.driver = None
    
    def search_baidu(self, keyword, max_results=10):
        """使用百度搜索"""
        if not self.driver:
            return []
            
        results = []
        
        try:
            print(f"🔍 百度搜索: {keyword}")
            
            # 访问百度
            search_url = f"https://www.baidu.com/s?wd={quote(keyword)}"
            self.driver.get(search_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "content_left"))
            )
            
            # 查找搜索结果
            result_elements = self.driver.find_elements(By.CSS_SELECTOR, ".result")
            
            print(f"  找到 {len(result_elements)} 个搜索结果")
            
            for i, element in enumerate(result_elements[:max_results]):
                try:
                    # 提取标题
                    title_elem = element.find_element(By.CSS_SELECTOR, "h3 a")
                    title = title_elem.text.strip()
                    url = title_elem.get_attribute('href')
                    
                    # 提取描述
                    try:
                        desc_elem = element.find_element(By.CSS_SELECTOR, ".c-abstract")
                        description = desc_elem.text.strip()
                    except NoSuchElementException:
                        description = ""
                    
                    # 检查是否包含企业信息
                    if self._is_company_related(title, description, keyword):
                        company_name = self._extract_company_name(title, description)
                        if company_name:
                            results.append({
                                'company_name': company_name,
                                'source': '百度搜索',
                                'data_type': 'search_result',
                                'description': description[:200],
                                'url': url,
                                'search_keyword': keyword,
                                'title': title
                            })
                            
                except Exception as e:
                    continue
            
            print(f"  ✅ 提取到 {len(results)} 条企业信息")
            
        except Exception as e:
            print(f"  ❌ 百度搜索失败: {e}")
        
        return results
    
    def search_sogou(self, keyword, max_results=10):
        """使用搜狗搜索"""
        if not self.driver:
            return []
            
        results = []
        
        try:
            print(f"🔍 搜狗搜索: {keyword}")
            
            # 访问搜狗
            search_url = f"https://www.sogou.com/web?query={quote(keyword)}"
            self.driver.get(search_url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 查找搜索结果
            result_elements = self.driver.find_elements(By.CSS_SELECTOR, ".results .vrwrap")
            
            print(f"  找到 {len(result_elements)} 个搜索结果")
            
            for i, element in enumerate(result_elements[:max_results]):
                try:
                    # 提取标题
                    title_elem = element.find_element(By.CSS_SELECTOR, "h3 a")
                    title = title_elem.text.strip()
                    url = title_elem.get_attribute('href')
                    
                    # 提取描述
                    try:
                        desc_elem = element.find_element(By.CSS_SELECTOR, ".str_info")
                        description = desc_elem.text.strip()
                    except NoSuchElementException:
                        description = ""
                    
                    # 检查是否包含企业信息
                    if self._is_company_related(title, description, keyword):
                        company_name = self._extract_company_name(title, description)
                        if company_name:
                            results.append({
                                'company_name': company_name,
                                'source': '搜狗搜索',
                                'data_type': 'search_result',
                                'description': description[:200],
                                'url': url,
                                'search_keyword': keyword,
                                'title': title
                            })
                            
                except Exception as e:
                    continue
            
            print(f"  ✅ 提取到 {len(results)} 条企业信息")
            
        except Exception as e:
            print(f"  ❌ 搜狗搜索失败: {e}")
        
        return results
    
    def _is_company_related(self, title, description, keyword):
        """判断是否与企业相关"""
        text = (title + ' ' + description).lower()
        
        # 企业相关关键词
        company_keywords = [
            '公司', '企业', '集团', '有限', '股份', '科技', '实业', 
            '建设', '开发', '管理', '服务', '工程', '装饰', '设计'
        ]
        
        # 检查是否包含企业关键词
        has_company_keyword = any(kw in text for kw in company_keywords)
        
        # 检查是否包含搜索关键词的部分
        keyword_parts = keyword.replace('公司', '').replace('企业', '').replace('有限', '').strip().split()
        has_search_keyword = any(kw in text for kw in keyword_parts if len(kw) > 1)
        
        return has_company_keyword and has_search_keyword
    
    def _extract_company_name(self, title, description):
        """提取企业名称"""
        text = title + ' ' + description
        
        # 企业名称正则模式
        patterns = [
            r'([^，。！？\s]{2,30}(?:有限公司|股份有限公司|集团有限公司|企业集团|科技有限公司))',
            r'([^，。！？\s]{2,30}(?:建设|工程|装饰|设计|开发|管理)(?:有限公司|公司))',
            r'([^，。！？\s]{2,30}(?:实业|科技|投资|控股)(?:有限公司|集团|公司))'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                # 清理和验证企业名称
                company_name = match.strip()
                if len(company_name) >= 4 and len(company_name) <= 50:
                    # 过滤掉明显不是企业名称的内容
                    if not any(word in company_name for word in ['网站', '搜索', '百度', '谷歌', '页面', '首页']):
                        return company_name
        
        return None
    
    def search_industry_data(self, industry, province=None, city=None, max_results=20):
        """搜索行业数据"""
        if not self.driver:
            print("❌ 浏览器驱动不可用")
            return []
            
        print(f"🏢 开始搜索行业数据: {industry}")
        
        # 构建搜索关键词
        keywords = self._build_search_keywords(industry, province, city)
        
        all_results = []
        
        for keyword in keywords[:2]:  # 限制关键词数量
            try:
                # 百度搜索
                baidu_results = self.search_baidu(keyword, max_results=8)
                all_results.extend(baidu_results)
                
                time.sleep(random.uniform(3, 5))
                
                # 搜狗搜索
                sogou_results = self.search_sogou(keyword, max_results=8)
                all_results.extend(sogou_results)
                
                time.sleep(random.uniform(3, 5))
                
            except Exception as e:
                print(f"搜索关键词 '{keyword}' 失败: {e}")
                continue
        
        # 去重和清理
        final_results = self._clean_and_deduplicate(all_results)
        
        return final_results[:max_results]
    
    def _build_search_keywords(self, industry, province=None, city=None):
        """构建搜索关键词"""
        keywords = []
        
        # 基础关键词
        base_keywords = [
            f"{industry}公司",
            f"{industry}企业"
        ]
        
        # 添加地区信息
        if city:
            for base in base_keywords:
                keywords.append(f"{city} {base}")
                
        if province and not city:
            for base in base_keywords:
                keywords.append(f"{province} {base}")
        
        # 如果没有地区信息，使用基础关键词
        if not keywords:
            keywords = base_keywords
            
        return keywords
    
    def _clean_and_deduplicate(self, results):
        """清理和去重结果"""
        if not results:
            return []
        
        # 转换为DataFrame进行处理
        df = pd.DataFrame(results)
        
        # 去重（基于企业名称）
        df = df.drop_duplicates(subset=['company_name'], keep='first')
        
        # 过滤掉无效的企业名称
        df = df[df['company_name'].str.len() >= 4]
        df = df[df['company_name'].str.len() <= 50]
        
        # 过滤掉明显不是企业的结果
        invalid_keywords = ['网站', '搜索', '百度', '谷歌', '页面', '首页', '官网']
        for keyword in invalid_keywords:
            df = df[~df['company_name'].str.contains(keyword, na=False)]
        
        return df.to_dict('records')
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("✅ 浏览器已关闭")

def test_browser_crawler():
    """测试浏览器爬虫"""
    print("🚀 测试基于浏览器的数据抓取器")
    print("=" * 60)
    
    if not SELENIUM_AVAILABLE:
        print("❌ Selenium不可用，请安装: pip install selenium")
        return
    
    crawler = BrowserCrawler()
    
    if not crawler.driver:
        print("❌ 浏览器驱动初始化失败")
        return
    
    try:
        # 测试案例
        test_cases = [
            {
                "industry": "酒店管理",
                "province": None,
                "city": "北京"
            },
            {
                "industry": "房地产开发", 
                "province": None,
                "city": "上海"
            }
        ]
        
        all_results = []
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n📋 测试案例 {i}: {case['industry']}")
            if case['city']:
                print(f"地区: {case['city']}")
            
            try:
                results = crawler.search_industry_data(
                    industry=case['industry'],
                    province=case['province'],
                    city=case['city'],
                    max_results=15
                )
                
                print(f"✅ 收集到 {len(results)} 条数据")
                
                # 显示样例
                if results:
                    print("数据样例:")
                    for idx, result in enumerate(results[:3], 1):
                        print(f"  {idx}. {result['company_name']}")
                        print(f"     来源: {result['source']}")
                        print(f"     描述: {result['description'][:50]}...")
                
                all_results.extend(results)
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        # 保存结果
        if all_results:
            df = pd.DataFrame(all_results)
            output_file = "data/browser_crawler_results.csv"
            os.makedirs("data", exist_ok=True)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n💾 结果已保存到: {output_file}")
            print(f"📊 总计收集: {len(all_results)} 条数据")
        else:
            print("\n❌ 未收集到任何数据")
            
    finally:
        crawler.close()

if __name__ == "__main__":
    test_browser_crawler()
