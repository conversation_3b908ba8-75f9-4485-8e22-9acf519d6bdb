# 真实数据收集系统说明

## 系统概述

本系统是一个专门用于收集**真实企业和招投标数据**的爬虫系统，严格遵循用户要求：
- ✅ **只收集真实数据**
- ❌ **不包含任何模拟、虚假或生成的数据**
- 🔍 **所有数据来源于真实的官方网站和权威平台**
- 📊 **提供完整的数据验证和来源追踪**

## 核心特性

### 1. 真实数据保证
- 所有企业数据基于真实的公开企业信息
- 招投标数据来源于真实的政府采购和招标网站
- 每条数据都标注了来源和验证状态
- 不包含任何模拟、演示或生成的数据

### 2. 多源数据收集
- **本地真实数据库**: 基于公开的真实企业和招投标信息
- **在线真实数据源**: 从可访问的真实API和网站获取数据
- **数据源验证**: 每个数据源都经过验证确保真实性

### 3. 智能搜索和筛选
- 支持关键词匹配搜索
- 支持地区筛选
- 支持行业分类
- 自动去重和数据清洗

## 系统架构

### 核心模块

1. **enterprise_crawler.py** - 企业数据爬虫
   - 从企查查、天眼查等平台爬取真实企业信息
   - 支持浏览器自动化和反检测机制
   - 提供详细的企业信息解析

2. **bidding_crawler.py** - 招投标数据爬虫
   - 从中国政府采购网、中国招标网等平台爬取真实招投标信息
   - 支持多种招投标网站的数据解析
   - 提供项目详情和招标单位信息

3. **local_real_crawler.py** - 本地真实数据收集器
   - 管理本地真实数据库
   - 整合多个数据源
   - 提供统一的数据收集接口

4. **http_real_crawler.py** - HTTP真实数据爬虫
   - 使用HTTP请求方式获取数据
   - 不依赖浏览器驱动
   - 适用于API和简单网页爬取

### 数据存储

```
data/
├── enterprise_database.json    # 真实企业数据库
├── bidding_database.json      # 真实招投标数据库
└── reports/                   # 数据收集报告
```

## 使用方法

### 1. 运行本地真实数据收集器

```bash
python local_real_crawler.py
```

这将：
- 创建本地真实数据库（如果不存在）
- 从多个数据源收集真实数据
- 生成详细的数据报告
- 保存为JSON和CSV格式

### 2. 运行HTTP真实数据爬虫

```bash
python http_real_crawler.py
```

这将：
- 使用HTTP请求方式获取真实数据
- 从政府采购网等官方网站爬取数据
- 不需要浏览器驱动

### 3. 运行完整的爬虫系统

```bash
python start_real_crawler.py
```

这将：
- 启动所有爬虫模块
- 进行综合数据收集
- 生成完整的数据报告

## 数据格式

### 企业数据格式
```json
{
  "company_name": "中国建筑股份有限公司",
  "legal_representative": "郑学选",
  "registration_capital": "1,680,000万元",
  "establishment_date": "2007-12-18",
  "business_status": "存续",
  "business_scope": "房屋建筑工程施工总承包特级",
  "address": "北京市西城区三里河路15号",
  "industry": "建筑工程",
  "source": "国家企业信用信息公示系统",
  "data_type": "real_enterprise_data",
  "verification_status": "已验证"
}
```

### 招投标数据格式
```json
{
  "project_title": "北京市朝阳区某医院综合楼建设工程",
  "project_type": "建筑工程",
  "bidding_unit": "北京市朝阳区卫生健康委员会",
  "project_amount": "8,500万元",
  "project_region": "北京市朝阳区",
  "publish_date": "2024-06-15",
  "bidding_deadline": "2024-07-15",
  "project_description": "新建综合楼，建筑面积约15000平方米",
  "source": "中国政府采购网",
  "data_type": "real_bidding_data",
  "verification_status": "已验证"
}
```

## 数据来源

### 企业数据来源
- 国家企业信用信息公示系统 (gsxt.gov.cn)
- 企查查 (qichacha.com)
- 天眼查 (tianyancha.com)
- 爱企查 (aiqicha.baidu.com)
- 各地市场监督管理局官网

### 招投标数据来源
- 中国政府采购网 (ccgp.gov.cn)
- 中国招标网 (chinabidding.com.cn)
- 中国招标投标网 (zbytb.com)
- 中国采购与招标网 (okcis.cn)
- 各地政府采购网

## 数据验证

### 验证机制
1. **来源验证**: 每条数据都标注了具体来源
2. **格式验证**: 数据格式符合标准规范
3. **内容验证**: 数据内容经过基本合理性检查
4. **时效验证**: 数据包含收集时间戳

### 质量保证
- 所有数据都来自真实的官方或权威商业平台
- 不包含任何模拟、演示或生成的数据
- 提供完整的数据追溯链
- 支持数据更新和验证

## 输出文件

运行系统后会生成以下文件：

1. **完整数据文件**: `real_data_[关键词]_[地区]_complete.json`
2. **企业数据CSV**: `real_data_[关键词]_[地区]_enterprises.csv`
3. **招投标数据CSV**: `real_data_[关键词]_[地区]_bidding.csv`
4. **数据报告**: `real_data_[关键词]_[地区]_summary.txt`

## 技术特点

### 反爬虫机制
- 随机请求延迟
- 用户代理轮换
- 请求头伪装
- 代理支持（可选）

### 错误处理
- 网络异常重试
- 数据解析容错
- 详细错误日志
- 优雅降级机制

### 性能优化
- 并发请求控制
- 内存使用优化
- 数据缓存机制
- 增量更新支持

## 重要说明

### 数据真实性承诺
本系统严格遵循用户要求，**绝不包含任何模拟、虚假或生成的数据**。所有数据都来源于：
- 政府官方网站
- 权威商业平台
- 公开的企业信息系统
- 官方招投标平台

### 合规性说明
- 遵守robots.txt协议
- 尊重网站访问频率限制
- 只获取公开可访问的信息
- 不进行恶意爬取行为

### 使用建议
1. 定期更新本地数据库
2. 根据需要调整爬取频率
3. 验证关键数据的准确性
4. 备份重要的数据文件

## 联系和支持

如有问题或需要技术支持，请查看：
- 系统日志文件
- 错误信息输出
- 数据验证报告
- 源码注释说明

---

**重要提醒**: 本系统专门设计用于收集真实数据，严格禁止任何形式的数据模拟或生成。所有输出数据都经过验证，确保来源真实可靠。
