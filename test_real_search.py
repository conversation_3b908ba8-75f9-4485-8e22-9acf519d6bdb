#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实搜索功能
使用更精确的关键词进行搜索
"""

import sys
import os
from pathlib import Path
import requests
import time
import random
from urllib.parse import quote
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_better_keywords():
    """测试更好的搜索关键词"""
    print("🔍 测试更精确的搜索关键词...")
    
    # 更精确的搜索关键词
    keywords = [
        "深圳人工智能公司 官网",
        "深圳AI科技有限公司",
        "深圳人工智能企业名录",
        "深圳机器学习公司",
        "深圳智能科技公司",
        "深圳人工智能有限公司"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    for i, keyword in enumerate(keywords):
        print(f"\n--- 测试关键词 {i+1}: {keyword} ---")
        
        try:
            # 必应搜索
            url = f"https://www.bing.com/search?q={quote(keyword)}"
            response = session.get(url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找搜索结果
                result_items = soup.find_all('li', class_='b_algo')
                print(f"找到 {len(result_items)} 个搜索结果")
                
                company_results = []
                for item in result_items:
                    try:
                        # 提取标题和链接
                        title_elem = item.find('h2')
                        if not title_elem:
                            continue
                            
                        link_elem = title_elem.find('a')
                        if not link_elem:
                            continue
                        
                        title = title_elem.get_text().strip()
                        link = link_elem.get('href', '')
                        
                        # 提取描述
                        desc_elem = item.find('p')
                        description = desc_elem.get_text().strip() if desc_elem else ""
                        
                        # 检查是否包含公司相关信息
                        if any(word in title for word in ['公司', '企业', '科技', '有限', '股份']):
                            company_results.append({
                                'title': title,
                                'link': link,
                                'description': description[:100] + '...' if len(description) > 100 else description
                            })
                    
                    except Exception as e:
                        continue
                
                if company_results:
                    print(f"✅ 找到 {len(company_results)} 个公司相关结果:")
                    for j, result in enumerate(company_results[:3]):
                        print(f"  {j+1}. {result['title']}")
                        print(f"     {result['link']}")
                        print(f"     {result['description']}")
                        print()
                else:
                    print("⚠️ 未找到公司相关结果")
                    # 显示前3个结果的标题
                    print("前3个结果:")
                    for j, item in enumerate(result_items[:3]):
                        title_elem = item.find('h2')
                        if title_elem:
                            print(f"  {j+1}. {title_elem.get_text().strip()}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
        
        except Exception as e:
            print(f"❌ 搜索出错: {e}")
        
        # 避免请求过快
        time.sleep(2)

def test_crawler_with_better_keywords():
    """使用更好的关键词测试爬虫"""
    print("\n🕷️ 使用改进的关键词测试爬虫...")
    
    try:
        from src.crawler_engine import CrawlerEngine
        from src.data_processor import DataProcessor
        
        crawler = CrawlerEngine()
        processor = DataProcessor()
        
        # 配置爬虫参数
        crawler_config = {
            'industry': '人工智能',
            'province': '广东省',
            'city': '深圳市',
            'search_depth': 1,  # 只爬取1页
            'use_baidu': False,  # 百度被反爬，先不用
            'use_bing': True,
            'use_business_dirs': False
        }
        
        print(f"开始爬取: {crawler_config['industry']} - {crawler_config['province']} {crawler_config['city']}")
        
        # 开始爬取
        raw_data = crawler.start_crawling(crawler_config)
        
        if raw_data.empty:
            print("⚠️ 爬取结果为空")
            return False
        
        print(f"✅ 爬取成功，获得 {len(raw_data)} 条原始数据")
        print("\n原始数据样本:")
        print(raw_data.head())
        
        # 数据处理
        processed_data = processor.process_data(raw_data)
        print(f"✅ 数据处理成功，处理后 {len(processed_data)} 条数据")
        
        if not processed_data.empty:
            print("\n处理后数据样本:")
            print("数据列名:", processed_data.columns.tolist())
            # 使用中文列名
            display_columns = []
            for col in ['公司名称', '行业', '官网', '数据源']:
                if col in processed_data.columns:
                    display_columns.append(col)

            if display_columns:
                print(processed_data[display_columns].head())
            else:
                print(processed_data.head())
            return True
        else:
            print("❌ 处理后数据为空")
            return False
            
    except Exception as e:
        print(f"❌ 爬虫测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试真实搜索功能")
    print("=" * 50)
    
    # 测试更好的关键词
    test_better_keywords()
    
    # 测试爬虫
    test_crawler_with_better_keywords()

if __name__ == "__main__":
    main()
