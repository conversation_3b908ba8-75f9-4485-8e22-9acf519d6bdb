#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实招投标数据爬取器
专门获取真实的招投标和企业信息
"""

import requests
import pandas as pd
import time
import random
import re
from urllib.parse import quote, urljoin
from bs4 import BeautifulSoup
import logging
from typing import List, Dict, Optional

class RealBiddingCrawler:
    """真实招投标数据爬取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.logger = logging.getLogger(__name__)
        self._setup_session()
        
        # 真实的招投标网站配置
        self.bidding_sites = {
            'ccgp': {
                'name': '中国政府采购网',
                'base_url': 'http://www.ccgp.gov.cn',
                'search_url': 'http://search.ccgp.gov.cn/bxsearch',
                'enabled': True
            },
            'cebpubservice': {
                'name': '全国公共资源交易平台',
                'base_url': 'http://deal.ggzy.gov.cn',
                'search_url': 'http://deal.ggzy.gov.cn/ds/deal/dealList_find.jsp',
                'enabled': True
            }
        }
        
        # 企业信息网站配置（公开API）
        self.enterprise_sites = {
            'tianyancha_open': {
                'name': '天眼查开放数据',
                'base_url': 'https://open.tianyancha.com',
                'enabled': False  # 需要API密钥
            },
            'qichacha_open': {
                'name': '企查查开放数据', 
                'base_url': 'https://openapi.qcc.com',
                'enabled': False  # 需要API密钥
            }
        }
    
    def _setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session.headers.update(headers)
    
    def search_real_bidding_data(self, industry: str, province: str = None, city: str = None, 
                                max_results: int = 50) -> pd.DataFrame:
        """搜索真实招投标数据"""
        self.logger.info(f"开始搜索真实招投标数据: {industry}")
        
        all_results = []
        
        # 生成搜索关键词
        keywords = self._generate_search_keywords(industry, province, city)
        
        # 搜索中国政府采购网
        ccgp_results = self._search_ccgp(keywords, max_results//2)
        all_results.extend(ccgp_results)
        
        # 搜索公共资源交易平台
        ggzy_results = self._search_ggzy(keywords, max_results//2)
        all_results.extend(ggzy_results)
        
        # 转换为DataFrame
        df = pd.DataFrame(all_results)
        
        if not df.empty:
            # 数据清洗
            df = self._clean_bidding_data(df)
            # 提取企业信息
            df = self._extract_company_info(df, industry, province, city)
        
        self.logger.info(f"真实招投标数据搜索完成，共获取 {len(df)} 条")
        return df
    
    def _generate_search_keywords(self, industry: str, province: str = None, city: str = None) -> List[str]:
        """生成搜索关键词"""
        keywords = []
        
        # 基础行业关键词
        base_keywords = [industry]
        
        # 行业相关词汇
        industry_terms = {
            '酒店建设': ['酒店', '宾馆', '度假村', '民宿', '客房', '餐饮'],
            '房地产开发': ['房地产', '住宅', '商业地产', '写字楼', '商铺'],
            '建筑工程': ['建筑', '施工', '工程', '装修', '装饰'],
            '装修装饰': ['装修', '装饰', '室内设计', '家装', '工装']
        }
        
        if industry in industry_terms:
            base_keywords.extend(industry_terms[industry])
        
        # 添加地区信息
        for keyword in base_keywords:
            if province and city:
                keywords.append(f"{province} {city} {keyword}")
                keywords.append(f"{city} {keyword}")
            elif province:
                keywords.append(f"{province} {keyword}")
            else:
                keywords.append(keyword)
        
        return keywords[:5]  # 限制关键词数量
    
    def _search_ccgp(self, keywords: List[str], max_results: int) -> List[Dict]:
        """搜索中国政府采购网"""
        results = []
        
        for keyword in keywords[:3]:
            try:
                self.logger.info(f"搜索中国政府采购网: {keyword}")
                
                # 构建搜索参数
                search_params = {
                    'searchtype': '1',
                    'page_index': '1',
                    'bidSort': '0',
                    'buyerName': '',
                    'projectId': '',
                    'pinMu': '0',
                    'bidType': '0',
                    'dbselect': 'bidx',
                    'kw': keyword,
                    'start_time': '',
                    'end_time': '',
                    'timeType': '6',
                    'displayZone': '',
                    'zoneId': '',
                    'pppStatus': '0',
                    'agentName': ''
                }
                
                # 发送请求
                response = self.session.get(
                    self.bidding_sites['ccgp']['search_url'],
                    params=search_params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    # 解析结果
                    page_results = self._parse_ccgp_results(response.text, keyword)
                    results.extend(page_results)
                    
                    self.logger.info(f"中国政府采购网 '{keyword}': {len(page_results)} 条结果")
                
                # 随机延时
                time.sleep(random.uniform(2, 4))
                
                if len(results) >= max_results:
                    break
                    
            except Exception as e:
                self.logger.error(f"搜索中国政府采购网出错: {e}")
                continue
        
        return results[:max_results]
    
    def _search_ggzy(self, keywords: List[str], max_results: int) -> List[Dict]:
        """搜索公共资源交易平台"""
        results = []
        
        for keyword in keywords[:3]:
            try:
                self.logger.info(f"搜索公共资源交易平台: {keyword}")
                
                # 构建搜索参数
                search_params = {
                    'TIMEBEGIN_SHOW': '',
                    'TIMEEND_SHOW': '',
                    'TIMEBEGIN': '',
                    'TIMEEND': '',
                    'SOURCE_TYPE': '1',
                    'DEAL_TIME': '01',
                    'DEAL_CLASSIFY': '01',
                    'DEAL_STAGE': '0101',
                    'DEAL_PROVINCE': '0',
                    'DEAL_CITY': '0',
                    'DEAL_PLATFORM': '0',
                    'BID_PLATFORM': '0',
                    'SEARCHKEY': keyword,
                    'PAGENUMBER': '1',
                    'FINDTXT': keyword
                }
                
                # 发送请求
                response = self.session.get(
                    self.bidding_sites['cebpubservice']['search_url'],
                    params=search_params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    # 解析结果
                    page_results = self._parse_ggzy_results(response.text, keyword)
                    results.extend(page_results)
                    
                    self.logger.info(f"公共资源交易平台 '{keyword}': {len(page_results)} 条结果")
                
                # 随机延时
                time.sleep(random.uniform(2, 4))
                
                if len(results) >= max_results:
                    break
                    
            except Exception as e:
                self.logger.error(f"搜索公共资源交易平台出错: {e}")
                continue
        
        return results[:max_results]
    
    def _parse_ccgp_results(self, html_content: str, keyword: str) -> List[Dict]:
        """解析中国政府采购网结果"""
        results = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找结果项
            result_items = soup.find_all(['li', 'div'], class_=re.compile(r'(vT_srch_result_list_bid|result|item)'))
            
            for item in result_items:
                try:
                    # 提取项目信息
                    title_elem = item.find(['a', 'span'], class_=re.compile(r'(vT_srch_result_list_bid_t|title)'))
                    if not title_elem:
                        continue
                    
                    title = title_elem.get_text(strip=True)
                    link = title_elem.get('href', '') if title_elem.name == 'a' else ''
                    
                    # 提取其他信息
                    content_elem = item.find(['div', 'span'], class_=re.compile(r'(content|description)'))
                    content = content_elem.get_text(strip=True) if content_elem else ''
                    
                    # 提取日期
                    date_elem = item.find(['span', 'div'], class_=re.compile(r'(date|time)'))
                    date = date_elem.get_text(strip=True) if date_elem else ''
                    
                    result = {
                        'project_title': title,
                        'project_url': urljoin(self.bidding_sites['ccgp']['base_url'], link) if link else '',
                        'content': content,
                        'publish_date': date,
                        'source': '中国政府采购网',
                        'search_keyword': keyword,
                        'data_type': 'bidding'
                    }
                    
                    results.append(result)
                    
                except Exception as e:
                    self.logger.debug(f"解析中国政府采购网项目出错: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"解析中国政府采购网页面出错: {e}")
        
        return results
    
    def _parse_ggzy_results(self, html_content: str, keyword: str) -> List[Dict]:
        """解析公共资源交易平台结果"""
        results = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找结果项
            result_items = soup.find_all(['tr', 'li', 'div'], class_=re.compile(r'(ewb-list-node|result|item)'))
            
            for item in result_items:
                try:
                    # 提取项目信息
                    title_elem = item.find('a')
                    if not title_elem:
                        continue
                    
                    title = title_elem.get_text(strip=True)
                    link = title_elem.get('href', '')
                    
                    # 提取其他信息
                    cells = item.find_all(['td', 'span', 'div'])
                    
                    region = ''
                    date = ''
                    for cell in cells:
                        cell_text = cell.get_text(strip=True)
                        if re.search(r'\d{4}-\d{2}-\d{2}', cell_text):
                            date = cell_text
                        elif any(province in cell_text for province in ['省', '市', '区', '县']):
                            region = cell_text
                    
                    result = {
                        'project_title': title,
                        'project_url': urljoin(self.bidding_sites['cebpubservice']['base_url'], link) if link else '',
                        'region': region,
                        'publish_date': date,
                        'source': '全国公共资源交易平台',
                        'search_keyword': keyword,
                        'data_type': 'bidding'
                    }
                    
                    results.append(result)
                    
                except Exception as e:
                    self.logger.debug(f"解析公共资源交易平台项目出错: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"解析公共资源交易平台页面出错: {e}")
        
        return results
    
    def _clean_bidding_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗招投标数据"""
        if df.empty:
            return df
        
        # 去重
        df = df.drop_duplicates(subset=['project_title'], keep='first')
        
        # 清理文本
        text_columns = ['project_title', 'content', 'region']
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip()
                df[col] = df[col].str.replace(r'\s+', ' ', regex=True)
        
        return df
    
    def _extract_company_info(self, df: pd.DataFrame, industry: str, province: str = None, city: str = None) -> pd.DataFrame:
        """从招投标信息中提取企业信息"""
        if df.empty:
            return df
        
        company_data = []
        
        for _, row in df.iterrows():
            # 从项目标题和内容中提取公司名称
            text = f"{row.get('project_title', '')} {row.get('content', '')}"
            
            # 使用正则表达式提取公司名称
            company_patterns = [
                r'([^，。；！？\s]{2,20}(?:有限公司|股份有限公司|集团|公司))',
                r'([^，。；！？\s]{2,20}(?:建设|工程|装饰|设计)(?:有限公司|公司))',
                r'中标(?:单位|人|企业)[：:]?\s*([^，。；！？\s]{2,20}(?:有限公司|股份有限公司|集团|公司))',
                r'采购人[：:]?\s*([^，。；！？\s]{2,20}(?:有限公司|股份有限公司|集团|公司))'
            ]
            
            companies = set()
            for pattern in company_patterns:
                matches = re.findall(pattern, text)
                companies.update(matches)
            
            # 为每个提取的公司创建记录
            for company in companies:
                if len(company) >= 4:  # 过滤太短的名称
                    company_record = {
                        'company_name': company,
                        'industry': industry,
                        'source': row.get('source', '招投标信息'),
                        'data_type': 'real_bidding',
                        'project_title': row.get('project_title', ''),
                        'project_url': row.get('project_url', ''),
                        'publish_date': row.get('publish_date', ''),
                        'region': row.get('region', ''),
                        'search_keyword': row.get('search_keyword', ''),
                        'data_quality_score': 0.8  # 真实数据质量分数
                    }
                    
                    # 添加地区信息
                    if province:
                        company_record['province'] = province
                    if city:
                        company_record['city'] = city
                    
                    company_data.append(company_record)
        
        # 转换为DataFrame并去重
        if company_data:
            company_df = pd.DataFrame(company_data)
            company_df = company_df.drop_duplicates(subset=['company_name'], keep='first')
            return company_df
        else:
            return pd.DataFrame()
