#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR智能爬虫系统
通过OCR技术识别页面信息进行数据爬取
"""

import sys
import os
import time
import base64
import io
from pathlib import Path
from typing import Dict, List, Any, Optional
import requests
import json
from PIL import Image, ImageEnhance, ImageFilter
import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class OCRCrawler:
    """OCR智能爬虫"""
    
    def __init__(self):
        """初始化OCR爬虫"""
        self.driver = None
        self.ocr_engines = self._setup_ocr_engines()
        self.setup_browser()
        
    def _setup_ocr_engines(self) -> Dict[str, Any]:
        """设置OCR引擎"""
        engines = {}
        
        # 尝试导入各种OCR库
        try:
            import easyocr
            engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'])
            print("✅ EasyOCR 引擎加载成功")
        except ImportError:
            print("⚠️ EasyOCR 未安装，将使用备用方案")
        
        try:
            import pytesseract
            engines['tesseract'] = pytesseract
            print("✅ Tesseract 引擎加载成功")
        except ImportError:
            print("⚠️ Tesseract 未安装，将使用备用方案")
        
        # 百度OCR API (需要配置)
        engines['baidu'] = {
            'app_id': 'your_app_id',
            'api_key': 'your_api_key', 
            'secret_key': 'your_secret_key',
            'available': False  # 需要配置后启用
        }
        
        return engines
    
    def setup_browser(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ 浏览器初始化成功")
            
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            self.driver = None
    
    def preprocess_image(self, image: Image.Image) -> Image.Image:
        """图像预处理，提高OCR识别率"""
        try:
            # 转换为灰度图
            if image.mode != 'L':
                image = image.convert('L')
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)
            
            # 增强锐度
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(2.0)
            
            # 使用OpenCV进行进一步处理
            img_array = np.array(image)
            
            # 高斯模糊去噪
            img_array = cv2.GaussianBlur(img_array, (1, 1), 0)
            
            # 自适应阈值二值化
            img_array = cv2.adaptiveThreshold(
                img_array, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 形态学操作去除噪点
            kernel = np.ones((1, 1), np.uint8)
            img_array = cv2.morphologyEx(img_array, cv2.MORPH_CLOSE, kernel)
            
            return Image.fromarray(img_array)
            
        except Exception as e:
            print(f"⚠️ 图像预处理失败: {e}")
            return image
    
    def extract_text_easyocr(self, image: Image.Image) -> List[Dict[str, Any]]:
        """使用EasyOCR提取文本"""
        if 'easyocr' not in self.ocr_engines:
            return []
        
        try:
            # 转换图像格式
            img_array = np.array(image)
            
            # 使用EasyOCR识别
            reader = self.ocr_engines['easyocr']
            results = reader.readtext(img_array)
            
            extracted_data = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # 置信度阈值
                    extracted_data.append({
                        'text': text.strip(),
                        'confidence': confidence,
                        'bbox': bbox,
                        'engine': 'easyocr'
                    })
            
            return extracted_data
            
        except Exception as e:
            print(f"❌ EasyOCR识别失败: {e}")
            return []
    
    def extract_text_tesseract(self, image: Image.Image) -> List[Dict[str, Any]]:
        """使用Tesseract提取文本"""
        if 'tesseract' not in self.ocr_engines:
            return []
        
        try:
            import pytesseract
            
            # 配置Tesseract
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz中文'
            
            # 提取文本
            text = pytesseract.image_to_string(image, lang='chi_sim+eng', config=config)
            
            if text.strip():
                return [{
                    'text': text.strip(),
                    'confidence': 0.8,  # Tesseract不直接提供置信度
                    'bbox': None,
                    'engine': 'tesseract'
                }]
            
            return []
            
        except Exception as e:
            print(f"❌ Tesseract识别失败: {e}")
            return []
    
    def extract_text_baidu(self, image: Image.Image) -> List[Dict[str, Any]]:
        """使用百度OCR API提取文本"""
        baidu_config = self.ocr_engines.get('baidu', {})
        if not baidu_config.get('available', False):
            return []
        
        try:
            # 将图像转换为base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # 获取access_token
            token_url = "https://aip.baidubce.com/oauth/2.0/token"
            token_params = {
                'grant_type': 'client_credentials',
                'client_id': baidu_config['api_key'],
                'client_secret': baidu_config['secret_key']
            }
            
            token_response = requests.post(token_url, params=token_params)
            access_token = token_response.json().get('access_token')
            
            if not access_token:
                return []
            
            # 调用OCR API
            ocr_url = f"https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token={access_token}"
            ocr_data = {'image': img_base64}
            
            ocr_response = requests.post(ocr_url, data=ocr_data)
            result = ocr_response.json()
            
            extracted_data = []
            if 'words_result' in result:
                for item in result['words_result']:
                    extracted_data.append({
                        'text': item['words'],
                        'confidence': 0.9,  # 百度OCR通常准确率较高
                        'bbox': None,
                        'engine': 'baidu'
                    })
            
            return extracted_data
            
        except Exception as e:
            print(f"❌ 百度OCR识别失败: {e}")
            return []
    
    def extract_text_from_image(self, image: Image.Image) -> List[Dict[str, Any]]:
        """从图像中提取文本（使用多个OCR引擎）"""
        # 预处理图像
        processed_image = self.preprocess_image(image)
        
        all_results = []
        
        # 尝试所有可用的OCR引擎
        if 'easyocr' in self.ocr_engines:
            easyocr_results = self.extract_text_easyocr(processed_image)
            all_results.extend(easyocr_results)
        
        if 'tesseract' in self.ocr_engines:
            tesseract_results = self.extract_text_tesseract(processed_image)
            all_results.extend(tesseract_results)
        
        if self.ocr_engines.get('baidu', {}).get('available', False):
            baidu_results = self.extract_text_baidu(processed_image)
            all_results.extend(baidu_results)
        
        # 去重和合并结果
        unique_texts = set()
        final_results = []
        
        for result in all_results:
            text = result['text']
            if text and text not in unique_texts and len(text.strip()) > 1:
                unique_texts.add(text)
                final_results.append(result)
        
        return final_results
    
    def capture_page_screenshot(self, url: str) -> Optional[Image.Image]:
        """截取页面截图"""
        if not self.driver:
            print("❌ 浏览器未初始化")
            return None
        
        try:
            print(f"📸 正在截取页面: {url}")
            
            # 访问页面
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 截取整个页面
            screenshot = self.driver.get_screenshot_as_png()
            
            # 转换为PIL图像
            image = Image.open(io.BytesIO(screenshot))
            
            return image
            
        except Exception as e:
            print(f"❌ 页面截图失败: {e}")
            return None
    
    def crawl_page_with_ocr(self, url: str, target_keywords: List[str] = None) -> Dict[str, Any]:
        """使用OCR爬取页面数据"""
        print(f"🔍 开始OCR爬取: {url}")
        
        # 截取页面截图
        screenshot = self.capture_page_screenshot(url)
        if not screenshot:
            return {
                'success': False,
                'message': '页面截图失败',
                'data': []
            }
        
        # 提取文本
        extracted_texts = self.extract_text_from_image(screenshot)
        
        if not extracted_texts:
            return {
                'success': False,
                'message': 'OCR文本提取失败',
                'data': []
            }
        
        # 过滤和处理文本
        processed_data = self._process_extracted_texts(extracted_texts, target_keywords)
        
        return {
            'success': True,
            'message': f'OCR爬取成功，提取到 {len(processed_data)} 条有效信息',
            'data': processed_data,
            'raw_texts': [item['text'] for item in extracted_texts]
        }
    
    def _process_extracted_texts(self, texts: List[Dict[str, Any]], 
                                target_keywords: List[str] = None) -> List[Dict[str, Any]]:
        """处理提取的文本"""
        processed_data = []
        
        for text_item in texts:
            text = text_item['text']
            
            # 基本清理
            text = text.strip()
            if len(text) < 2:
                continue
            
            # 关键词过滤
            if target_keywords:
                if not any(keyword in text for keyword in target_keywords):
                    continue
            
            # 尝试提取结构化信息
            structured_info = self._extract_structured_info(text)
            
            processed_item = {
                'original_text': text,
                'confidence': text_item['confidence'],
                'engine': text_item['engine'],
                'structured_info': structured_info
            }
            
            processed_data.append(processed_item)
        
        return processed_data
    
    def _extract_structured_info(self, text: str) -> Dict[str, Any]:
        """从文本中提取结构化信息"""
        info = {}
        
        # 提取电话号码
        phone_pattern = r'1[3-9]\d{9}|0\d{2,3}-?\d{7,8}'
        phones = re.findall(phone_pattern, text)
        if phones:
            info['phones'] = phones
        
        # 提取邮箱
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        if emails:
            info['emails'] = emails
        
        # 提取公司名称（包含"公司"、"集团"等）
        company_pattern = r'[\u4e00-\u9fa5]+(?:公司|集团|企业|有限|股份|科技|建设|工程|发展)'
        companies = re.findall(company_pattern, text)
        if companies:
            info['companies'] = companies
        
        # 提取地址信息
        address_pattern = r'[\u4e00-\u9fa5]+(?:省|市|区|县|街道|路|号|大厦|广场|中心)'
        addresses = re.findall(address_pattern, text)
        if addresses:
            info['addresses'] = addresses
        
        return info
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("✅ 浏览器已关闭")

# 测试函数
def test_ocr_crawler():
    """测试OCR爬虫"""
    crawler = OCRCrawler()
    
    try:
        # 测试网站
        test_urls = [
            "https://www.baidu.com",
            "https://www.bing.com"
        ]
        
        for url in test_urls:
            print(f"\n🧪 测试OCR爬取: {url}")
            result = crawler.crawl_page_with_ocr(url, target_keywords=['搜索', '百度', '必应'])
            
            if result['success']:
                print(f"✅ 爬取成功: {result['message']}")
                print(f"   提取文本数量: {len(result.get('raw_texts', []))}")
                print(f"   处理数据数量: {len(result['data'])}")
                
                # 显示部分结果
                for i, item in enumerate(result['data'][:3]):
                    print(f"   数据{i+1}: {item['original_text'][:50]}...")
            else:
                print(f"❌ 爬取失败: {result['message']}")
    
    finally:
        crawler.close()

if __name__ == "__main__":
    test_ocr_crawler()
