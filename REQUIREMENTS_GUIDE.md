# 📦 依赖包安装指南

## 🎯 不同安装方式

### 1. 🚀 快速启动（推荐新用户）
```bash
# 安装最小依赖包，快速体验系统
pip install -r requirements_minimal.txt
```

### 2. 🔧 标准安装（推荐大多数用户）
```bash
# 安装标准功能依赖包
pip install -r requirements.txt
```

### 3. 💪 完整功能安装（高级用户）
```bash
# 安装所有功能依赖包，包括OCR、AI等高级功能
pip install -r requirements_complete.txt
```

### 4. 🎯 真实数据爬取专用
```bash
# 专门用于真实数据爬取的依赖包
pip install -r real_crawler_requirements.txt
```

## 📋 功能对比

| 功能模块 | 最小安装 | 标准安装 | 完整安装 | 真实爬取 |
|---------|---------|---------|---------|---------|
| 基础数据收集 | ✅ | ✅ | ✅ | ✅ |
| Web界面 | ✅ | ✅ | ✅ | ✅ |
| 数据可视化 | ✅ | ✅ | ✅ | ✅ |
| 浏览器自动化 | ❌ | ✅ | ✅ | ✅ |
| OCR文字识别 | ❌ | ✅ | ✅ | ✅ |
| 验证码识别 | ❌ | ❌ | ✅ | ✅ |
| 高级数据库 | ❌ | ❌ | ✅ | ✅ |
| AI智能分析 | ❌ | ❌ | ✅ | ❌ |
| 社交媒体爬取 | ❌ | ❌ | ❌ | ✅ |

## 🔧 特殊依赖安装

### OCR功能依赖
```bash
# Windows用户需要额外安装Tesseract
# 1. 下载安装 https://github.com/UB-Mannheim/tesseract/wiki
# 2. 添加到系统PATH

# Linux用户
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim

# macOS用户
brew install tesseract tesseract-lang
```

### 浏览器驱动安装
```bash
# Playwright浏览器安装
playwright install

# Chrome驱动（自动安装）
python -c "import undetected_chromedriver; undetected_chromedriver.Chrome()"
```

### 可选AI功能
```bash
# 如果需要AI智能分析功能
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install transformers scikit-learn
```

## 🚨 常见问题解决

### 1. 安装失败
```bash
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者使用阿里云镜像
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
```

### 2. 版本冲突
```bash
# 创建新的虚拟环境
python -m venv fresh_env
# Windows
fresh_env\Scripts\activate
# Linux/macOS
source fresh_env/bin/activate

# 重新安装
pip install -r requirements.txt
```

### 3. 内存不足
```bash
# 分批安装大型包
pip install torch --no-cache-dir
pip install transformers --no-cache-dir
pip install -r requirements.txt --no-cache-dir
```

## 📊 安装验证

### 运行检查脚本
```bash
# 检查基础依赖
python check_installation.py

# 检查系统状态
python check_status.py

# 快速测试
python quick_test.py
```

### 手动验证
```python
# 测试核心模块
import streamlit
import pandas
import requests
import plotly

# 测试OCR模块（如果安装了完整版）
try:
    import pytesseract
    import cv2
    import easyocr
    print("✅ OCR模块可用")
except ImportError:
    print("⚠️ OCR模块未安装")

# 测试浏览器自动化
try:
    from selenium import webdriver
    from playwright.sync_api import sync_playwright
    print("✅ 浏览器自动化可用")
except ImportError:
    print("⚠️ 浏览器自动化模块未安装")
```

## 💡 推荐安装流程

1. **首次使用**：先安装 `requirements_minimal.txt`
2. **功能测试**：运行 `python quick_test.py` 验证
3. **需要更多功能**：升级到 `requirements.txt`
4. **专业用户**：安装 `requirements_complete.txt`
5. **真实数据需求**：额外安装 `real_crawler_requirements.txt`

---

💡 **提示**：建议在虚拟环境中安装，避免与系统Python包冲突。
