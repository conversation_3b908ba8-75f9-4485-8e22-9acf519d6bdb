#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目状态检查脚本 - 诊断和健康检查
检查环境配置、依赖安装、文件完整性等
"""

import sys
import os
import subprocess
from pathlib import Path
import platform
import sqlite3

def print_banner():
    """打印横幅"""
    banner = """
    🔍 项目状态检查工具
    ================================
    环境诊断 + 健康检查 + 问题排查
    ================================
    """
    print(banner)

def check_python_environment():
    """检查Python环境"""
    print("🐍 Python环境检查")
    print("-" * 40)
    
    # Python版本
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
    
    # Python路径
    print(f"Python路径: {sys.executable}")
    
    # 虚拟环境检查
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 运行在虚拟环境中")
        print(f"虚拟环境路径: {sys.prefix}")
    else:
        print("⚠️  运行在全局Python环境中")
        print("💡 建议使用虚拟环境: python setup_env.py")
    
    return True

def check_required_files():
    """检查必需文件"""
    print("\n📁 文件完整性检查")
    print("-" * 40)
    
    required_files = [
        "main.py",
        "run.py", 
        "config.yaml",
        "requirements.txt",
        "src/crawler_engine.py",
        "src/data_processor.py",
        "src/visualizer.py",
        "src/database_manager.py",
        "src/utils/logger.py",
        "src/utils/anti_detection.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  缺少 {len(missing_files)} 个必需文件")
        return False
    else:
        print("\n✅ 所有必需文件完整")
        return True

def check_directories():
    """检查目录结构"""
    print("\n📂 目录结构检查")
    print("-" * 40)
    
    required_dirs = ["src", "src/utils", "data", "logs"]
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/")
            try:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                print(f"   🔧 已自动创建")
            except Exception as e:
                print(f"   ❌ 创建失败: {e}")

def check_dependencies():
    """检查依赖包"""
    print("\n📦 依赖包检查")
    print("-" * 40)
    
    required_packages = [
        ("streamlit", "Web框架"),
        ("plotly", "可视化库"),
        ("pandas", "数据处理"),
        ("requests", "HTTP库"),
        ("beautifulsoup4", "HTML解析"),
        ("selenium", "浏览器自动化"),
        ("fake-useragent", "用户代理"),
        ("fuzzywuzzy", "模糊匹配"),
        ("pyyaml", "YAML解析"),
        ("loguru", "日志库")
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} ({description})")
        except ImportError:
            print(f"❌ {package} ({description})")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少 {len(missing_packages)} 个依赖包")
        print("💡 运行安装命令:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    else:
        print("\n✅ 所有依赖包已安装")
        return True

def check_database():
    """检查数据库"""
    print("\n💾 数据库检查")
    print("-" * 40)
    
    db_path = Path("data/customer_data.db")
    
    if not db_path.exists():
        print("ℹ️  数据库文件不存在（首次运行正常）")
        return True
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        expected_tables = ['customer_info', 'crawl_history', 'source_stats']
        existing_tables = [table[0] for table in tables]
        
        for table in expected_tables:
            if table in existing_tables:
                print(f"✅ 表 {table}")
            else:
                print(f"❌ 表 {table}")
        
        # 检查数据量
        cursor.execute("SELECT COUNT(*) FROM customer_info")
        count = cursor.fetchone()[0]
        print(f"📊 客户信息记录数: {count}")
        
        conn.close()
        print("✅ 数据库连接正常")
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_configuration():
    """检查配置文件"""
    print("\n⚙️ 配置文件检查")
    print("-" * 40)
    
    config_file = Path("config.yaml")
    
    if not config_file.exists():
        print("❌ config.yaml 不存在")
        return False
    
    try:
        import yaml
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查关键配置项
        required_sections = ['database', 'crawler', 'search_engines']
        
        for section in required_sections:
            if section in config:
                print(f"✅ 配置节 {section}")
            else:
                print(f"❌ 配置节 {section}")
        
        print("✅ 配置文件格式正确")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def check_network():
    """检查网络连接"""
    print("\n🌐 网络连接检查")
    print("-" * 40)
    
    test_urls = [
        ("百度", "https://www.baidu.com"),
        ("必应", "https://www.bing.com")
    ]
    
    try:
        import requests
        
        for name, url in test_urls:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {name} 连接正常")
                else:
                    print(f"⚠️  {name} 返回状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ {name} 连接失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ requests库未安装，无法测试网络")
        return False

def check_system_resources():
    """检查系统资源"""
    print("\n💻 系统资源检查")
    print("-" * 40)
    
    # 系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"处理器架构: {platform.machine()}")
    print(f"Python架构: {platform.architecture()[0]}")
    
    # 磁盘空间
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        print(f"磁盘空间: {free // (1024**3)}GB 可用 / {total // (1024**3)}GB 总计")
        
        if free < 1024**3:  # 小于1GB
            print("⚠️  磁盘空间不足")
        else:
            print("✅ 磁盘空间充足")
            
    except Exception as e:
        print(f"⚠️  无法检查磁盘空间: {e}")

def run_quick_test():
    """运行快速功能测试"""
    print("\n🧪 快速功能测试")
    print("-" * 40)
    
    try:
        # 测试导入核心模块
        sys.path.insert(0, str(Path(__file__).parent))
        
        from src.utils.logger import setup_logger
        print("✅ 日志模块导入成功")
        
        from src.data_processor import DataProcessor
        print("✅ 数据处理模块导入成功")
        
        from src.database_manager import DatabaseManager
        print("✅ 数据库管理模块导入成功")
        
        # 测试数据库连接
        db_manager = DatabaseManager()
        total_records = db_manager.get_total_records()
        print(f"✅ 数据库连接测试成功 (记录数: {total_records})")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def generate_report(results):
    """生成检查报告"""
    print("\n📋 检查报告")
    print("=" * 50)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"通过率: {passed_checks/total_checks*100:.1f}%")
    
    if passed_checks == total_checks:
        print("\n🎉 所有检查通过！系统状态良好")
        print("💡 可以运行: python run.py")
    else:
        print("\n⚠️  存在问题需要解决")
        print("💡 建议:")
        
        if not results.get("dependencies", True):
            print("   1. 安装缺失的依赖包: pip install -r requirements.txt")
        
        if not results.get("files", True):
            print("   2. 检查项目文件完整性，重新下载项目")
        
        if not results.get("python", True):
            print("   3. 升级Python版本到3.8+")
        
        print("   4. 查看详细错误信息并逐项解决")

def main():
    """主函数"""
    print_banner()
    
    print(f"检查时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"工作目录: {os.getcwd()}")
    
    # 执行各项检查
    results = {}
    
    results["python"] = check_python_environment()
    results["files"] = check_required_files()
    check_directories()  # 自动修复，不计入结果
    results["dependencies"] = check_dependencies()
    results["database"] = check_database()
    results["configuration"] = check_configuration()
    results["network"] = check_network()
    check_system_resources()  # 信息性检查
    results["functionality"] = run_quick_test()
    
    # 生成报告
    generate_report(results)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 检查被用户中断")
    except Exception as e:
        print(f"\n💥 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
