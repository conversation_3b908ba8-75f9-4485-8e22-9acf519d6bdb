#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单爬虫测试脚本
验证基础浏览器自动化框架是否正常工作
"""

import asyncio
import sys
import logging
from src.browser_manager import get_browser_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_basic_navigation():
    """测试基础页面导航功能"""
    print("🔍 测试基础页面导航功能...")
    
    manager = get_browser_manager(headless=True)
    
    try:
        # 启动浏览器管理器
        await manager.start()
        
        # 创建浏览器和页面
        browser = await manager.get_or_create_browser()
        context = await manager.create_context(browser)
        page = await manager.create_page(context)
        
        # 测试访问百度
        print("📍 访问百度首页...")
        await page.goto("https://www.baidu.com", timeout=15000)
        title = await page.title()
        print(f"✅ 页面标题: {title}")
        
        # 测试搜索功能
        print("🔍 测试搜索功能...")
        await page.fill('input[name="wd"]', '酒店建设')
        await page.click('input[type="submit"]')
        await page.wait_for_timeout(3000)
        
        # 获取搜索结果
        results = await page.query_selector_all('.result')
        print(f"✅ 搜索结果数量: {len(results)}")
        
        # 提取前几个结果的标题
        for i, result in enumerate(results[:3]):
            try:
                title_element = await result.query_selector('h3 a')
                if title_element:
                    title_text = await title_element.text_content()
                    print(f"  {i+1}. {title_text[:50]}...")
            except:
                pass
        
        await page.close()
        await manager.close_context(context)
        
        print("✅ 基础导航测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 基础导航测试失败: {e}")
        return False
        
    finally:
        from src.browser_manager import cleanup_browser_manager
        await cleanup_browser_manager()

async def test_page_analysis():
    """测试页面分析功能"""
    print("\n🔍 测试页面分析功能...")
    
    manager = get_browser_manager(headless=True)
    
    try:
        await manager.start()
        browser = await manager.get_or_create_browser()
        context = await manager.create_context(browser)
        page = await manager.create_page(context)
        
        # 访问一个简单的测试页面
        print("📍 访问测试页面...")
        await page.goto("https://httpbin.org/html", timeout=15000)
        
        # 分析页面结构
        print("🔍 分析页面结构...")
        
        # 获取所有链接
        links = await page.evaluate('''
            () => {
                const links = document.querySelectorAll('a');
                return Array.from(links).map(link => ({
                    text: link.textContent.trim(),
                    href: link.href
                }));
            }
        ''')
        print(f"✅ 找到 {len(links)} 个链接")
        
        # 获取所有标题
        headings = await page.evaluate('''
            () => {
                const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
                return Array.from(headings).map(h => ({
                    tag: h.tagName,
                    text: h.textContent.trim()
                }));
            }
        ''')
        print(f"✅ 找到 {len(headings)} 个标题")
        
        # 测试反检测功能
        webdriver_check = await page.evaluate('navigator.webdriver')
        chrome_check = await page.evaluate('typeof window.chrome')
        print(f"✅ webdriver检测: {webdriver_check} (应该是None)")
        print(f"✅ chrome对象: {chrome_check} (应该是object)")
        
        await page.close()
        await manager.close_context(context)
        
        print("✅ 页面分析测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 页面分析测试失败: {e}")
        return False
        
    finally:
        from src.browser_manager import cleanup_browser_manager
        await cleanup_browser_manager()

async def test_data_extraction():
    """测试数据提取功能"""
    print("\n🔍 测试数据提取功能...")
    
    manager = get_browser_manager(headless=True)
    
    try:
        await manager.start()
        browser = await manager.get_or_create_browser()
        context = await manager.create_context(browser)
        page = await manager.create_page(context)
        
        # 访问一个有结构化数据的页面
        print("📍 访问GitHub首页...")
        await page.goto("https://github.com", timeout=15000)
        
        # 提取页面信息
        page_info = await page.evaluate('''
            () => {
                return {
                    title: document.title,
                    url: window.location.href,
                    links_count: document.querySelectorAll('a').length,
                    buttons_count: document.querySelectorAll('button').length,
                    forms_count: document.querySelectorAll('form').length
                };
            }
        ''')
        
        print(f"✅ 页面信息:")
        print(f"   标题: {page_info['title']}")
        print(f"   链接数: {page_info['links_count']}")
        print(f"   按钮数: {page_info['buttons_count']}")
        print(f"   表单数: {page_info['forms_count']}")
        
        await page.close()
        await manager.close_context(context)
        
        print("✅ 数据提取测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据提取测试失败: {e}")
        return False
        
    finally:
        from src.browser_manager import cleanup_browser_manager
        await cleanup_browser_manager()

def main():
    """主函数"""
    print("=" * 60)
    print("基础浏览器自动化框架测试")
    print("=" * 60)
    
    async def run_all_tests():
        # 测试基础导航
        test1 = await test_basic_navigation()
        
        # 测试页面分析
        test2 = await test_page_analysis()
        
        # 测试数据提取
        test3 = await test_data_extraction()
        
        return test1, test2, test3
    
    # 运行所有测试
    results = asyncio.run(run_all_tests())
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 2:  # 至少2个测试通过
        print("✅ 基础浏览器自动化框架工作正常")
        print("💡 建议：快手平台可能需要更复杂的反爬虫策略")
        print("📋 下一步：可以尝试其他平台或优化反爬虫机制")
        sys.exit(0)
    else:
        print("❌ 基础框架存在问题，需要修复")
        sys.exit(1)

if __name__ == "__main__":
    main()
