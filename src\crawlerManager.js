const DouyinCrawler = require('./platforms/douyin/douyinCrawler');
const KuaishouCrawler = require('./platforms/kuaishou/kuaishouCrawler');
const QichachaCrawler = require('./platforms/qichacha/qichachaCrawler');
const fs = require('fs').promises;
const path = require('path');

class CrawlerManager {
    constructor() {
        this.douyinCrawler = new DouyinCrawler();
        this.kuaishouCrawler = new KuaishouCrawler();
        this.qichachaCrawler = new QichachaCrawler();
        this.results = [];
    }

    async crawlDouyin(userUrl) {
        console.log('\n🎵 开始抖音数据爬取...');
        const result = await this.douyinCrawler.crawlUser(userUrl);
        this.results.push({ platform: 'douyin', ...result });
        return result;
    }

    async crawlKuaishou(userUrl) {
        console.log('\n⚡ 开始快手数据爬取...');
        const result = await this.kuaishouCrawler.crawlUser(userUrl);
        this.results.push({ platform: 'kuaishou', ...result });
        return result;
    }

    async crawlQichacha(companyName) {
        console.log('\n🏢 开始企查查数据爬取...');
        const result = await this.qichachaCrawler.crawlCompany(companyName);
        this.results.push({ platform: 'qichacha', ...result });
        return result;
    }

    async crawlByIndustry(industry, region = '') {
        console.log(`\n🎯 开始行业数据爬取: ${industry} ${region}`);
        
        const results = {
            industry,
            region,
            crawlTime: new Date().toISOString(),
            data: {
                companies: [],
                socialMedia: [],
                videos: []
            }
        };

        try {
            // 1. 搜索相关企业 (企查查)
            const companyKeywords = this.generateCompanyKeywords(industry);
            for (const keyword of companyKeywords.slice(0, 3)) {
                try {
                    const companyResult = await this.qichachaCrawler.crawlCompany(keyword);
                    if (companyResult.success) {
                        results.data.companies.push(companyResult.data);
                    }
                } catch (error) {
                    console.warn(`企业搜索失败: ${keyword}`, error.message);
                }
            }

            // 2. 搜索相关视频内容 (抖音/快手)
            const videoKeywords = this.generateVideoKeywords(industry);
            
            // 这里可以扩展为搜索功能，目前先记录关键词
            results.searchKeywords = {
                companies: companyKeywords,
                videos: videoKeywords
            };

            // 保存综合结果
            await this.saveIndustryData(results, industry);

            return {
                success: true,
                data: results,
                message: `行业数据爬取完成: ${industry}`
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: `行业数据爬取失败: ${industry}`
            };
        }
    }

    generateCompanyKeywords(industry) {
        const keywordMap = {
            '酒店建设': ['酒店建设', '酒店工程', '酒店装修', '酒店设计'],
            '房地产开发': ['房地产开发', '地产公司', '房地产投资', '建筑开发'],
            '建筑施工': ['建筑施工', '工程建设', '建筑公司', '施工企业'],
            '装修装饰': ['装修公司', '装饰工程', '室内设计', '装修设计']
        };
        
        return keywordMap[industry] || [industry];
    }

    generateVideoKeywords(industry) {
        const keywordMap = {
            '酒店建设': ['酒店建设', '酒店装修', '酒店设计', '工程施工'],
            '房地产开发': ['房地产', '楼盘开发', '地产项目', '建筑工程'],
            '建筑施工': ['建筑施工', '工程建设', '施工现场', '建筑工艺'],
            '装修装饰': ['装修', '室内设计', '装饰', '家装']
        };
        
        return keywordMap[industry] || [industry];
    }

    async saveIndustryData(data, industry) {
        const dataDir = path.join(__dirname, '../data/industry');
        await fs.mkdir(dataDir, { recursive: true });
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${industry}_${timestamp}.json`;
        const filepath = path.join(dataDir, filename);
        
        await fs.writeFile(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 行业数据已保存到: ${filepath}`);
        
        return filepath;
    }

    async getResults() {
        return this.results;
    }

    async generateReport() {
        const report = {
            crawlTime: new Date().toISOString(),
            totalTasks: this.results.length,
            successfulTasks: this.results.filter(r => r.success).length,
            failedTasks: this.results.filter(r => !r.success).length,
            platforms: {},
            details: this.results
        };

        // 按平台统计
        this.results.forEach(result => {
            if (!report.platforms[result.platform]) {
                report.platforms[result.platform] = {
                    total: 0,
                    successful: 0,
                    failed: 0
                };
            }
            
            report.platforms[result.platform].total++;
            if (result.success) {
                report.platforms[result.platform].successful++;
            } else {
                report.platforms[result.platform].failed++;
            }
        });

        // 保存报告
        const reportDir = path.join(__dirname, '../data/reports');
        await fs.mkdir(reportDir, { recursive: true });
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const reportPath = path.join(reportDir, `crawler_report_${timestamp}.json`);
        
        await fs.writeFile(reportPath, JSON.stringify(report, null, 2), 'utf8');
        console.log(`📊 爬取报告已保存到: ${reportPath}`);
        
        return report;
    }

    async testAllPlatforms() {
        console.log('\n🧪 开始测试所有爬虫平台...');
        
        const testResults = [];

        // 测试企查查 (最容易验证)
        try {
            const qichachaResult = await this.crawlQichacha('腾讯科技');
            testResults.push({
                platform: 'qichacha',
                success: qichachaResult.success,
                message: qichachaResult.message
            });
        } catch (error) {
            testResults.push({
                platform: 'qichacha',
                success: false,
                message: error.message
            });
        }

        // 生成测试报告
        const testReport = {
            testTime: new Date().toISOString(),
            results: testResults,
            summary: {
                total: testResults.length,
                passed: testResults.filter(r => r.success).length,
                failed: testResults.filter(r => !r.success).length
            }
        };

        console.log('\n📋 测试结果:');
        testResults.forEach(result => {
            const status = result.success ? '✅' : '❌';
            console.log(`${status} ${result.platform}: ${result.message}`);
        });

        return testReport;
    }
}

module.exports = CrawlerManager;
