# 企业信息平台配置文件
# 支持企查查、天眼查、钉钉企典等企业信息平台的数据爬取

platforms:
  qichacha:
    enabled: true
    name: "企查查"
    search_url: "https://www.qcc.com/web/search"
    api_url: "https://www.qcc.com/api/search/company"
    detail_url: "https://www.qcc.com/firm/{company_id}"
    headers:
      User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      Referer: "https://www.qcc.com/"
      Accept: "application/json, text/plain, */*"
      Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
      Accept-Encoding: "gzip, deflate, br"
      Connection: "keep-alive"
    rate_limit:
      requests_per_minute: 20
      delay_between_requests: 3
    
  tianyancha:
    enabled: true
    name: "天眼查"
    search_url: "https://www.tianyancha.com/search"
    api_url: "https://www.tianyancha.com/services/v3/t/search/sug"
    detail_url: "https://www.tianyancha.com/company/{company_id}"
    headers:
      User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      Referer: "https://www.tianyancha.com/"
      Accept: "application/json"
      Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
      Accept-Encoding: "gzip, deflate, br"
      Connection: "keep-alive"
    rate_limit:
      requests_per_minute: 15
      delay_between_requests: 4
    
  dingding:
    enabled: true
    name: "钉钉企典"
    search_url: "https://aiqicha.baidu.com/s"
    api_url: "https://aiqicha.baidu.com/ajax/search"
    detail_url: "https://aiqicha.baidu.com/company/detail/{company_id}"
    headers:
      User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      Referer: "https://aiqicha.baidu.com/"
      Accept: "application/json"
      Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
      Accept-Encoding: "gzip, deflate, br"
      Connection: "keep-alive"
    rate_limit:
      requests_per_minute: 25
      delay_between_requests: 2

search_parameters:
  company_types:
    - "有限责任公司"
    - "股份有限公司"
    - "个人独资企业"
    - "合伙企业"
    - "外商投资企业"
    - "国有企业"
    - "集体企业"
  
  status_filter:
    - "存续"
    - "在业"
    - "迁入"
    - "迁出"
  
  industry_keywords:
    - "{industry}"
    - "{industry}相关"
    - "{industry}服务"
    - "{industry}技术"
    - "{industry}设备"
    - "{industry}工程"
    - "{industry}建设"
    - "{industry}开发"
    - "{industry}管理"
    - "{industry}咨询"
  
  location_keywords:
    - "{region} {industry}"
    - "{province} {industry}"
    - "{city} {industry}"
    - "{region} {industry} 公司"
    - "{province} {industry} 企业"
    - "{city} {industry} 有限公司"

data_fields:
  basic_info:
    - "company_name"      # 公司名称
    - "legal_person"      # 法定代表人
    - "registered_capital" # 注册资本
    - "establishment_date" # 成立日期
    - "business_status"   # 经营状态
    - "company_type"      # 公司类型
    - "registration_number" # 注册号
    - "tax_number"        # 纳税人识别号
    - "organization_code" # 组织机构代码
    - "unified_code"      # 统一社会信用代码
  
  contact_info:
    - "phone"            # 电话
    - "email"            # 邮箱
    - "website"          # 网站
    - "address"          # 地址
    - "postal_code"      # 邮政编码
    - "fax"              # 传真
  
  business_info:
    - "business_scope"   # 经营范围
    - "industry"         # 行业
    - "main_business"    # 主营业务
    - "employee_count"   # 员工人数
    - "annual_revenue"   # 年营业额
    - "registered_address" # 注册地址
    - "business_term"    # 营业期限

filtering_rules:
  min_registered_capital: 0  # 最小注册资本（万元）
  max_results_per_keyword: 30  # 每个关键词最大结果数
  
  required_fields:
    - "company_name"
    - "business_status"
  
  valid_status:
    - "存续"
    - "在业"
    - "开业"
    - "正常"
  
  exclude_keywords:
    - "注销"
    - "吊销"
    - "清算"
    - "破产"
    - "停业"
    - "个体工商户"
    - "农民专业合作社"
  
  company_name_patterns:
    - ".*(?:公司|企业|集团|有限|股份|科技|实业|工厂|厂).*"
  
  industry_relevance:
    min_score: 0.3  # 行业相关性最低分数

data_extraction:
  field_patterns:
    legal_person:
      - "法定代表人[:：]\\s*([^\\s，。；\\n]+)"
      - "法人[:：]\\s*([^\\s，。；\\n]+)"
    
    registered_capital:
      - "注册资本[:：]\\s*([^\\s，。；\\n]+)"
      - "注册资金[:：]\\s*([^\\s，。；\\n]+)"
    
    establishment_date:
      - "成立日期[:：]\\s*(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2})"
      - "成立时间[:：]\\s*(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2})"
    
    business_status:
      - "经营状态[:：]\\s*([^\\s，。；\\n]+)"
      - "企业状态[:：]\\s*([^\\s，。；\\n]+)"
    
    phone:
      - "电话[:：]\\s*([0-9\\-\\s\\(\\)]{7,20})"
      - "联系电话[:：]\\s*([0-9\\-\\s\\(\\)]{7,20})"
      - "(\\d{3,4}[-\\s]?\\d{7,8})"
      - "(1[3-9]\\d{9})"
    
    email:
      - "邮箱[:：]\\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})"
      - "([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})"
    
    website:
      - "网站[:：]\\s*(https?://[^\\s<>\"]+)"
      - "官网[:：]\\s*(https?://[^\\s<>\"]+)"
      - "(https?://[^\\s<>\"]+)"
    
    address:
      - "地址[:：]\\s*([^\\n]{10,100})"
      - "注册地址[:：]\\s*([^\\n]{10,100})"
      - "([^\\n]*(?:省|市|区|县|街道|路|号)[^\\n]{5,50})"

anti_detection:
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
  
  random_delays:
    min_delay: 2
    max_delay: 6
  
  retry_settings:
    max_retries: 3
    retry_delay: 10
    backoff_factor: 2
  
  session_management:
    cookies_enabled: true
    session_timeout: 300  # 5分钟

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/enterprise_info_crawler.log"

# 演示数据配置
demo_data:
  enabled: true
  companies_per_platform: 2
  
  capital_ranges:
    min: 100
    max: 50000
  
  legal_person_names:
    male: ["张伟", "李强", "王建", "刘勇", "陈刚", "杨磊", "赵涛", "孙斌", "周鹏", "吴超"]
    female: ["李娜", "王静", "张敏", "刘丽", "陈红", "杨雪", "赵梅", "孙琳", "周慧", "吴颖"]
  
  company_suffixes:
    - "科技有限公司"
    - "实业股份有限公司"
    - "集团有限公司"
    - "发展有限公司"
    - "创新科技有限公司"
    - "商贸有限公司"
    - "工程有限公司"
    - "建设有限公司"
  
  business_descriptions:
    - "专业从事{industry}相关业务的科技公司"
    - "{region}知名{industry}企业，业务覆盖全国"
    - "大型{industry}集团企业，行业领军企业"
    - "专注{industry}领域发展的创新型企业"
    - "新兴{industry}科技企业，专注技术创新"
    - "{industry}产品贸易公司，服务全国客户"
