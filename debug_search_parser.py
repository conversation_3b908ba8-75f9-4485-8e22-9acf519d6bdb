#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试搜索解析器
分析搜索引擎返回的HTML结构
"""

import requests
import time
import random
from bs4 import BeautifulSoup
import re

def debug_360_search():
    """调试360搜索"""
    print("🔍 调试360搜索")
    print("=" * 50)
    
    session = requests.Session()
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
    }
    
    session.headers.update(headers)
    
    try:
        url = 'https://www.so.com/s'
        params = {'q': '北京酒店管理公司'}
        
        print(f"请求URL: {url}")
        print(f"参数: {params}")
        
        response = session.get(url, params=params, timeout=15)
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        
        # 保存完整响应
        with open('debug_360_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("完整响应已保存到: debug_360_response.html")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找可能的结果容器
        print("\n🔍 查找结果容器:")
        
        # 尝试不同的选择器
        selectors = [
            'div[class*="result"]',
            'li[class*="result"]', 
            'div[class*="item"]',
            'li[class*="item"]',
            'div[data-click]',
            '.result',
            '.res-list',
            '.results'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                print(f"  ✅ {selector}: 找到 {len(elements)} 个元素")
                
                # 显示前3个元素的结构
                for i, elem in enumerate(elements[:3], 1):
                    print(f"    元素 {i}:")
                    print(f"      类名: {elem.get('class', [])}")
                    print(f"      文本预览: {elem.get_text()[:100]}...")
                    
                    # 查找标题链接
                    title_links = elem.find_all('a')
                    if title_links:
                        print(f"      包含 {len(title_links)} 个链接")
                        for j, link in enumerate(title_links[:2], 1):
                            print(f"        链接 {j}: {link.get_text()[:50]}...")
            else:
                print(f"  ❌ {selector}: 未找到元素")
        
        # 查找所有链接
        all_links = soup.find_all('a', href=True)
        print(f"\n📎 页面总链接数: {len(all_links)}")
        
        # 过滤可能的结果链接
        result_links = []
        for link in all_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)
            
            # 过滤条件
            if (text and len(text) > 5 and 
                '公司' in text and 
                not any(skip in href for skip in ['so.com', 'javascript:', '#'])):
                result_links.append({
                    'text': text,
                    'href': href
                })
        
        print(f"🎯 可能的结果链接: {len(result_links)}")
        for i, link in enumerate(result_links[:5], 1):
            print(f"  {i}. {link['text'][:50]}...")
            print(f"     URL: {link['href'][:80]}...")
        
    except Exception as e:
        print(f"❌ 360搜索调试失败: {e}")

def debug_bing_search():
    """调试必应搜索"""
    print("\n🔍 调试必应搜索")
    print("=" * 50)
    
    session = requests.Session()
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
    }
    
    session.headers.update(headers)
    
    try:
        url = 'https://www.bing.com/search'
        params = {'q': '北京酒店管理公司'}
        
        print(f"请求URL: {url}")
        print(f"参数: {params}")
        
        response = session.get(url, params=params, timeout=15)
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        
        # 保存完整响应
        with open('debug_bing_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("完整响应已保存到: debug_bing_response.html")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找必应特定的结果容器
        print("\n🔍 查找必应结果容器:")
        
        selectors = [
            'li.b_algo',
            '.b_algo',
            'li[class*="algo"]',
            'div[class*="result"]',
            '.b_results li',
            '#b_results li'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                print(f"  ✅ {selector}: 找到 {len(elements)} 个元素")
                
                # 显示前3个元素的结构
                for i, elem in enumerate(elements[:3], 1):
                    print(f"    元素 {i}:")
                    print(f"      类名: {elem.get('class', [])}")
                    
                    # 查找标题
                    h2 = elem.find('h2')
                    if h2:
                        title_link = h2.find('a')
                        if title_link:
                            print(f"      标题: {title_link.get_text()[:50]}...")
                            print(f"      链接: {title_link.get('href', '')[:80]}...")
                    
                    # 查找描述
                    desc = elem.find('p')
                    if desc:
                        print(f"      描述: {desc.get_text()[:100]}...")
            else:
                print(f"  ❌ {selector}: 未找到元素")
        
        # 查找所有包含"公司"的文本
        company_texts = soup.find_all(text=re.compile(r'.*公司.*'))
        print(f"\n🏢 包含'公司'的文本: {len(company_texts)}")
        for i, text in enumerate(company_texts[:5], 1):
            clean_text = text.strip()
            if len(clean_text) > 10:
                print(f"  {i}. {clean_text[:80]}...")
        
    except Exception as e:
        print(f"❌ 必应搜索调试失败: {e}")

def analyze_search_structure():
    """分析搜索结果结构"""
    print("\n📊 分析搜索结果结构")
    print("=" * 50)
    
    # 检查保存的文件
    files_to_check = ['debug_360_response.html', 'debug_bing_response.html']
    
    for filename in files_to_check:
        try:
            print(f"\n分析文件: {filename}")
            
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # 统计信息
            print(f"  文档长度: {len(content)} 字符")
            print(f"  总元素数: {len(soup.find_all())}")
            print(f"  链接数: {len(soup.find_all('a'))}")
            print(f"  div数: {len(soup.find_all('div'))}")
            print(f"  li数: {len(soup.find_all('li'))}")
            
            # 查找包含"公司"的元素
            company_elements = soup.find_all(text=re.compile(r'.*公司.*'))
            print(f"  包含'公司'的文本: {len(company_elements)}")
            
            # 查找可能的结果区域
            result_areas = soup.find_all(['div', 'li'], class_=re.compile(r'result|item|algo'))
            print(f"  可能的结果区域: {len(result_areas)}")
            
        except FileNotFoundError:
            print(f"  ❌ 文件 {filename} 不存在")
        except Exception as e:
            print(f"  ❌ 分析 {filename} 失败: {e}")

def main():
    """主函数"""
    print("🔧 搜索解析器调试工具")
    print("=" * 80)
    
    # 调试360搜索
    debug_360_search()
    
    time.sleep(3)
    
    # 调试必应搜索
    debug_bing_search()
    
    # 分析结构
    analyze_search_structure()
    
    print("\n✅ 调试完成")
    print("💡 请检查生成的HTML文件来分析搜索结果结构")

if __name__ == "__main__":
    main()
