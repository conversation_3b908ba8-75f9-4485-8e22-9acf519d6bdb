#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的数据抓取器
基于诊断结果，使用更有效的数据收集策略
"""

import sys
import os
from pathlib import Path
import requests
import time
import random
import re
import json
import pandas as pd
from urllib.parse import urlencode, quote
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class ImprovedDataCrawler:
    """改进的数据抓取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
        
    def setup_session(self):
        """设置会话配置"""
        # 更真实的User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]
        
        headers = {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        self.session.headers.update(headers)
        
        # 设置超时
        self.session.timeout = 15
        
    def search_with_multiple_engines(self, keyword, industry, region=None, max_results=20):
        """使用多个搜索引擎收集数据"""
        print(f"🔍 开始多引擎搜索: {keyword}")
        
        all_results = []
        
        # 搜索引擎配置
        search_engines = [
            {
                'name': '360搜索',
                'url': 'https://www.so.com/s',
                'params': {'q': keyword},
                'parser': self._parse_360_results
            },
            {
                'name': '必应搜索',
                'url': 'https://www.bing.com/search',
                'params': {'q': keyword},
                'parser': self._parse_bing_results
            }
        ]
        
        for engine in search_engines:
            try:
                print(f"  搜索 {engine['name']}...")
                
                # 添加随机延时
                time.sleep(random.uniform(2, 4))
                
                # 发送请求
                response = self.session.get(
                    engine['url'], 
                    params=engine['params'],
                    timeout=15
                )
                
                if response.status_code == 200:
                    # 解析结果
                    results = engine['parser'](response.text, keyword, industry)
                    all_results.extend(results)
                    print(f"    ✅ {engine['name']}: {len(results)} 条结果")
                else:
                    print(f"    ❌ {engine['name']}: 状态码 {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ {engine['name']}: {e}")
                continue
        
        # 去重和清理
        cleaned_results = self._clean_and_deduplicate(all_results)
        print(f"✅ 多引擎搜索完成: {len(cleaned_results)} 条有效结果")
        
        return cleaned_results[:max_results]
    
    def _parse_360_results(self, html_content, keyword, industry):
        """解析360搜索结果"""
        results = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找搜索结果
            result_items = soup.find_all(['div', 'li'], class_=re.compile(r'result|item'))
            
            for item in result_items[:10]:  # 限制数量
                try:
                    # 提取标题
                    title_elem = item.find(['h3', 'h4', 'a'], class_=re.compile(r'title|link'))
                    if not title_elem:
                        title_elem = item.find('a')
                    
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        url = title_elem.get('href', '')
                        
                        # 提取描述
                        desc_elem = item.find(['p', 'div'], class_=re.compile(r'desc|content|abstract'))
                        description = desc_elem.get_text(strip=True) if desc_elem else ''
                        
                        # 检查是否包含企业相关信息
                        if self._is_company_related(title, description, keyword):
                            company_name = self._extract_company_name(title, description)
                            if company_name:
                                results.append({
                                    'company_name': company_name,
                                    'industry': industry,
                                    'source': '360搜索',
                                    'data_type': 'search_result',
                                    'description': description[:200],
                                    'url': url,
                                    'search_keyword': keyword
                                })
                                
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"    解析360搜索结果失败: {e}")
            
        return results
    
    def _parse_bing_results(self, html_content, keyword, industry):
        """解析必应搜索结果"""
        results = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找搜索结果
            result_items = soup.find_all('li', class_='b_algo')
            
            for item in result_items[:10]:
                try:
                    # 提取标题
                    title_elem = item.find('h2')
                    if title_elem:
                        title_link = title_elem.find('a')
                        if title_link:
                            title = title_link.get_text(strip=True)
                            url = title_link.get('href', '')
                            
                            # 提取描述
                            desc_elem = item.find('p')
                            description = desc_elem.get_text(strip=True) if desc_elem else ''
                            
                            # 检查是否包含企业相关信息
                            if self._is_company_related(title, description, keyword):
                                company_name = self._extract_company_name(title, description)
                                if company_name:
                                    results.append({
                                        'company_name': company_name,
                                        'industry': industry,
                                        'source': '必应搜索',
                                        'data_type': 'search_result',
                                        'description': description[:200],
                                        'url': url,
                                        'search_keyword': keyword
                                    })
                                    
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"    解析必应搜索结果失败: {e}")
            
        return results
    
    def _is_company_related(self, title, description, keyword):
        """判断是否与企业相关"""
        text = (title + ' ' + description).lower()
        
        # 企业相关关键词
        company_keywords = [
            '公司', '企业', '集团', '有限', '股份', '科技', '实业', 
            '建设', '开发', '管理', '服务', '工程', '装饰', '设计'
        ]
        
        # 检查是否包含企业关键词
        has_company_keyword = any(kw in text for kw in company_keywords)
        
        # 检查是否包含搜索关键词
        has_search_keyword = any(kw in text for kw in keyword.split())
        
        return has_company_keyword and has_search_keyword
    
    def _extract_company_name(self, title, description):
        """提取企业名称"""
        text = title + ' ' + description
        
        # 企业名称正则模式
        patterns = [
            r'([^，。！？\s]{2,30}(?:有限公司|股份有限公司|集团有限公司|企业集团|科技有限公司))',
            r'([^，。！？\s]{2,30}(?:建设|工程|装饰|设计|开发|管理)(?:有限公司|公司))',
            r'([^，。！？\s]{2,30}(?:实业|科技|投资|控股)(?:有限公司|集团|公司))'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                # 清理和验证企业名称
                company_name = match.strip()
                if len(company_name) >= 4 and len(company_name) <= 50:
                    # 过滤掉明显不是企业名称的内容
                    if not any(word in company_name for word in ['网站', '搜索', '百度', '谷歌', '页面']):
                        return company_name
        
        return None
    
    def _clean_and_deduplicate(self, results):
        """清理和去重结果"""
        if not results:
            return []
        
        # 转换为DataFrame进行处理
        df = pd.DataFrame(results)
        
        # 去重（基于企业名称）
        df = df.drop_duplicates(subset=['company_name'], keep='first')
        
        # 过滤掉无效的企业名称
        df = df[df['company_name'].str.len() >= 4]
        df = df[df['company_name'].str.len() <= 50]
        
        # 过滤掉明显不是企业的结果
        invalid_keywords = ['网站', '搜索', '百度', '谷歌', '页面', '首页', '官网']
        for keyword in invalid_keywords:
            df = df[~df['company_name'].str.contains(keyword, na=False)]
        
        return df.to_dict('records')
    
    def search_industry_data(self, industry, province=None, city=None, max_results=20):
        """搜索行业数据"""
        print(f"🏢 开始搜索行业数据: {industry}")
        
        # 构建搜索关键词
        keywords = self._build_search_keywords(industry, province, city)
        
        all_results = []
        
        for keyword in keywords[:3]:  # 限制关键词数量
            try:
                results = self.search_with_multiple_engines(keyword, industry, max_results=10)
                all_results.extend(results)
                
                # 添加延时
                time.sleep(random.uniform(3, 5))
                
            except Exception as e:
                print(f"搜索关键词 '{keyword}' 失败: {e}")
                continue
        
        # 最终清理和限制结果数量
        final_results = self._clean_and_deduplicate(all_results)
        
        return final_results[:max_results]
    
    def _build_search_keywords(self, industry, province=None, city=None):
        """构建搜索关键词"""
        keywords = []
        
        # 基础关键词
        base_keywords = [
            f"{industry}公司",
            f"{industry}企业",
            f"{industry}有限公司"
        ]
        
        # 添加地区信息
        if province:
            for base in base_keywords:
                keywords.append(f"{province} {base}")
                
        if city:
            for base in base_keywords:
                keywords.append(f"{city} {base}")
        
        # 如果没有地区信息，使用基础关键词
        if not keywords:
            keywords = base_keywords
            
        return keywords

def test_improved_crawler():
    """测试改进的爬虫"""
    print("🚀 测试改进的数据抓取器")
    print("=" * 60)
    
    crawler = ImprovedDataCrawler()
    
    # 测试案例
    test_cases = [
        {
            "industry": "酒店管理",
            "province": "北京市",
            "city": "朝阳区"
        },
        {
            "industry": "房地产开发", 
            "province": "上海市",
            "city": "浦东新区"
        }
    ]
    
    all_results = []
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试案例 {i}: {case['industry']}")
        print(f"地区: {case['province']} {case['city']}")
        
        try:
            results = crawler.search_industry_data(
                industry=case['industry'],
                province=case['province'],
                city=case['city'],
                max_results=15
            )
            
            print(f"✅ 收集到 {len(results)} 条数据")
            
            # 显示样例
            if results:
                print("数据样例:")
                for idx, result in enumerate(results[:3], 1):
                    print(f"  {idx}. {result['company_name']}")
                    print(f"     来源: {result['source']}")
                    print(f"     描述: {result['description'][:50]}...")
            
            all_results.extend(results)
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    # 保存结果
    if all_results:
        df = pd.DataFrame(all_results)
        output_file = "data/improved_crawler_results.csv"
        os.makedirs("data", exist_ok=True)
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n💾 结果已保存到: {output_file}")
        print(f"📊 总计收集: {len(all_results)} 条数据")
    else:
        print("\n❌ 未收集到任何数据")

if __name__ == "__main__":
    test_improved_crawler()
