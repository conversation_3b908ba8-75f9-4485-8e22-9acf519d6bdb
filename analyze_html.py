#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析HTML结构脚本
"""

from bs4 import BeautifulSoup
import re

def analyze_bing_html():
    """分析必应HTML结构"""
    print("=== 分析必应HTML结构 ===")
    
    try:
        with open('debug_bing_response.html', 'r', encoding='utf-8') as f:
            html = f.read()
        
        soup = BeautifulSoup(html, 'html.parser')
        
        # 查找所有可能的搜索结果容器
        print('=== 查找搜索结果容器 ===')
        selectors = [
            'li.b_algo', 
            '.b_algo', 
            'li[class*="algo"]', 
            '.b_searchResult', 
            'li[class*="result"]', 
            'ol#b_results li', 
            '#b_results li',
            '#b_results > li',
            '.b_results li'
        ]
        
        for selector in selectors:
            items = soup.select(selector)
            print(f'{selector}: {len(items)} 个结果')
            if items:
                print(f'  第一个结果的类名: {items[0].get("class", [])}')
        
        # 查找主要结果区域
        print('\n=== 查找主要结果区域 ===')
        results_area = soup.find('ol', id='b_results')
        if results_area:
            print(f'找到结果区域: {results_area.name}, id={results_area.get("id")}')
            children = results_area.find_all('li', recursive=False)
            print(f'直接子元素li数量: {len(children)}')
            for i, child in enumerate(children[:3]):
                print(f'  子元素{i+1}: class={child.get("class", [])}')
        else:
            print('未找到 ol#b_results')
        
        # 查找包含公司信息的链接
        print('\n=== 查找包含公司关键词的链接 ===')
        links = soup.find_all('a')
        company_links = []
        for link in links:
            text = link.get_text().strip()
            if any(keyword in text for keyword in ['公司', '企业', '科技', '有限']) and len(text) > 5:
                company_links.append((text, link.get('href', '')))
        
        print(f'找到 {len(company_links)} 个可能的公司链接:')
        for i, (text, href) in enumerate(company_links[:10]):
            print(f'{i+1}. {text[:50]}...')
            print(f'   -> {href[:80]}...')
            print()
        
        # 查找所有h2, h3标签
        print('\n=== 查找标题标签 ===')
        for tag in ['h1', 'h2', 'h3']:
            elements = soup.find_all(tag)
            print(f'{tag}标签数量: {len(elements)}')
            for i, elem in enumerate(elements[:5]):
                text = elem.get_text().strip()
                if text and len(text) > 10:
                    print(f'  {i+1}. {text[:60]}...')
        
        return True
        
    except Exception as e:
        print(f"分析失败: {e}")
        return False

def analyze_baidu_html():
    """分析百度HTML结构"""
    print("\n=== 分析百度HTML结构 ===")
    
    try:
        with open('debug_baidu_response.html', 'r', encoding='utf-8') as f:
            html = f.read()
        
        print(f"HTML长度: {len(html)}")
        print("HTML内容预览:")
        print(html[:500])
        print("...")
        print(html[-200:])
        
        # 检查是否包含验证码或反爬页面
        if "验证" in html or "安全" in html:
            print("⚠️ 检测到可能的验证页面")
        
        return True
        
    except Exception as e:
        print(f"分析失败: {e}")
        return False

if __name__ == "__main__":
    analyze_baidu_html()
    analyze_bing_html()
