# 真实爬虫系统使用指南

## 🎉 系统概述

恭喜！您的真实数据爬取系统已经成功部署并集成到现有的客户信息收集系统中。现在您可以获取来自抖音、快手、企查查等主流平台的真实数据，而不再是模拟数据。

## 🚀 核心功能

### 1. 平台支持
- **抖音 (Douyin)**: 用户资料、视频信息、评论数据
- **快手 (Kuaishou)**: 用户资料、视频内容、热门视频
- **企查查 (Qichacha)**: 企业信息、联系方式、股东信息

### 2. 爬取模式
- **智能行业爬取**: 根据行业自动搜索相关企业和社交媒体账号
- **单平台爬取**: 针对特定平台进行精准数据收集
- **企业信息查询**: 深度挖掘企业详细信息

### 3. 反检测技术
- 浏览器指纹伪装
- 用户代理随机化
- 请求延迟控制
- 代理池支持
- 验证码处理

## 📋 使用步骤

### 第一步：启动系统
```bash
# 在项目目录下运行
streamlit run main.py --server.port 8502
```

### 第二步：系统检查
1. 打开浏览器访问 `http://localhost:8502`
2. 查看页面顶部的系统状态
3. 如果显示"✅ 真实数据爬取系统已启用"，说明系统正常

### 第三步：平台测试
1. 在左侧边栏找到"🚀 真实爬虫系统"部分
2. 点击"🧪 测试平台连通性"按钮
3. 等待测试完成，确保所有平台都显示"✅"

### 第四步：开始爬取
根据需求选择不同的爬取模式：

#### 智能行业爬取
1. 在"目标行业"输入框中输入行业关键词（如：酒店建设）
2. 选择"智能行业爬取"模式
3. 选择目标地区
4. 点击"开始收集"按钮

#### 单平台爬取
1. 选择"单平台爬取"模式
2. 选择目标平台（抖音/快手/企查查）
3. 输入目标URL或关键词
4. 点击"开始爬取"按钮

#### 企业信息查询
1. 选择"企业信息查询"模式
2. 输入企业名称
3. 点击"查询企业"按钮

## 🔧 高级配置

### 环境变量配置
复制 `real_crawlers/.env.example` 为 `real_crawlers/.env` 并配置：

```env
# 企查查账号（推荐配置）
QICHACHA_USERNAME=your_username
QICHACHA_PASSWORD=your_password

# 代理配置（推荐配置）
PROXY_HOST=your_proxy_host
PROXY_PORT=your_proxy_port

# 系统配置
HEADLESS=false  # 设为true可隐藏浏览器窗口
CONCURRENT_LIMIT=3  # 并发爬虫数量
REQUEST_DELAY=2000  # 请求延迟（毫秒）
```

### 命令行工具
您也可以直接使用命令行工具：

```bash
# 进入爬虫目录
cd real_crawlers

# 测试所有平台
python main.py test

# 爬取抖音用户
python main.py douyin https://www.douyin.com/user/xxx

# 爬取企业信息
python main.py qichacha 腾讯科技

# 按行业爬取
python main.py industry 酒店建设 --region 广东省

# 生成报告
python main.py report
```

## 📊 数据输出

### 数据存储位置
- **抖音数据**: `data/douyin/`
- **快手数据**: `data/kuaishou/`
- **企查查数据**: `data/qichacha/`
- **行业报告**: `data/industry/`
- **系统报告**: `data/reports/`

### 数据格式
所有数据以JSON格式存储，包含：
- 时间戳
- 数据来源
- 完整的原始数据
- 处理后的结构化数据

### 导出功能
系统支持将数据导出为：
- Excel文件 (.xlsx)
- CSV文件 (.csv)
- JSON文件 (.json)

## ⚠️ 重要提醒

### 合规使用
1. **遵守平台条款**: 请确保您的使用符合各平台的服务条款
2. **频率控制**: 避免过于频繁的请求，建议设置合理的延迟
3. **数据用途**: 仅将爬取的数据用于合法的商业用途

### 账号安全
1. **企查查登录**: 建议使用专门的账号进行爬取
2. **代理使用**: 强烈建议配置代理池避免IP被封
3. **验证码处理**: 遇到验证码时请手动处理

### 技术建议
1. **首次使用**: 建议设置 `HEADLESS=false` 观察爬取过程
2. **批量爬取**: 大量数据爬取时建议分批进行
3. **错误处理**: 遇到错误时查看日志文件获取详细信息

## 🔍 故障排除

### 常见问题

#### 1. 平台测试失败
- 检查网络连接
- 确认代理配置
- 查看防火墙设置

#### 2. 企查查登录失败
- 验证用户名密码
- 检查账号是否被限制
- 尝试手动登录网站

#### 3. 数据爬取为空
- 确认目标URL有效
- 检查页面结构是否变化
- 查看错误日志

#### 4. 浏览器启动失败
- 确认Playwright浏览器已安装
- 运行 `playwright install` 重新安装
- 检查系统权限

### 日志查看
```bash
# 查看爬虫日志
tail -f logs/crawler.log

# 查看系统日志
cat data/reports/platform_test_*.json
```

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查看日志**: 首先检查相关日志文件
2. **测试连通性**: 运行平台测试确认基础功能
3. **检查配置**: 验证环境变量和代理设置
4. **重启系统**: 尝试重启爬虫系统

## 🎯 最佳实践

1. **渐进式爬取**: 从小规模测试开始，逐步扩大范围
2. **定期维护**: 定期更新选择器和配置
3. **数据备份**: 重要数据及时备份
4. **监控运行**: 关注系统运行状态和错误率

---

**恭喜您成功部署了真实数据爬取系统！现在您可以获取真实、准确的客户信息，大大提升业务效率。**
