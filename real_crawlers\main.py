#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据爬取系统 - 主入口
"""

import asyncio
import sys
import argparse
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from crawler_manager import CrawlerManager

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='真实数据爬取系统')
    parser.add_argument('command', choices=['test', 'douyin', 'kuaishou', 'qichacha', 'industry', 'report'], 
                       help='执行的命令')
    parser.add_argument('target', nargs='?', help='目标URL或关键词')
    parser.add_argument('--region', help='地区（用于行业搜索）')
    parser.add_argument('--limit', type=int, default=10, help='结果数量限制')
    
    args = parser.parse_args()
    
    manager = CrawlerManager()
    
    print('🚀 真实数据爬取系统启动')
    print('=' * 50)
    
    try:
        if args.command == 'test':
            print('🧪 运行平台测试...')
            results = await manager.test_all_platforms()
            
            print('\n📊 测试结果:')
            for platform, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                print(f"  {platform.upper()}: {status}")
            
            success_count = sum(results.values())
            total_count = len(results)
            print(f"\n总结: {success_count}/{total_count} 个平台测试通过")
            
        elif args.command == 'douyin':
            if not args.target:
                print('❌ 请提供抖音用户URL')
                print('用法: python main.py douyin https://www.douyin.com/user/xxx')
                return
            
            print(f'🎵 开始爬取抖音用户: {args.target}')
            result = await manager.crawl_douyin_user(args.target)
            print(f"✅ 爬取完成，获得 {result.get('total_videos', 0)} 个视频")
            
        elif args.command == 'kuaishou':
            if not args.target:
                print('❌ 请提供快手用户URL')
                print('用法: python main.py kuaishou https://www.kuaishou.com/profile/xxx')
                return
            
            print(f'⚡ 开始爬取快手用户: {args.target}')
            result = await manager.crawl_kuaishou_user(args.target)
            print(f"✅ 爬取完成，获得 {result.get('total_videos', 0)} 个视频")
            
        elif args.command == 'qichacha':
            if not args.target:
                print('❌ 请提供企业名称')
                print('用法: python main.py qichacha 腾讯科技')
                return
            
            print(f'🏢 开始爬取企业信息: {args.target}')
            result = await manager.crawl_company_info(args.target)
            print(f"✅ 爬取完成，企业: {result.get('company_name', args.target)}")
            
        elif args.command == 'industry':
            if not args.target:
                print('❌ 请提供行业名称')
                print('用法: python main.py industry 酒店建设 --region 广东省')
                return
            
            region = args.region or ''
            print(f'🏭 开始按行业爬取: {args.target} {region}')
            result = await manager.crawl_by_industry(args.target, region)
            
            companies = len(result['data']['companies'])
            douyin_users = len(result['data']['douyin_users'])
            kuaishou_users = len(result['data']['kuaishou_users'])
            
            print(f"✅ 行业爬取完成:")
            print(f"  企业: {companies} 个")
            print(f"  抖音用户: {douyin_users} 个")
            print(f"  快手用户: {kuaishou_users} 个")
            
        elif args.command == 'report':
            print('📊 生成爬取报告...')
            report = await manager.generate_report()
            
            print('\n📋 数据统计:')
            for platform, stats in report['platform_statistics'].items():
                print(f"  {platform.upper()}: {stats['total_files']} 个文件")
            
            print(f"  行业报告: {report['file_statistics'].get('industry_reports', 0)} 个")
            print(f"  总数据文件: {report['data_summary']['total_data_files']} 个")
            
    except KeyboardInterrupt:
        print('\n⚠️ 用户中断操作')
    except Exception as e:
        print(f'❌ 执行失败: {str(e)}')
        return 1
    
    print('\n🎉 操作完成!')
    return 0

def show_usage():
    """显示使用说明"""
    print('📖 真实数据爬取系统使用说明:')
    print('')
    print('测试所有平台:')
    print('  python main.py test')
    print('')
    print('爬取抖音用户:')
    print('  python main.py douyin https://www.douyin.com/user/xxx')
    print('')
    print('爬取快手用户:')
    print('  python main.py kuaishou https://www.kuaishou.com/profile/xxx')
    print('')
    print('爬取企查查企业:')
    print('  python main.py qichacha 腾讯科技')
    print('')
    print('按行业爬取:')
    print('  python main.py industry 酒店建设 --region 广东省')
    print('')
    print('生成报告:')
    print('  python main.py report')
    print('')
    print('⚠️  重要提醒:')
    print('   - 请遵守各平台的使用条款')
    print('   - 建议配置代理避免IP被封')
    print('   - 企查查需要真实账号登录')
    print('   - 首次运行建议设置 HEADLESS=false 观察过程')

if __name__ == '__main__':
    if len(sys.argv) == 1:
        show_usage()
    else:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
