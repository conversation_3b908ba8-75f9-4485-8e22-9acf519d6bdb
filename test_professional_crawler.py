#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业爬虫测试脚本
测试招标、房地产、酒店建设等专业数据源
"""

import sys
import os
from pathlib import Path
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_professional_crawler_import():
    """测试专业爬虫模块导入"""
    print("🔍 测试专业爬虫模块导入...")
    
    try:
        from src.professional_crawler import ProfessionalCrawler
        print("✅ ProfessionalCrawler 导入成功")
        return True
    except Exception as e:
        print(f"❌ ProfessionalCrawler 导入失败: {e}")
        traceback.print_exc()
        return False

def test_professional_config():
    """测试专业配置文件"""
    print("\n⚙️ 测试专业配置文件...")
    
    try:
        import yaml
        with open('professional_sources_config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必要的配置节
        required_sections = ['bidding_sites', 'real_estate_sites', 'hotel_construction_sites', 'search_keywords']
        for section in required_sections:
            if section in config:
                print(f"✅ 配置节 {section} 存在")
            else:
                print(f"❌ 配置节 {section} 缺失")
                return False
        
        # 检查招标网站配置
        bidding_sites = config.get('bidding_sites', {})
        gov_sites = bidding_sites.get('government_procurement', [])
        print(f"✅ 政府采购网站配置: {len(gov_sites)} 个")
        
        # 检查搜索关键词配置
        keywords = config.get('search_keywords', {})
        bidding_keywords = keywords.get('bidding_keywords', {})
        print(f"✅ 招标关键词配置: {len(bidding_keywords)} 个类别")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_professional_crawler_init():
    """测试专业爬虫初始化"""
    print("\n🚀 测试专业爬虫初始化...")
    
    try:
        from src.professional_crawler import ProfessionalCrawler
        
        crawler = ProfessionalCrawler()
        
        if hasattr(crawler, 'config') and crawler.config:
            print("✅ 专业爬虫配置加载成功")
        else:
            print("❌ 专业爬虫配置加载失败")
            return False
        
        if hasattr(crawler, 'session') and crawler.session:
            print("✅ 专业爬虫会话初始化成功")
        else:
            print("❌ 专业爬虫会话初始化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 专业爬虫初始化失败: {e}")
        traceback.print_exc()
        return False

def test_keyword_generation():
    """测试关键词生成功能"""
    print("\n🔤 测试关键词生成功能...")
    
    try:
        from src.professional_crawler import ProfessionalCrawler
        
        crawler = ProfessionalCrawler()
        
        # 测试招标关键词生成
        bidding_keywords = crawler._generate_bidding_keywords("酒店建设", "广东省", "深圳市")
        print(f"✅ 招标关键词生成: {len(bidding_keywords)} 个")
        if bidding_keywords:
            print(f"   示例: {bidding_keywords[0]}")
        
        # 测试房地产关键词生成
        real_estate_keywords = crawler._generate_real_estate_keywords("房地产开发", "广东省", "深圳市")
        print(f"✅ 房地产关键词生成: {len(real_estate_keywords)} 个")
        if real_estate_keywords:
            print(f"   示例: {real_estate_keywords[0]}")
        
        # 测试酒店关键词生成
        hotel_keywords = crawler._generate_hotel_keywords("酒店管理", "广东省", "深圳市")
        print(f"✅ 酒店关键词生成: {len(hotel_keywords)} 个")
        if hotel_keywords:
            print(f"   示例: {hotel_keywords[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 关键词生成测试失败: {e}")
        traceback.print_exc()
        return False

def test_integrated_crawler():
    """测试集成后的主爬虫"""
    print("\n🕷️ 测试集成后的主爬虫...")
    
    try:
        from src.crawler_engine import CrawlerEngine
        
        crawler = CrawlerEngine()
        
        # 检查专业爬虫是否成功集成
        if hasattr(crawler, 'professional_crawler') and crawler.professional_crawler:
            print("✅ 专业爬虫成功集成到主爬虫")
        else:
            print("⚠️ 专业爬虫未成功集成，但主爬虫仍可工作")
        
        # 测试配置
        crawler_config = {
            'industry': '酒店建设',
            'province': '广东省',
            'city': '深圳市',
            'search_depth': 1,
            'use_baidu': False,  # 百度被反爬，暂时禁用
            'use_bing': False,   # 暂时禁用必应，专注测试专业源
            'use_business_dirs': False,
            'use_professional_sources': True  # 启用专业数据源
        }
        
        print("开始小规模测试...")
        raw_data = crawler.start_crawling(crawler_config)
        
        if raw_data.empty:
            print("⚠️ 未获取到数据，这可能是正常的（网站访问限制等）")
        else:
            print(f"✅ 获取到 {len(raw_data)} 条数据")
            print("数据样本:")
            print(raw_data.head())
        
        return True
        
    except Exception as e:
        print(f"❌ 集成爬虫测试失败: {e}")
        traceback.print_exc()
        return False

def test_search_url_building():
    """测试搜索URL构建"""
    print("\n🔗 测试搜索URL构建...")
    
    try:
        from src.professional_crawler import ProfessionalCrawler
        
        crawler = ProfessionalCrawler()
        
        # 测试URL构建
        site_config = {
            'name': '测试网站',
            'search_url': 'http://example.com/search',
            'base_url': 'http://example.com'
        }
        
        url = crawler._build_search_url(site_config, '深圳酒店建设')
        print(f"✅ URL构建成功: {url}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL构建测试失败: {e}")
        return False

def test_data_extraction():
    """测试数据提取功能"""
    print("\n📊 测试数据提取功能...")
    
    try:
        from src.professional_crawler import ProfessionalCrawler
        
        crawler = ProfessionalCrawler()
        
        # 测试公司名称提取
        test_titles = [
            "深圳市某某酒店建设工程招标公告",
            "中标单位：深圳建筑工程有限公司",
            "投标人：广东省酒店管理集团股份有限公司"
        ]
        
        for title in test_titles:
            company = crawler._extract_company_from_title(title)
            print(f"✅ 从 '{title[:30]}...' 提取公司: {company}")
        
        # 测试位置提取
        test_content = "项目位置：深圳市南山区科技园南区 联系地址：广东省深圳市福田区中心大道123号"
        location = crawler._extract_location_from_content(test_content)
        print(f"✅ 位置提取: {location}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据提取测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始专业爬虫系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("模块导入", test_professional_crawler_import()))
    test_results.append(("配置文件", test_professional_config()))
    test_results.append(("爬虫初始化", test_professional_crawler_init()))
    test_results.append(("关键词生成", test_keyword_generation()))
    test_results.append(("URL构建", test_search_url_building()))
    test_results.append(("数据提取", test_data_extraction()))
    test_results.append(("集成测试", test_integrated_crawler()))
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("📋 专业爬虫测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总测试项: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有专业爬虫测试通过！")
        print("💡 建议:")
        print("- 专业爬虫模块已就绪")
        print("- 可以开始实际的数据采集测试")
        print("- 建议针对具体网站优化解析逻辑")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 项测试失败")
        print("💡 建议:")
        print("- 检查失败的测试项")
        print("- 确保所有依赖正确安装")
        print("- 检查配置文件格式")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
