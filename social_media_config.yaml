# 社交媒体平台配置文件
# 支持抖音、快手等短视频平台的企业信息爬取

platforms:
  douyin:
    enabled: true
    name: "抖音"
    search_api: "https://www.douyin.com/aweme/v1/web/general/search/"
    user_api: "https://www.douyin.com/aweme/v1/web/aweme/profile/"
    headers:
      User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      Referer: "https://www.douyin.com/"
      Accept: "application/json, text/plain, */*"
      Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
      Accept-Encoding: "gzip, deflate, br"
      Connection: "keep-alive"
      Sec-Fetch-Dest: "empty"
      Sec-Fetch-Mode: "cors"
      Sec-Fetch-Site: "same-origin"
    rate_limit:
      requests_per_minute: 30
      delay_between_requests: 2
    
  kuaishou:
    enabled: true
    name: "快手"
    search_api: "https://www.kuaishou.com/graphql"
    user_api: "https://www.kuaishou.com/profile/"
    headers:
      User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      Referer: "https://www.kuaishou.com/"
      Accept: "application/json"
      Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8"
      Accept-Encoding: "gzip, deflate, br"
      Connection: "keep-alive"
      Content-Type: "application/json"
    rate_limit:
      requests_per_minute: 25
      delay_between_requests: 3

search_keywords:
  business_types:
    - "{industry} 企业"
    - "{industry} 公司"
    - "{industry} 商家"
    - "{industry} 品牌"
    - "{industry} 官方"
    - "{industry} 厂家"
    - "{industry} 供应商"
    - "{industry} 制造商"
    - "{industry} 服务商"
    - "{industry} 专业"
  
  location_keywords:
    - "{region} {industry}"
    - "{province} {industry}"
    - "{city} {industry}"
    - "{region} {industry} 企业"
    - "{province} {industry} 公司"
    - "{city} {industry} 商家"
  
  industry_specific:
    hotel:
      - "酒店建设"
      - "酒店装修"
      - "酒店设计"
      - "酒店工程"
      - "酒店管理"
      - "民宿建设"
      - "度假村建设"
    
    real_estate:
      - "房地产开发"
      - "地产项目"
      - "房地产投资"
      - "地产建设"
      - "房地产营销"
      - "地产策划"
    
    construction:
      - "建筑工程"
      - "建筑设计"
      - "建筑施工"
      - "装修工程"
      - "建材供应"
      - "工程承包"

filtering_rules:
  min_followers: 1000  # 最少粉丝数
  max_results_per_keyword: 20  # 每个关键词最大结果数
  
  business_indicators:
    - "公司"
    - "企业"
    - "集团"
    - "有限"
    - "股份"
    - "官方"
    - "品牌"
    - "商家"
    - "厂家"
    - "供应商"
    - "制造商"
    - "服务商"
    - "专业"
    - "认证"
    - "蓝V"
  
  exclude_keywords:
    - "个人"
    - "网红"
    - "达人"
    - "博主"
    - "主播"
    - "娱乐"
    - "搞笑"
    - "段子"
    - "美食"
    - "旅游"
    - "时尚"
    - "美妆"
    - "游戏"
    - "音乐"
    - "舞蹈"
  
  required_keywords:
    - ["企业", "公司", "商家", "品牌", "官方"]  # 至少包含其中一个

data_extraction:
  contact_patterns:
    phone:
      - "(?:电话|联系|Tel|TEL|手机|微信)[:：\\s]*([0-9\\-\\s\\(\\)]{7,20})"
      - "(\\d{3,4}[-\\s]?\\d{7,8})"  # 固定电话
      - "(1[3-9]\\d{9})"  # 手机号码
      - "(\\d{3}-\\d{4}-\\d{4})"  # 格式化电话
    
    email:
      - "([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})"
    
    wechat:
      - "(?:微信|WeChat|wechat)[:：\\s]*([a-zA-Z0-9_-]{6,20})"
      - "VX[:：\\s]*([a-zA-Z0-9_-]{6,20})"
      - "wx[:：\\s]*([a-zA-Z0-9_-]{6,20})"
    
    address:
      - "(?:地址|位置|Address)[:：\\s]*([^，。；\\n]{10,50})"
      - "([^，。；\\n]*(?:省|市|区|县|街道|路|号)[^，。；\\n]*)"
    
    website:
      - "(https?://[^\\s<>\"]+)"
      - "(?:网站|官网|网址)[:：\\s]*(https?://[^\\s<>\"]+)"

anti_detection:
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
    - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
  
  random_delays:
    min_delay: 1
    max_delay: 5
  
  retry_settings:
    max_retries: 3
    retry_delay: 5
    backoff_factor: 2

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/social_media_crawler.log"

# 演示数据配置
demo_data:
  enabled: true
  companies_per_platform: 2
  follower_ranges:
    min: 1000
    max: 100000
  
  name_templates:
    - "{region}{industry}科技有限公司"
    - "{region}{industry}实业股份有限公司"
    - "{region}{industry}集团"
    - "{region}{industry}发展有限公司"
    - "{region}{industry}创新科技公司"
  
  description_templates:
    - "专业{industry}服务商，提供优质{industry}解决方案"
    - "{region}知名{industry}品牌，行业领先企业"
    - "{industry}生产厂家，质量保证，价格优惠"
    - "专业{industry}批发，支持一件代发，全国包邮"
    - "新兴{industry}科技企业，专注技术创新"
