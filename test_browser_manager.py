#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BrowserManager测试脚本
验证浏览器管理器的各项功能
"""

import asyncio
import sys
import logging
from src.browser_manager import BrowserManager, get_browser_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_browser_manager():
    """测试BrowserManager功能"""
    print("🔍 开始测试BrowserManager...")
    
    manager = None
    try:
        # 创建浏览器管理器
        manager = BrowserManager(headless=True, max_browsers=2)
        print("✅ BrowserManager创建成功")
        
        # 启动Playwright
        await manager.start()
        print("✅ Playwright启动成功")
        
        # 创建浏览器实例
        browser = await manager.create_browser()
        print("✅ 浏览器实例创建成功")
        
        # 创建浏览器上下文
        context = await manager.create_context(browser)
        print("✅ 浏览器上下文创建成功")
        
        # 创建页面
        page = await manager.create_page(context)
        print("✅ 页面创建成功")
        
        # 测试页面导航
        await page.goto("https://www.baidu.com")
        title = await page.title()
        print(f"✅ 页面导航成功，标题: {title}")
        
        # 测试反检测功能
        webdriver_result = await page.evaluate("navigator.webdriver")
        print(f"✅ webdriver检测结果: {webdriver_result} (应该是undefined)")
        
        chrome_result = await page.evaluate("typeof window.chrome")
        print(f"✅ chrome对象检测结果: {chrome_result} (应该是object)")
        
        # 关闭页面
        await page.close()
        print("✅ 页面关闭成功")
        
        # 关闭上下文
        await manager.close_context(context)
        print("✅ 上下文关闭成功")
        
        # 关闭浏览器
        await manager.close_browser(browser)
        print("✅ 浏览器关闭成功")
        
        print("\n🎉 BrowserManager测试完成，所有功能正常！")
        return True
        
    except Exception as e:
        print(f"❌ BrowserManager测试失败: {e}")
        return False
        
    finally:
        if manager:
            await manager.close_all()
            print("✅ 资源清理完成")

async def test_global_manager():
    """测试全局管理器功能"""
    print("\n🔍 开始测试全局BrowserManager...")
    
    try:
        # 获取全局管理器
        manager = get_browser_manager(headless=True)
        print("✅ 全局BrowserManager获取成功")
        
        # 启动
        await manager.start()
        
        # 创建浏览器和页面
        browser = await manager.get_or_create_browser()
        context = await manager.create_context(browser)
        page = await manager.create_page(context)
        
        # 简单测试
        await page.goto("https://httpbin.org/user-agent")
        content = await page.content()
        print("✅ 全局管理器页面访问成功")
        
        # 清理
        from src.browser_manager import cleanup_browser_manager
        await cleanup_browser_manager()
        print("✅ 全局管理器清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 全局管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("BrowserManager功能测试")
    print("=" * 60)
    
    async def run_tests():
        # 测试基础功能
        test1_success = await test_browser_manager()
        
        # 测试全局管理器
        test2_success = await test_global_manager()
        
        return test1_success and test2_success
    
    # 运行所有测试
    success = asyncio.run(run_tests())
    
    if success:
        print("\n✅ 所有测试通过，BrowserManager可以正常使用")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查实现")
        sys.exit(1)

if __name__ == "__main__":
    main()
