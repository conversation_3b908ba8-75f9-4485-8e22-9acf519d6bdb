#!/usr/bin/env node

require('dotenv').config();
const CrawlerManager = require('./crawlerManager');

async function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    const crawlerManager = new CrawlerManager();

    console.log('🚀 真实数据爬取系统启动');
    console.log('=' * 50);

    try {
        switch (command) {
            case 'test':
                console.log('🧪 运行平台测试...');
                const testResults = await crawlerManager.testAllPlatforms();
                console.log('\n📊 测试完成!');
                break;

            case 'douyin':
                const douyinUrl = args[1];
                if (!douyinUrl) {
                    console.error('❌ 请提供抖音用户URL');
                    console.log('用法: node src/index.js douyin https://www.douyin.com/user/xxx');
                    process.exit(1);
                }
                await crawlerManager.crawlDouyin(douyinUrl);
                break;

            case 'kuaishou':
                const kuaishouUrl = args[1];
                if (!kuaishouUrl) {
                    console.error('❌ 请提供快手用户URL');
                    console.log('用法: node src/index.js kuaishou https://www.kuaishou.com/profile/xxx');
                    process.exit(1);
                }
                await crawlerManager.crawlKuaishou(kuaishouUrl);
                break;

            case 'qichacha':
                const companyName = args[1];
                if (!companyName) {
                    console.error('❌ 请提供企业名称');
                    console.log('用法: node src/index.js qichacha 腾讯科技');
                    process.exit(1);
                }
                await crawlerManager.crawlQichacha(companyName);
                break;

            case 'industry':
                const industry = args[1];
                const region = args[2] || '';
                if (!industry) {
                    console.error('❌ 请提供行业名称');
                    console.log('用法: node src/index.js industry 酒店建设 广东省');
                    process.exit(1);
                }
                await crawlerManager.crawlByIndustry(industry, region);
                break;

            case 'report':
                console.log('📊 生成爬取报告...');
                const report = await crawlerManager.generateReport();
                console.log(`✅ 报告生成完成，共处理 ${report.totalTasks} 个任务`);
                break;

            default:
                console.log('📖 使用说明:');
                console.log('');
                console.log('测试所有平台:');
                console.log('  node src/index.js test');
                console.log('');
                console.log('爬取抖音用户:');
                console.log('  node src/index.js douyin https://www.douyin.com/user/xxx');
                console.log('');
                console.log('爬取快手用户:');
                console.log('  node src/index.js kuaishou https://www.kuaishou.com/profile/xxx');
                console.log('');
                console.log('爬取企查查企业:');
                console.log('  node src/index.js qichacha 腾讯科技');
                console.log('');
                console.log('按行业爬取:');
                console.log('  node src/index.js industry 酒店建设 广东省');
                console.log('');
                console.log('生成报告:');
                console.log('  node src/index.js report');
                break;
        }

    } catch (error) {
        console.error('❌ 执行失败:', error.message);
        process.exit(1);
    }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    process.exit(1);
});

if (require.main === module) {
    main();
}

module.exports = { CrawlerManager };
