#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业信息平台爬虫模块
专注于企查查、天眼查、钉钉企典等企业信息平台的数据爬取
"""

import requests
import time
import random
import re
import json
import pandas as pd
from urllib.parse import urljoin, urlparse, quote
from typing import List, Dict, Any, Optional
import yaml
from pathlib import Path
from datetime import datetime, timedelta

try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None

try:
    from fake_useragent import UserAgent
except ImportError:
    UserAgent = None

from .utils.logger import get_logger
from .region_manager import RegionManager

class EnterpriseInfoCrawler:
    """企业信息平台爬虫类"""
    
    def __init__(self, config_path: str = "enterprise_info_config.yaml"):
        """初始化企业信息爬虫"""
        self.logger = get_logger(__name__)
        self.config = self._load_config(config_path)
        self.ua = UserAgent() if UserAgent else None
        self.session = requests.Session()
        self.region_manager = RegionManager()
        
        # 设置请求头
        self._setup_session()
        
    def _load_config(self, config_path: str) -> Dict:
        """加载企业信息平台配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载企业信息配置失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'platforms': {
                'qichacha': {
                    'enabled': True,
                    'name': '企查查',
                    'search_url': 'https://www.qcc.com/web/search',
                    'api_url': 'https://www.qcc.com/api/search/company',
                    'headers': {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'https://www.qcc.com/',
                        'Accept': 'application/json, text/plain, */*'
                    }
                },
                'tianyancha': {
                    'enabled': True,
                    'name': '天眼查',
                    'search_url': 'https://www.tianyancha.com/search',
                    'api_url': 'https://www.tianyancha.com/services/v3/t/search/sug',
                    'headers': {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'https://www.tianyancha.com/',
                        'Accept': 'application/json'
                    }
                },
                'dingding': {
                    'enabled': True,
                    'name': '钉钉企典',
                    'search_url': 'https://aiqicha.baidu.com/s',
                    'api_url': 'https://aiqicha.baidu.com/ajax/search',
                    'headers': {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'https://aiqicha.baidu.com/',
                        'Accept': 'application/json'
                    }
                }
            },
            'search_parameters': {
                'company_types': [
                    '有限责任公司',
                    '股份有限公司',
                    '个人独资企业',
                    '合伙企业',
                    '外商投资企业'
                ],
                'status_filter': ['存续', '在业'],
                'industry_keywords': [
                    '{industry}',
                    '{industry}相关',
                    '{industry}服务',
                    '{industry}技术',
                    '{industry}设备'
                ]
            },
            'data_fields': {
                'basic_info': [
                    'company_name', 'legal_person', 'registered_capital',
                    'establishment_date', 'business_status', 'company_type',
                    'registration_number', 'tax_number', 'organization_code'
                ],
                'contact_info': [
                    'phone', 'email', 'website', 'address',
                    'postal_code', 'fax'
                ],
                'business_info': [
                    'business_scope', 'industry', 'main_business',
                    'employee_count', 'annual_revenue'
                ]
            }
        }
    
    def _setup_session(self):
        """设置会话请求头"""
        headers = {
            'User-Agent': self.ua.random if self.ua else 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
        }
        self.session.headers.update(headers)
    
    def search_enterprise_platforms(self, industry: str, province: str = None, city: str = None, 
                                  platforms: List[str] = None) -> pd.DataFrame:
        """搜索企业信息平台"""
        if not platforms:
            platforms = ['qichacha', 'tianyancha', 'dingding']
        
        all_results = []
        
        for platform in platforms:
            if platform not in self.config.get('platforms', {}):
                self.logger.warning(f"不支持的平台: {platform}")
                continue
                
            platform_config = self.config['platforms'][platform]
            if not platform_config.get('enabled', False):
                self.logger.info(f"平台 {platform} 已禁用")
                continue
            
            self.logger.info(f"开始搜索 {platform_config['name']} 平台")
            
            if platform == 'qichacha':
                results = self._search_qichacha(industry, province, city)
            elif platform == 'tianyancha':
                results = self._search_tianyancha(industry, province, city)
            elif platform == 'dingding':
                results = self._search_dingding(industry, province, city)
            else:
                continue
            
            all_results.extend(results)
            
            # 避免请求过快
            time.sleep(random.uniform(3, 6))
        
        return pd.DataFrame(all_results)
    
    def _search_qichacha(self, industry: str, province: str = None, city: str = None) -> List[Dict]:
        """搜索企查查平台"""
        results = []
        
        # 生成搜索关键词
        keywords = self._generate_enterprise_keywords(industry, province, city)
        
        platform_config = self.config['platforms']['qichacha']
        
        for keyword in keywords[:3]:  # 限制关键词数量
            try:
                self.logger.info(f"企查查搜索关键词: {keyword}")
                
                # 构建搜索参数
                search_params = {
                    'key': keyword,
                    'index': '1',
                    'sortField': '0',
                    'isSortAsc': 'false'
                }
                
                # 添加地区筛选
                if province:
                    search_params['province'] = province
                if city:
                    search_params['city'] = city
                
                # 更新请求头
                headers = platform_config.get('headers', {})
                
                # 发送搜索请求
                response = self.session.get(
                    platform_config['search_url'],
                    params=search_params,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    # 解析搜索结果
                    search_results = self._parse_qichacha_results(response.text, keyword, industry)
                    results.extend(search_results)
                    
                    self.logger.info(f"企查查关键词 '{keyword}': {len(search_results)} 条结果")
                else:
                    self.logger.warning(f"企查查搜索请求失败: {response.status_code}")
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                self.logger.error(f"企查查搜索出错: {e}")
                continue
        
        # 如果没有获取到真实数据，返回空结果
        if not results:
            self.logger.warning("企查查未获取到真实数据")
        
        return results
    
    def _search_tianyancha(self, industry: str, province: str = None, city: str = None) -> List[Dict]:
        """搜索天眼查平台"""
        results = []
        
        # 生成搜索关键词
        keywords = self._generate_enterprise_keywords(industry, province, city)
        
        platform_config = self.config['platforms']['tianyancha']
        
        for keyword in keywords[:3]:  # 限制关键词数量
            try:
                self.logger.info(f"天眼查搜索关键词: {keyword}")
                
                # 构建搜索参数
                search_params = {
                    'key': keyword,
                    'sessionNo': str(int(time.time() * 1000)),
                    'checkFrom': 'searchBox'
                }
                
                # 更新请求头
                headers = platform_config.get('headers', {})
                
                # 发送搜索请求
                response = self.session.get(
                    platform_config['api_url'],
                    params=search_params,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    # 解析搜索结果
                    search_results = self._parse_tianyancha_results(response.text, keyword, industry)
                    results.extend(search_results)
                    
                    self.logger.info(f"天眼查关键词 '{keyword}': {len(search_results)} 条结果")
                else:
                    self.logger.warning(f"天眼查搜索请求失败: {response.status_code}")
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                self.logger.error(f"天眼查搜索出错: {e}")
                continue
        
        # 如果没有获取到真实数据，返回空结果
        if not results:
            self.logger.warning("天眼查未获取到真实数据")
        
        return results
    
    def _search_dingding(self, industry: str, province: str = None, city: str = None) -> List[Dict]:
        """搜索钉钉企典平台"""
        results = []
        
        # 生成搜索关键词
        keywords = self._generate_enterprise_keywords(industry, province, city)
        
        platform_config = self.config['platforms']['dingding']
        
        for keyword in keywords[:3]:  # 限制关键词数量
            try:
                self.logger.info(f"钉钉企典搜索关键词: {keyword}")
                
                # 构建搜索参数
                search_params = {
                    'q': keyword,
                    't': '0'  # 企业搜索
                }
                
                # 更新请求头
                headers = platform_config.get('headers', {})
                
                # 发送搜索请求
                response = self.session.get(
                    platform_config['search_url'],
                    params=search_params,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    # 解析搜索结果
                    search_results = self._parse_dingding_results(response.text, keyword, industry)
                    results.extend(search_results)
                    
                    self.logger.info(f"钉钉企典关键词 '{keyword}': {len(search_results)} 条结果")
                else:
                    self.logger.warning(f"钉钉企典搜索请求失败: {response.status_code}")
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                self.logger.error(f"钉钉企典搜索出错: {e}")
                continue
        
        # 如果没有获取到真实数据，返回空结果
        if not results:
            self.logger.warning("钉钉企典未获取到真实数据")
        
        return results

    def _generate_enterprise_keywords(self, industry: str, province: str = None, city: str = None) -> List[str]:
        """生成企业搜索关键词"""
        keywords = []

        # 获取区域信息
        region = self.region_manager.get_region_display_name(province, city)

        # 基础行业关键词
        industry_keywords = self.config.get('search_parameters', {}).get('industry_keywords', [])
        for keyword_template in industry_keywords:
            keyword = keyword_template.format(industry=industry)
            keywords.append(keyword)

        # 地区相关关键词
        if province or city:
            keywords.extend([
                f"{region} {industry}",
                f"{province or ''} {industry}".strip(),
                f"{city or ''} {industry}".strip()
            ])

        return [k for k in keywords if k.strip()]

    def _parse_qichacha_results(self, response_text: str, keyword: str, industry: str) -> List[Dict]:
        """解析企查查搜索结果"""
        results = []

        try:
            if BeautifulSoup:
                soup = BeautifulSoup(response_text, 'html.parser')

                # 查找企业列表
                company_items = soup.find_all(['div', 'li'], class_=re.compile(r'(company|result|item)'))

                for item in company_items:
                    try:
                        company_info = self._extract_qichacha_company_info(item, keyword, industry)
                        if company_info:
                            results.append(company_info)
                    except Exception as e:
                        self.logger.debug(f"解析企查查企业项出错: {e}")
                        continue
            else:
                # 使用正则表达式解析
                results = self._parse_enterprise_with_regex(response_text, keyword, industry, 'qichacha')

        except Exception as e:
            self.logger.error(f"解析企查查结果出错: {e}")

        return results

    def _parse_tianyancha_results(self, response_text: str, keyword: str, industry: str) -> List[Dict]:
        """解析天眼查搜索结果"""
        results = []

        try:
            # 尝试解析JSON响应
            data = json.loads(response_text)

            if 'data' in data and isinstance(data['data'], list):
                companies = data['data']

                for company in companies:
                    try:
                        company_info = self._extract_tianyancha_company_info(company, keyword, industry)
                        if company_info:
                            results.append(company_info)
                    except Exception as e:
                        self.logger.debug(f"解析天眼查企业项出错: {e}")
                        continue

        except json.JSONDecodeError:
            # 如果不是JSON，尝试HTML解析
            if BeautifulSoup:
                soup = BeautifulSoup(response_text, 'html.parser')
                company_items = soup.find_all(['div', 'li'], class_=re.compile(r'(company|result|item)'))

                for item in company_items:
                    try:
                        company_info = self._extract_tianyancha_html_info(item, keyword, industry)
                        if company_info:
                            results.append(company_info)
                    except Exception as e:
                        self.logger.debug(f"解析天眼查HTML企业项出错: {e}")
                        continue
        except Exception as e:
            self.logger.error(f"解析天眼查结果出错: {e}")

        return results

    def _parse_dingding_results(self, response_text: str, keyword: str, industry: str) -> List[Dict]:
        """解析钉钉企典搜索结果"""
        results = []

        try:
            if BeautifulSoup:
                soup = BeautifulSoup(response_text, 'html.parser')

                # 查找企业列表
                company_items = soup.find_all(['div', 'li'], class_=re.compile(r'(company|result|item)'))

                for item in company_items:
                    try:
                        company_info = self._extract_dingding_company_info(item, keyword, industry)
                        if company_info:
                            results.append(company_info)
                    except Exception as e:
                        self.logger.debug(f"解析钉钉企典企业项出错: {e}")
                        continue
            else:
                # 使用正则表达式解析
                results = self._parse_enterprise_with_regex(response_text, keyword, industry, 'dingding')

        except Exception as e:
            self.logger.error(f"解析钉钉企典结果出错: {e}")

        return results

    def _extract_qichacha_company_info(self, item_element, keyword: str, industry: str) -> Optional[Dict]:
        """提取企查查企业信息"""
        try:
            # 提取公司名称
            name_elem = item_element.find(['h3', 'h4', 'a'], class_=re.compile(r'(name|title|company)'))
            if not name_elem:
                name_elem = item_element.find('a')

            if not name_elem:
                return None

            company_name = name_elem.get_text().strip()
            company_url = name_elem.get('href', '') if name_elem.name == 'a' else ''

            # 提取其他信息
            content = item_element.get_text()

            # 提取法人代表
            legal_person_match = re.search(r'法定代表人[:：]\s*([^\s，。；]+)', content)
            legal_person = legal_person_match.group(1) if legal_person_match else ''

            # 提取注册资本
            capital_match = re.search(r'注册资本[:：]\s*([^\s，。；]+)', content)
            registered_capital = capital_match.group(1) if capital_match else ''

            # 提取成立日期
            date_match = re.search(r'成立日期[:：]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})', content)
            establishment_date = date_match.group(1) if date_match else ''

            # 提取经营状态
            status_match = re.search(r'经营状态[:：]\s*([^\s，。；]+)', content)
            business_status = status_match.group(1) if status_match else ''

            # 过滤无效结果
            if not self._is_valid_enterprise_result(company_name, content, industry):
                return None

            return {
                'company_name': company_name,
                'legal_person': legal_person,
                'registered_capital': registered_capital,
                'establishment_date': establishment_date,
                'business_status': business_status,
                'industry': industry,
                'source': 'qichacha',
                'search_keyword': keyword,
                'company_url': urljoin('https://www.qcc.com', company_url) if company_url else '',
                'data_type': 'enterprise_info',
                'description': content[:200] + '...' if len(content) > 200 else content
            }

        except Exception as e:
            self.logger.debug(f"提取企查查企业信息出错: {e}")
            return None

    def _extract_tianyancha_company_info(self, company_data: Dict, keyword: str, industry: str) -> Optional[Dict]:
        """提取天眼查企业信息（JSON格式）"""
        try:
            company_name = company_data.get('name', '')
            if not company_name:
                return None

            # 过滤无效结果
            if not self._is_valid_enterprise_result(company_name, str(company_data), industry):
                return None

            return {
                'company_name': company_name,
                'legal_person': company_data.get('legalPersonName', ''),
                'registered_capital': company_data.get('regCapital', ''),
                'establishment_date': company_data.get('estiblishTime', ''),
                'business_status': company_data.get('regStatus', ''),
                'industry': industry,
                'source': 'tianyancha',
                'search_keyword': keyword,
                'company_url': f"https://www.tianyancha.com/company/{company_data.get('id', '')}",
                'data_type': 'enterprise_info',
                'description': company_data.get('businessScope', '')[:200]
            }

        except Exception as e:
            self.logger.debug(f"提取天眼查企业信息出错: {e}")
            return None

    def _extract_tianyancha_html_info(self, item_element, keyword: str, industry: str) -> Optional[Dict]:
        """提取天眼查企业信息（HTML格式）"""
        try:
            # 提取公司名称
            name_elem = item_element.find(['h3', 'h4', 'a'], class_=re.compile(r'(name|title|company)'))
            if not name_elem:
                name_elem = item_element.find('a')

            if not name_elem:
                return None

            company_name = name_elem.get_text().strip()
            company_url = name_elem.get('href', '') if name_elem.name == 'a' else ''

            # 提取其他信息
            content = item_element.get_text()

            # 过滤无效结果
            if not self._is_valid_enterprise_result(company_name, content, industry):
                return None

            return {
                'company_name': company_name,
                'legal_person': self._extract_field_from_content(content, '法定代表人'),
                'registered_capital': self._extract_field_from_content(content, '注册资本'),
                'establishment_date': self._extract_field_from_content(content, '成立日期'),
                'business_status': self._extract_field_from_content(content, '经营状态'),
                'industry': industry,
                'source': 'tianyancha',
                'search_keyword': keyword,
                'company_url': urljoin('https://www.tianyancha.com', company_url) if company_url else '',
                'data_type': 'enterprise_info',
                'description': content[:200] + '...' if len(content) > 200 else content
            }

        except Exception as e:
            self.logger.debug(f"提取天眼查HTML企业信息出错: {e}")
            return None

    def _extract_dingding_company_info(self, item_element, keyword: str, industry: str) -> Optional[Dict]:
        """提取钉钉企典企业信息"""
        try:
            # 提取公司名称
            name_elem = item_element.find(['h3', 'h4', 'a'], class_=re.compile(r'(name|title|company)'))
            if not name_elem:
                name_elem = item_element.find('a')

            if not name_elem:
                return None

            company_name = name_elem.get_text().strip()
            company_url = name_elem.get('href', '') if name_elem.name == 'a' else ''

            # 提取其他信息
            content = item_element.get_text()

            # 过滤无效结果
            if not self._is_valid_enterprise_result(company_name, content, industry):
                return None

            return {
                'company_name': company_name,
                'legal_person': self._extract_field_from_content(content, '法定代表人'),
                'registered_capital': self._extract_field_from_content(content, '注册资本'),
                'establishment_date': self._extract_field_from_content(content, '成立日期'),
                'business_status': self._extract_field_from_content(content, '经营状态'),
                'industry': industry,
                'source': 'dingding',
                'search_keyword': keyword,
                'company_url': urljoin('https://aiqicha.baidu.com', company_url) if company_url else '',
                'data_type': 'enterprise_info',
                'description': content[:200] + '...' if len(content) > 200 else content
            }

        except Exception as e:
            self.logger.debug(f"提取钉钉企典企业信息出错: {e}")
            return None

    def _extract_field_from_content(self, content: str, field_name: str) -> str:
        """从内容中提取指定字段"""
        pattern = rf'{field_name}[:：]\s*([^\s，。；\n]+)'
        match = re.search(pattern, content)
        return match.group(1) if match else ''

    def _is_valid_enterprise_result(self, company_name: str, content: str, industry: str) -> bool:
        """验证企业结果是否有效"""
        # 检查公司名称长度
        if not company_name or len(company_name) < 3:
            return False

        # 必须包含公司相关词汇
        company_keywords = ['公司', '企业', '集团', '有限', '股份', '科技', '实业', '工厂', '厂']
        if not any(keyword in company_name for keyword in company_keywords):
            return False

        # 排除无关结果
        exclude_keywords = ['招聘', '求职', '新闻', '百科', '学校', '教育', '医院', '政府']
        if any(keyword in company_name for keyword in exclude_keywords):
            return False

        # 检查是否与目标行业相关
        text = (company_name + " " + content).lower()
        if industry.lower() not in text:
            return False

        return True

    def _parse_enterprise_with_regex(self, html_content: str, keyword: str, industry: str, source: str) -> List[Dict]:
        """使用正则表达式解析企业信息"""
        results = []

        # 提取公司名称的正则模式
        company_patterns = [
            r'([^<>]*(?:' + keyword + r')[^<>]*(?:公司|企业|集团|有限|股份|科技|实业))',
            r'([^<>]*(?:公司|企业|集团|有限|股份|科技|实业)[^<>]*(?:' + keyword + r')[^<>]*)',
            r'title="([^"]*(?:' + keyword + r')[^"]*(?:公司|企业|集团|有限|股份|科技|实业)[^"]*)"',
            r'>([^<]*(?:' + keyword + r')[^<]*(?:公司|企业|集团|有限|股份|科技|实业)[^<]*)<'
        ]

        found_companies = set()

        for pattern in company_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                company_name = self._clean_text(match)
                if (self._is_valid_enterprise_result(company_name, '', industry) and
                    company_name not in found_companies):

                    found_companies.add(company_name)

                    company_info = {
                        'company_name': company_name,
                        'legal_person': '',
                        'registered_capital': '',
                        'establishment_date': '',
                        'business_status': '',
                        'industry': industry,
                        'source': source,
                        'search_keyword': keyword,
                        'company_url': '',
                        'data_type': 'enterprise_info',
                        'description': f'{industry}相关企业'
                    }

                    results.append(company_info)

                    if len(results) >= 10:  # 限制结果数量
                        break

        return results

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\-\(\)（）]', '', text)

        return text.strip()


