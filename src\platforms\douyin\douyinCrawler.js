const BrowserManager = require('../../config/browserConfig');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

class DouyinCrawler {
    constructor() {
        this.browserManager = new BrowserManager();
        this.baseUrl = 'https://www.douyin.com';
        this.apiBaseUrl = 'https://www.douyin.com/aweme/v1';
    }

    async extractUserInfo(userUrl) {
        const browser = await this.browserManager.createBrowser();
        const page = await this.browserManager.createPage(browser);

        try {
            console.log(`🔍 正在访问抖音用户页面: ${userUrl}`);
            
            // 访问用户主页
            await page.goto(userUrl, { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });

            // 等待页面加载
            await page.waitForTimeout(3000);

            // 提取用户基本信息
            const userInfo = await page.evaluate(() => {
                const userNameEl = document.querySelector('[data-e2e="user-title"]');
                const userDescEl = document.querySelector('[data-e2e="user-desc"]');
                const followersEl = document.querySelector('[data-e2e="followers-count"]');
                const followingEl = document.querySelector('[data-e2e="following-count"]');
                const likesEl = document.querySelector('[data-e2e="likes-count"]');

                return {
                    username: userNameEl?.textContent?.trim() || '',
                    description: userDescEl?.textContent?.trim() || '',
                    followers: followersEl?.textContent?.trim() || '0',
                    following: followingEl?.textContent?.trim() || '0',
                    likes: likesEl?.textContent?.trim() || '0'
                };
            });

            console.log(`✅ 用户信息提取成功: ${userInfo.username}`);

            // 滚动加载更多视频
            await this.scrollToLoadVideos(page);

            // 提取视频列表
            const videos = await this.extractVideoList(page);

            console.log(`✅ 提取到 ${videos.length} 个视频`);

            return {
                userInfo,
                videos,
                crawlTime: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ 抖音数据提取失败:', error.message);
            throw error;
        } finally {
            await this.browserManager.closeBrowser(browser);
        }
    }

    async scrollToLoadVideos(page, maxScrolls = 5) {
        console.log('📜 开始滚动加载更多视频...');
        
        for (let i = 0; i < maxScrolls; i++) {
            await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            
            await page.waitForTimeout(2000);
            
            // 检查是否有新内容加载
            const videoCount = await page.$$eval('[data-e2e="user-post-item"]', els => els.length);
            console.log(`第 ${i + 1} 次滚动，当前视频数: ${videoCount}`);
        }
    }

    async extractVideoList(page) {
        return await page.evaluate(() => {
            const videoElements = document.querySelectorAll('[data-e2e="user-post-item"]');
            const videos = [];

            videoElements.forEach((element, index) => {
                try {
                    const linkEl = element.querySelector('a');
                    const imgEl = element.querySelector('img');
                    const titleEl = element.querySelector('[data-e2e="video-title"]');
                    const statsEl = element.querySelector('.video-count');

                    const video = {
                        id: index + 1,
                        title: titleEl?.textContent?.trim() || imgEl?.alt || `视频${index + 1}`,
                        url: linkEl?.href || '',
                        cover: imgEl?.src || '',
                        stats: statsEl?.textContent?.trim() || '',
                        extractTime: new Date().toISOString()
                    };

                    if (video.url) {
                        videos.push(video);
                    }
                } catch (error) {
                    console.warn(`视频 ${index + 1} 提取失败:`, error.message);
                }
            });

            return videos;
        });
    }

    async interceptApiRequests(page) {
        const apiData = [];
        
        page.on('response', async (response) => {
            const url = response.url();
            
            // 拦截抖音API请求
            if (url.includes('/aweme/v1/') || url.includes('/web/api/')) {
                try {
                    const data = await response.json();
                    apiData.push({
                        url,
                        data,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    // 忽略非JSON响应
                }
            }
        });

        return apiData;
    }

    async saveData(data, filename) {
        const dataDir = path.join(__dirname, '../../../data/douyin');
        await fs.mkdir(dataDir, { recursive: true });
        
        const filepath = path.join(dataDir, `${filename}.json`);
        await fs.writeFile(filepath, JSON.stringify(data, null, 2), 'utf8');
        
        console.log(`💾 数据已保存到: ${filepath}`);
        return filepath;
    }

    async crawlUser(userUrl, options = {}) {
        try {
            const data = await this.extractUserInfo(userUrl);
            
            // 生成文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `douyin_user_${timestamp}`;
            
            // 保存数据
            await this.saveData(data, filename);
            
            return {
                success: true,
                data,
                message: `成功爬取抖音用户数据，共 ${data.videos.length} 个视频`
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: '抖音数据爬取失败'
            };
        }
    }
}

module.exports = DouyinCrawler;
