@echo off
chcp 65001 >nul
title 智能客户信息收集系统

echo.
echo 🕷️ 智能客户信息收集系统
echo ================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测通过

REM 检查是否已安装依赖
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo.
    echo 📦 首次运行，正在安装依赖包...
    python install.py
    if errorlevel 1 (
        echo ❌ 安装失败
        pause
        exit /b 1
    )
)

echo.
echo 🚀 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:8501
echo ⏹️  按 Ctrl+C 停止服务
echo.

REM 启动应用
python run.py

pause
