#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人工行为模拟模块
提供鼠标移动、滚动、点击等人工行为模拟功能，使爬虫行为更加自然
"""

import asyncio
import random
import math
import logging
from typing import List, Tuple, Optional, Dict
from playwright.async_api import Page, Locator
from dataclasses import dataclass

@dataclass
class MousePath:
    """鼠标路径数据类"""
    points: List[Tuple[float, float]]
    duration: float
    
@dataclass
class ScrollAction:
    """滚动动作数据类"""
    delta_x: float
    delta_y: float
    duration: float

class BehaviorSimulator:
    """人工行为模拟器"""
    
    def __init__(self):
        """初始化行为模拟器"""
        self.logger = logging.getLogger(__name__)
        
        # 行为参数配置
        self.mouse_speed_range = (50, 200)  # 像素/秒
        self.click_delay_range = (0.1, 0.3)  # 秒
        self.scroll_delay_range = (0.5, 2.0)  # 秒
        self.typing_speed_range = (80, 150)  # 字符/分钟
        self.pause_probability = 0.3  # 随机暂停概率
        self.pause_duration_range = (0.5, 2.0)  # 暂停时长范围
    
    async def human_like_click(self, page: Page, selector: str, offset: Tuple[float, float] = None) -> bool:
        """
        人性化点击操作
        
        Args:
            page: 页面对象
            selector: 元素选择器
            offset: 点击偏移量 (x, y)，相对于元素中心
            
        Returns:
            是否点击成功
        """
        try:
            # 等待元素可见
            await page.wait_for_selector(selector, state="visible", timeout=10000)
            element = page.locator(selector)
            
            # 获取元素位置和大小
            box = await element.bounding_box()
            if not box:
                self.logger.warning(f"⚠️ 无法获取元素边界: {selector}")
                return False
            
            # 计算点击位置
            if offset:
                click_x = box['x'] + box['width'] / 2 + offset[0]
                click_y = box['y'] + box['height'] / 2 + offset[1]
            else:
                # 在元素内随机选择点击位置（避免正中心）
                margin = 5  # 边距
                click_x = box['x'] + margin + random.random() * (box['width'] - 2 * margin)
                click_y = box['y'] + margin + random.random() * (box['height'] - 2 * margin)
            
            # 移动鼠标到目标位置
            await self.human_like_mouse_move(page, click_x, click_y)
            
            # 随机暂停
            if random.random() < self.pause_probability:
                pause_duration = random.uniform(*self.pause_duration_range)
                await asyncio.sleep(pause_duration)
            
            # 执行点击
            click_delay = random.uniform(*self.click_delay_range)
            await asyncio.sleep(click_delay)
            await page.mouse.click(click_x, click_y)
            
            self.logger.debug(f"✅ 人性化点击成功: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 人性化点击失败: {selector} - {e}")
            return False
    
    async def human_like_mouse_move(self, page: Page, target_x: float, target_y: float) -> None:
        """
        人性化鼠标移动
        
        Args:
            page: 页面对象
            target_x: 目标X坐标
            target_y: 目标Y坐标
        """
        try:
            # 获取当前鼠标位置（模拟）
            current_x = random.uniform(100, 800)
            current_y = random.uniform(100, 600)
            
            # 生成贝塞尔曲线路径
            path = self._generate_bezier_path(current_x, current_y, target_x, target_y)
            
            # 沿路径移动鼠标
            for i, (x, y) in enumerate(path.points):
                if i == 0:
                    continue
                
                # 计算移动时间
                step_duration = path.duration / len(path.points)
                await asyncio.sleep(step_duration)
                
                # 移动鼠标
                await page.mouse.move(x, y)
            
            self.logger.debug(f"🖱️ 鼠标移动到: ({target_x:.1f}, {target_y:.1f})")
            
        except Exception as e:
            self.logger.error(f"❌ 鼠标移动失败: {e}")
    
    def _generate_bezier_path(self, start_x: float, start_y: float, end_x: float, end_y: float) -> MousePath:
        """
        生成贝塞尔曲线鼠标路径
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            
        Returns:
            鼠标路径对象
        """
        # 计算距离和基础移动时间
        distance = math.sqrt((end_x - start_x) ** 2 + (end_y - start_y) ** 2)
        base_speed = random.uniform(*self.mouse_speed_range)
        duration = distance / base_speed
        
        # 生成控制点（创建自然的曲线）
        mid_x = (start_x + end_x) / 2
        mid_y = (start_y + end_y) / 2
        
        # 添加随机偏移创建曲线
        offset_range = min(distance * 0.2, 50)
        control1_x = mid_x + random.uniform(-offset_range, offset_range)
        control1_y = mid_y + random.uniform(-offset_range, offset_range)
        control2_x = mid_x + random.uniform(-offset_range, offset_range)
        control2_y = mid_y + random.uniform(-offset_range, offset_range)
        
        # 生成贝塞尔曲线点
        points = []
        steps = max(int(distance / 10), 10)  # 根据距离调整步数
        
        for i in range(steps + 1):
            t = i / steps
            
            # 三次贝塞尔曲线公式
            x = (1 - t) ** 3 * start_x + 3 * (1 - t) ** 2 * t * control1_x + \
                3 * (1 - t) * t ** 2 * control2_x + t ** 3 * end_x
            y = (1 - t) ** 3 * start_y + 3 * (1 - t) ** 2 * t * control1_y + \
                3 * (1 - t) * t ** 2 * control2_y + t ** 3 * end_y
            
            points.append((x, y))
        
        return MousePath(points=points, duration=duration)
    
    async def human_like_scroll(self, page: Page, direction: str = "down", amount: int = None) -> None:
        """
        人性化滚动操作
        
        Args:
            page: 页面对象
            direction: 滚动方向 ("up", "down", "left", "right")
            amount: 滚动量（像素），如果为None则使用随机值
        """
        try:
            if amount is None:
                amount = random.randint(200, 800)
            
            # 确定滚动方向
            delta_x, delta_y = 0, 0
            if direction == "down":
                delta_y = amount
            elif direction == "up":
                delta_y = -amount
            elif direction == "right":
                delta_x = amount
            elif direction == "left":
                delta_x = -amount
            
            # 分段滚动，模拟真实滚动行为
            segments = random.randint(3, 8)
            segment_delta_x = delta_x / segments
            segment_delta_y = delta_y / segments
            
            for i in range(segments):
                # 随机滚动延迟
                delay = random.uniform(0.05, 0.15)
                await asyncio.sleep(delay)
                
                # 执行滚动
                await page.mouse.wheel(segment_delta_x, segment_delta_y)
            
            # 滚动后随机暂停
            scroll_delay = random.uniform(*self.scroll_delay_range)
            await asyncio.sleep(scroll_delay)
            
            self.logger.debug(f"📜 人性化滚动: {direction} {amount}px")
            
        except Exception as e:
            self.logger.error(f"❌ 滚动操作失败: {e}")
    
    async def human_like_typing(self, page: Page, selector: str, text: str, clear_first: bool = True) -> bool:
        """
        人性化输入操作
        
        Args:
            page: 页面对象
            selector: 输入框选择器
            text: 要输入的文本
            clear_first: 是否先清空输入框
            
        Returns:
            是否输入成功
        """
        try:
            # 点击输入框
            if not await self.human_like_click(page, selector):
                return False
            
            # 清空输入框
            if clear_first:
                await page.keyboard.press("Control+A")
                await asyncio.sleep(0.1)
                await page.keyboard.press("Delete")
                await asyncio.sleep(0.2)
            
            # 计算输入速度
            chars_per_minute = random.uniform(*self.typing_speed_range)
            char_delay = 60 / chars_per_minute
            
            # 逐字符输入
            for i, char in enumerate(text):
                await page.keyboard.type(char)
                
                # 随机输入延迟
                delay = char_delay + random.uniform(-char_delay * 0.3, char_delay * 0.3)
                await asyncio.sleep(delay)
                
                # 偶尔暂停（模拟思考）
                if i > 0 and i % random.randint(5, 15) == 0:
                    if random.random() < 0.3:
                        pause = random.uniform(0.5, 1.5)
                        await asyncio.sleep(pause)
            
            self.logger.debug(f"⌨️ 人性化输入完成: {text[:20]}...")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 输入操作失败: {selector} - {e}")
            return False
    
    async def random_mouse_movement(self, page: Page, duration: float = 2.0) -> None:
        """
        随机鼠标移动（模拟用户浏览行为）
        
        Args:
            page: 页面对象
            duration: 移动持续时间（秒）
        """
        try:
            # 获取页面尺寸
            viewport = page.viewport_size
            if not viewport:
                viewport = {"width": 1920, "height": 1080}
            
            start_time = asyncio.get_event_loop().time()
            
            while asyncio.get_event_loop().time() - start_time < duration:
                # 生成随机目标位置
                target_x = random.uniform(100, viewport["width"] - 100)
                target_y = random.uniform(100, viewport["height"] - 100)
                
                # 移动鼠标
                await self.human_like_mouse_move(page, target_x, target_y)
                
                # 随机暂停
                pause = random.uniform(0.5, 1.5)
                await asyncio.sleep(pause)
            
            self.logger.debug(f"🎯 随机鼠标移动完成: {duration}s")
            
        except Exception as e:
            self.logger.error(f"❌ 随机鼠标移动失败: {e}")
    
    async def simulate_reading_behavior(self, page: Page, reading_time: float = None) -> None:
        """
        模拟阅读行为
        
        Args:
            page: 页面对象
            reading_time: 阅读时间（秒），如果为None则随机生成
        """
        try:
            if reading_time is None:
                reading_time = random.uniform(3.0, 10.0)
            
            # 模拟阅读过程中的滚动
            scroll_count = random.randint(2, 5)
            scroll_interval = reading_time / scroll_count
            
            for i in range(scroll_count):
                # 随机滚动
                if random.random() < 0.7:  # 70%概率向下滚动
                    await self.human_like_scroll(page, "down", random.randint(100, 300))
                else:
                    await self.human_like_scroll(page, "up", random.randint(50, 150))
                
                # 等待间隔
                await asyncio.sleep(scroll_interval + random.uniform(-1.0, 1.0))
            
            self.logger.debug(f"📖 模拟阅读行为完成: {reading_time:.1f}s")
            
        except Exception as e:
            self.logger.error(f"❌ 模拟阅读行为失败: {e}")
    
    async def wait_with_random_activity(self, page: Page, base_wait_time: float) -> None:
        """
        带随机活动的等待
        
        Args:
            page: 页面对象
            base_wait_time: 基础等待时间（秒）
        """
        try:
            # 添加随机变化
            actual_wait_time = base_wait_time + random.uniform(-base_wait_time * 0.2, base_wait_time * 0.3)
            
            # 在等待期间进行随机活动
            if actual_wait_time > 3.0:
                # 长等待时进行随机鼠标移动
                activity_time = min(actual_wait_time * 0.3, 2.0)
                await self.random_mouse_movement(page, activity_time)
                remaining_time = actual_wait_time - activity_time
                await asyncio.sleep(remaining_time)
            else:
                # 短等待时直接等待
                await asyncio.sleep(actual_wait_time)
            
            self.logger.debug(f"⏱️ 带活动等待完成: {actual_wait_time:.1f}s")
            
        except Exception as e:
            self.logger.error(f"❌ 等待活动失败: {e}")

# 全局行为模拟器实例
_behavior_simulator: Optional[BehaviorSimulator] = None

def get_behavior_simulator() -> BehaviorSimulator:
    """
    获取全局行为模拟器实例
    
    Returns:
        行为模拟器实例
    """
    global _behavior_simulator
    if _behavior_simulator is None:
        _behavior_simulator = BehaviorSimulator()
    return _behavior_simulator
