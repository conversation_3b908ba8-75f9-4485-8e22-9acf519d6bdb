#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据爬取系统 - 爬虫管理器
"""

import asyncio
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from douyin_crawler import <PERSON>uyinCrawler
from kuaishou_crawler import KuaishouCrawler
from qichacha_crawler import QichachaCrawler

class CrawlerManager:
    """爬虫管理器 - 统一管理所有平台的爬虫"""
    
    def __init__(self):
        self.crawlers = {
            'douyin': DouyinCrawler,
            'kuaishou': <PERSON><PERSON>houCrawler,
            'qichacha': QichachaCrawler
        }
        
        # 创建数据目录
        self.data_dir = Path("data")
        self.reports_dir = self.data_dir / "reports"
        self.industry_dir = self.data_dir / "industry"
        
        for dir_path in [self.data_dir, self.reports_dir, self.industry_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        print("🚀 真实数据爬取系统管理器初始化完成")

    async def test_all_platforms(self) -> Dict[str, bool]:
        """测试所有平台的连通性"""
        results = {}
        
        print("🧪 开始测试所有平台...")
        
        for platform_name, crawler_class in self.crawlers.items():
            print(f"\n📡 测试 {platform_name.upper()} 平台...")
            
            try:
                crawler = crawler_class()
                
                # 创建页面测试连通性
                page = await crawler.create_page()
                
                if platform_name == 'douyin':
                    await page.goto('https://www.douyin.com', wait_until='networkidle')
                elif platform_name == 'kuaishou':
                    await page.goto('https://www.kuaishou.com', wait_until='networkidle')
                elif platform_name == 'qichacha':
                    await page.goto('https://www.qcc.com', wait_until='networkidle')
                
                await crawler.wait_random(2, 3)
                
                # 检查页面标题
                title = await page.title()
                
                if title and len(title) > 0:
                    print(f"✅ {platform_name.upper()} 连接成功 - {title}")
                    results[platform_name] = True
                else:
                    print(f"❌ {platform_name.upper()} 连接失败 - 无法获取页面标题")
                    results[platform_name] = False
                
                await crawler.close()
                
            except Exception as e:
                print(f"❌ {platform_name.upper()} 测试失败: {str(e)}")
                results[platform_name] = False
        
        # 保存测试结果
        self._save_test_results(results)
        
        return results

    def _save_test_results(self, results: Dict[str, bool]):
        """保存测试结果"""
        test_report = {
            'test_time': datetime.now().isoformat(),
            'results': results,
            'summary': {
                'total_platforms': len(results),
                'successful': sum(results.values()),
                'failed': len(results) - sum(results.values())
            }
        }
        
        report_file = self.reports_dir / f"platform_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(test_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 测试报告已保存: {report_file}")

    async def crawl_douyin_user(self, user_url: str) -> Dict:
        """爬取抖音用户"""
        print(f"🎵 开始爬取抖音用户: {user_url}")
        
        crawler = DouyinCrawler()
        try:
            result = await crawler.crawl_user_profile(user_url)
            return result
        finally:
            await crawler.close()

    async def crawl_kuaishou_user(self, user_url: str) -> Dict:
        """爬取快手用户"""
        print(f"⚡ 开始爬取快手用户: {user_url}")
        
        crawler = KuaishouCrawler()
        try:
            result = await crawler.crawl_user_profile(user_url)
            return result
        finally:
            await crawler.close()

    async def crawl_company_info(self, company_name: str) -> Dict:
        """爬取企业信息"""
        print(f"🏢 开始爬取企业信息: {company_name}")
        
        crawler = QichachaCrawler()
        try:
            result = await crawler.crawl_company_info(company_name)
            return result
        finally:
            await crawler.close()

    async def crawl_by_industry(self, industry: str, region: str = '') -> Dict:
        """按行业爬取数据"""
        print(f"🏭 开始按行业爬取数据: {industry} {region}")
        
        # 生成行业相关的关键词
        keywords = self._generate_industry_keywords(industry, region)
        
        results = {
            'industry': industry,
            'region': region,
            'keywords': keywords,
            'crawl_time': datetime.now().isoformat(),
            'data': {
                'companies': [],
                'douyin_users': [],
                'kuaishou_users': []
            }
        }
        
        # 1. 搜索相关企业
        print("🔍 搜索相关企业...")
        for keyword in keywords[:3]:  # 限制关键词数量
            try:
                crawler = QichachaCrawler()
                company_url = await crawler.search_company(keyword)
                if company_url:
                    company_info = await crawler.crawl_company_info(keyword)
                    results['data']['companies'].append(company_info)
                await crawler.close()
                await asyncio.sleep(3)
            except Exception as e:
                print(f"⚠️ 企业搜索失败: {keyword} - {str(e)}")
        
        # 2. 搜索抖音相关用户
        print("🎵 搜索抖音相关用户...")
        try:
            crawler = DouyinCrawler()
            for keyword in keywords[:2]:
                users = await crawler.search_users(keyword, limit=5)
                results['data']['douyin_users'].extend(users)
                await asyncio.sleep(2)
            await crawler.close()
        except Exception as e:
            print(f"⚠️ 抖音搜索失败: {str(e)}")
        
        # 3. 搜索快手相关用户
        print("⚡ 搜索快手相关用户...")
        try:
            crawler = KuaishouCrawler()
            for keyword in keywords[:2]:
                users = await crawler.search_users(keyword, limit=5)
                results['data']['kuaishou_users'].extend(users)
                await asyncio.sleep(2)
            await crawler.close()
        except Exception as e:
            print(f"⚠️ 快手搜索失败: {str(e)}")
        
        # 保存行业数据
        self._save_industry_data(results)
        
        return results

    def _generate_industry_keywords(self, industry: str, region: str = '') -> List[str]:
        """生成行业相关关键词"""
        base_keywords = [industry]
        
        # 添加地区前缀
        if region:
            base_keywords.append(f"{region}{industry}")
        
        # 根据行业类型添加相关词汇
        industry_extensions = {
            '酒店': ['酒店管理', '酒店建设', '酒店装修', '酒店设计', '酒店工程'],
            '建设': ['建筑工程', '工程建设', '建设集团', '建筑公司', '工程公司'],
            '装修': ['装饰装修', '室内设计', '装修公司', '装饰工程', '家装'],
            '餐饮': ['餐厅', '美食', '餐饮管理', '食品', '厨师'],
            '教育': ['培训', '学校', '教育机构', '在线教育', '培训机构'],
            '科技': ['软件', '互联网', '人工智能', 'AI', '科技公司'],
            '医疗': ['医院', '诊所', '医疗器械', '药品', '健康'],
            '金融': ['银行', '保险', '投资', '理财', '金融服务']
        }
        
        # 查找匹配的行业扩展词汇
        for key, extensions in industry_extensions.items():
            if key in industry:
                base_keywords.extend(extensions[:3])
                break
        
        return base_keywords[:10]  # 限制关键词数量

    def _save_industry_data(self, data: Dict):
        """保存行业数据"""
        safe_industry = data['industry'].replace('/', '_').replace('\\', '_')
        safe_region = data.get('region', '').replace('/', '_').replace('\\', '_')
        
        filename = f"industry_{safe_industry}"
        if safe_region:
            filename += f"_{safe_region}"
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filepath = self.industry_dir / f"{filename}_{timestamp}.json"
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 行业数据已保存: {filepath}")

    async def generate_report(self) -> Dict:
        """生成爬取报告"""
        print("📊 正在生成爬取报告...")
        
        report = {
            'generate_time': datetime.now().isoformat(),
            'data_summary': {},
            'file_statistics': {},
            'platform_statistics': {}
        }
        
        # 统计各平台数据文件
        for platform in ['douyin', 'kuaishou', 'qichacha']:
            platform_dir = self.data_dir / platform
            if platform_dir.exists():
                files = list(platform_dir.glob('*.json'))
                report['platform_statistics'][platform] = {
                    'total_files': len(files),
                    'latest_file': max(files, key=os.path.getctime).name if files else None
                }
        
        # 统计行业数据
        industry_files = list(self.industry_dir.glob('*.json'))
        report['file_statistics']['industry_reports'] = len(industry_files)
        
        # 统计总数据量
        total_files = 0
        for platform_dir in [self.data_dir / p for p in ['douyin', 'kuaishou', 'qichacha']]:
            if platform_dir.exists():
                total_files += len(list(platform_dir.glob('*.json')))
        
        report['data_summary']['total_data_files'] = total_files
        report['data_summary']['total_industry_reports'] = len(industry_files)
        
        # 保存报告
        report_file = self.reports_dir / f"crawl_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📋 报告已生成: {report_file}")
        return report

    def get_data_summary(self) -> Dict:
        """获取数据摘要"""
        summary = {
            'platforms': {},
            'total_files': 0,
            'latest_crawl': None
        }
        
        for platform in ['douyin', 'kuaishou', 'qichacha']:
            platform_dir = self.data_dir / platform
            if platform_dir.exists():
                files = list(platform_dir.glob('*.json'))
                summary['platforms'][platform] = len(files)
                summary['total_files'] += len(files)
                
                if files:
                    latest_file = max(files, key=os.path.getctime)
                    file_time = datetime.fromtimestamp(os.path.getctime(latest_file))
                    
                    if not summary['latest_crawl'] or file_time > summary['latest_crawl']:
                        summary['latest_crawl'] = file_time
        
        return summary
