#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
社交媒体平台爬虫模块
专注于抖音、快手等短视频平台的企业信息爬取
"""

import requests
import time
import random
import re
import json
import pandas as pd
from urllib.parse import urljoin, urlparse, quote
from typing import List, Dict, Any, Optional
import yaml
from pathlib import Path
from datetime import datetime, timedelta

try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None

try:
    from fake_useragent import UserAgent
except ImportError:
    UserAgent = None

from .utils.logger import get_logger
from .region_manager import RegionManager

class SocialMediaCrawler:
    """社交媒体平台爬虫类"""
    
    def __init__(self, config_path: str = "social_media_config.yaml"):
        """初始化社交媒体爬虫"""
        self.logger = get_logger(__name__)
        self.config = self._load_config(config_path)
        self.ua = UserAgent() if UserAgent else None
        self.session = requests.Session()
        self.region_manager = RegionManager()
        
        # 设置请求头
        self._setup_session()
        
    def _load_config(self, config_path: str) -> Dict:
        """加载社交媒体平台配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载社交媒体配置失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'platforms': {
                'douyin': {
                    'enabled': True,
                    'name': '抖音',
                    'search_api': 'https://www.douyin.com/aweme/v1/web/general/search/',
                    'user_api': 'https://www.douyin.com/aweme/v1/web/aweme/profile/',
                    'headers': {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'https://www.douyin.com/',
                        'Accept': 'application/json, text/plain, */*'
                    }
                },
                'kuaishou': {
                    'enabled': True,
                    'name': '快手',
                    'search_api': 'https://www.kuaishou.com/graphql',
                    'user_api': 'https://www.kuaishou.com/profile/',
                    'headers': {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'https://www.kuaishou.com/',
                        'Accept': 'application/json'
                    }
                }
            },
            'search_keywords': {
                'business_types': [
                    '{industry} 企业',
                    '{industry} 公司',
                    '{industry} 商家',
                    '{industry} 品牌',
                    '{industry} 官方'
                ],
                'location_keywords': [
                    '{region} {industry}',
                    '{province} {industry}',
                    '{city} {industry}'
                ]
            },
            'filtering_rules': {
                'min_followers': 1000,  # 最少粉丝数
                'business_indicators': [
                    '公司', '企业', '集团', '有限', '股份',
                    '官方', '品牌', '商家', '厂家', '供应商'
                ],
                'exclude_keywords': [
                    '个人', '网红', '达人', '博主', '主播',
                    '娱乐', '搞笑', '段子', '美食', '旅游'
                ]
            }
        }
    
    def _setup_session(self):
        """设置会话请求头"""
        headers = {
            'User-Agent': self.ua.random if self.ua else 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }
        self.session.headers.update(headers)
    
    def search_social_media_platforms(self, industry: str, province: str = None, city: str = None, 
                                    platforms: List[str] = None) -> pd.DataFrame:
        """搜索社交媒体平台"""
        if not platforms:
            platforms = ['douyin', 'kuaishou']
        
        all_results = []
        
        for platform in platforms:
            if platform not in self.config.get('platforms', {}):
                self.logger.warning(f"不支持的平台: {platform}")
                continue
                
            platform_config = self.config['platforms'][platform]
            if not platform_config.get('enabled', False):
                self.logger.info(f"平台 {platform} 已禁用")
                continue
            
            self.logger.info(f"开始搜索 {platform_config['name']} 平台")
            
            if platform == 'douyin':
                results = self._search_douyin(industry, province, city)
            elif platform == 'kuaishou':
                results = self._search_kuaishou(industry, province, city)
            else:
                continue
            
            all_results.extend(results)
            
            # 避免请求过快
            time.sleep(random.uniform(3, 6))
        
        return pd.DataFrame(all_results)
    
    def _search_douyin(self, industry: str, province: str = None, city: str = None) -> List[Dict]:
        """搜索抖音平台"""
        results = []
        
        # 生成搜索关键词
        keywords = self._generate_search_keywords(industry, province, city)
        
        platform_config = self.config['platforms']['douyin']
        
        for keyword in keywords[:5]:  # 限制关键词数量
            try:
                self.logger.info(f"抖音搜索关键词: {keyword}")
                
                # 构建搜索参数
                search_params = {
                    'keyword': keyword,
                    'search_source': 'tab_search',
                    'type': 1,  # 用户搜索
                    'count': 20,
                    'cursor': 0
                }
                
                # 更新请求头
                headers = platform_config.get('headers', {})
                
                # 发送搜索请求
                response = self.session.get(
                    platform_config['search_api'],
                    params=search_params,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    # 解析搜索结果
                    search_results = self._parse_douyin_search_results(response.text, keyword, industry)
                    results.extend(search_results)
                    
                    self.logger.info(f"抖音关键词 '{keyword}': {len(search_results)} 条结果")
                else:
                    self.logger.warning(f"抖音搜索请求失败: {response.status_code}")
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                self.logger.error(f"抖音搜索出错: {e}")
                continue
        
        # 如果没有获取到真实数据，返回空结果
        if not results:
            self.logger.warning("抖音未获取到真实数据")
        
        return results
    
    def _search_kuaishou(self, industry: str, province: str = None, city: str = None) -> List[Dict]:
        """搜索快手平台"""
        results = []
        
        # 生成搜索关键词
        keywords = self._generate_search_keywords(industry, province, city)
        
        platform_config = self.config['platforms']['kuaishou']
        
        for keyword in keywords[:5]:  # 限制关键词数量
            try:
                self.logger.info(f"快手搜索关键词: {keyword}")
                
                # 快手使用GraphQL API
                query = {
                    "operationName": "visionSearchPhoto",
                    "variables": {
                        "keyword": keyword,
                        "pcursor": "",
                        "searchSessionId": "",
                        "page": "search"
                    },
                    "query": "fragment photoContent on PhotoEntity {...}"
                }
                
                # 更新请求头
                headers = platform_config.get('headers', {})
                headers['Content-Type'] = 'application/json'
                
                # 发送搜索请求
                response = self.session.post(
                    platform_config['search_api'],
                    json=query,
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    # 解析搜索结果
                    search_results = self._parse_kuaishou_search_results(response.text, keyword, industry)
                    results.extend(search_results)
                    
                    self.logger.info(f"快手关键词 '{keyword}': {len(search_results)} 条结果")
                else:
                    self.logger.warning(f"快手搜索请求失败: {response.status_code}")
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                self.logger.error(f"快手搜索出错: {e}")
                continue
        
        # 如果没有获取到真实数据，返回空结果
        if not results:
            self.logger.warning("快手未获取到真实数据")
        
        return results
    
    def _generate_search_keywords(self, industry: str, province: str = None, city: str = None) -> List[str]:
        """生成搜索关键词"""
        keywords = []
        
        # 获取区域信息
        region = self.region_manager.get_region_display_name(province, city)
        
        # 基础业务类型关键词
        business_keywords = self.config.get('search_keywords', {}).get('business_types', [])
        for keyword_template in business_keywords:
            keyword = keyword_template.format(industry=industry)
            keywords.append(keyword)
        
        # 地区相关关键词
        if province or city:
            location_keywords = self.config.get('search_keywords', {}).get('location_keywords', [])
            for keyword_template in location_keywords:
                keyword = keyword_template.format(
                    region=region,
                    province=province or '',
                    city=city or '',
                    industry=industry
                )
                keywords.append(keyword.strip())
        
        return keywords

    def _parse_douyin_search_results(self, response_text: str, keyword: str, industry: str) -> List[Dict]:
        """解析抖音搜索结果"""
        results = []

        try:
            # 尝试解析JSON响应
            data = json.loads(response_text)

            if 'user_list' in data:
                users = data['user_list']

                for user in users:
                    user_info = user.get('user_info', {})

                    # 检查是否为企业账号
                    if self._is_business_account(user_info, keyword):
                        account_info = {
                            'company_name': self._extract_company_name_from_social(user_info.get('nickname', '')),
                            'platform': '抖音',
                            'account_name': user_info.get('nickname', ''),
                            'account_id': user_info.get('uid', ''),
                            'followers_count': user_info.get('follower_count', 0),
                            'description': user_info.get('signature', ''),
                            'avatar_url': user_info.get('avatar_larger', {}).get('url_list', [''])[0],
                            'verification_info': user_info.get('custom_verify', ''),
                            'industry': industry,
                            'source': 'douyin',
                            'search_keyword': keyword,
                            'profile_url': f"https://www.douyin.com/user/{user_info.get('sec_uid', '')}",
                            'data_type': 'social_media'
                        }

                        # 提取联系信息
                        contact_info = self._extract_contact_from_description(user_info.get('signature', ''))
                        account_info.update(contact_info)

                        results.append(account_info)

        except json.JSONDecodeError:
            self.logger.warning("抖音响应不是有效的JSON格式")
        except Exception as e:
            self.logger.error(f"解析抖音搜索结果出错: {e}")

        return results

    def _parse_kuaishou_search_results(self, response_text: str, keyword: str, industry: str) -> List[Dict]:
        """解析快手搜索结果"""
        results = []

        try:
            # 尝试解析JSON响应
            data = json.loads(response_text)

            if 'data' in data and 'visionSearchPhoto' in data['data']:
                search_data = data['data']['visionSearchPhoto']

                if 'result' in search_data:
                    photos = search_data['result']

                    # 从视频中提取用户信息
                    seen_users = set()

                    for photo in photos:
                        user_info = photo.get('user', {})
                        user_id = user_info.get('user_id', '')

                        if user_id in seen_users:
                            continue
                        seen_users.add(user_id)

                        # 检查是否为企业账号
                        if self._is_business_account(user_info, keyword):
                            account_info = {
                                'company_name': self._extract_company_name_from_social(user_info.get('user_name', '')),
                                'platform': '快手',
                                'account_name': user_info.get('user_name', ''),
                                'account_id': user_id,
                                'followers_count': user_info.get('fan', 0),
                                'description': user_info.get('user_text', ''),
                                'avatar_url': user_info.get('head_url', ''),
                                'verification_info': user_info.get('user_type_str', ''),
                                'industry': industry,
                                'source': 'kuaishou',
                                'search_keyword': keyword,
                                'profile_url': f"https://www.kuaishou.com/profile/{user_id}",
                                'data_type': 'social_media'
                            }

                            # 提取联系信息
                            contact_info = self._extract_contact_from_description(user_info.get('user_text', ''))
                            account_info.update(contact_info)

                            results.append(account_info)

        except json.JSONDecodeError:
            self.logger.warning("快手响应不是有效的JSON格式")
        except Exception as e:
            self.logger.error(f"解析快手搜索结果出错: {e}")

        return results

    def _is_business_account(self, user_info: Dict, keyword: str) -> bool:
        """判断是否为企业账号"""
        # 检查粉丝数量
        followers = user_info.get('follower_count', user_info.get('fan', 0))
        min_followers = self.config.get('filtering_rules', {}).get('min_followers', 1000)

        if followers < min_followers:
            return False

        # 检查账号名称和描述
        name = user_info.get('nickname', user_info.get('user_name', '')).lower()
        description = user_info.get('signature', user_info.get('user_text', '')).lower()

        # 必须包含业务指示词
        business_indicators = self.config.get('filtering_rules', {}).get('business_indicators', [])
        has_business_indicator = any(
            indicator in name or indicator in description
            for indicator in business_indicators
        )

        if not has_business_indicator:
            return False

        # 排除个人账号关键词
        exclude_keywords = self.config.get('filtering_rules', {}).get('exclude_keywords', [])
        has_exclude_keyword = any(
            keyword in name or keyword in description
            for keyword in exclude_keywords
        )

        if has_exclude_keyword:
            return False

        # 检查是否与搜索关键词相关
        if keyword.lower() not in name and keyword.lower() not in description:
            return False

        return True

    def _extract_company_name_from_social(self, account_name: str) -> str:
        """从社交媒体账号名称中提取公司名称"""
        # 移除常见的社交媒体后缀
        name = re.sub(r'(官方|旗舰店|专卖店|直营店|总店|分店)$', '', account_name)

        # 提取公司名称模式
        patterns = [
            r'(.+?(?:公司|企业|集团|有限|股份|科技|实业))',
            r'(.+?(?:品牌|商城|商店|店铺))',
            r'^([^（(]+)',  # 括号前的内容
        ]

        for pattern in patterns:
            match = re.search(pattern, name)
            if match:
                return match.group(1).strip()

        return account_name.strip()

    def _extract_contact_from_description(self, description: str) -> Dict:
        """从描述中提取联系信息"""
        contact_info = {}

        if not description:
            return contact_info

        # 提取电话号码
        phone_patterns = [
            r'(?:电话|联系|Tel|TEL|手机|微信)[:：\s]*([0-9\-\s\(\)]{7,20})',
            r'(\d{3,4}[-\s]?\d{7,8})',  # 固定电话
            r'(1[3-9]\d{9})',  # 手机号码
        ]

        for pattern in phone_patterns:
            match = re.search(pattern, description)
            if match:
                contact_info['phone'] = match.group(1).strip()
                break

        # 提取邮箱
        email_pattern = r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
        email_match = re.search(email_pattern, description)
        if email_match:
            contact_info['email'] = email_match.group(1)

        # 提取微信号
        wechat_patterns = [
            r'(?:微信|WeChat|wechat)[:：\s]*([a-zA-Z0-9_-]{6,20})',
            r'VX[:：\s]*([a-zA-Z0-9_-]{6,20})'
        ]

        for pattern in wechat_patterns:
            match = re.search(pattern, description)
            if match:
                contact_info['wechat'] = match.group(1).strip()
                break

        # 提取地址信息
        address_patterns = [
            r'(?:地址|位置|Address)[:：\s]*([^，。；\n]{10,50})',
            r'([^，。；\n]*(?:省|市|区|县|街道|路|号)[^，。；\n]*)'
        ]

        for pattern in address_patterns:
            match = re.search(pattern, description)
            if match:
                contact_info['address'] = match.group(1).strip()
                break

        return contact_info


