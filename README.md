# 🕷️ 智能客户信息收集系统 v2.0

## 📋 项目简介

这是一个专业级的客户信息收集系统，由资深爬虫专家设计开发。系统能够根据输入的行业关键词和地理区域，自动从多个数据源收集相关企业信息，并提供强大的数据处理、可视化和导出功能。

### 🆕 v2.0 新功能
- **🌍 区域搜索**: 支持按省份、城市进行精准的区域化搜索
- **🗺️ 地图可视化**: 企业分布地图和区域统计图表
- **🎨 增强UI**: 更直观的用户界面和数据筛选功能
- **💾 智能导出**: 支持筛选后数据导出和多格式选择

## ✨ 核心特性

### 🎯 智能数据采集
- **多源数据收集**: 支持百度、必应等搜索引擎，以及企业目录网站
- **区域化搜索**: 根据选择的省份、城市自动生成区域化搜索关键词
- **智能关键词扩展**: 自动生成相关搜索词，提高收集覆盖率
- **反反爬机制**: 内置用户代理轮换、请求延时、重试机制等

### 🔧 专业数据处理
- **智能去重**: 精确去重 + 模糊去重，避免重复数据
- **数据标准化**: 公司名称、联系方式、网址等字段标准化
- **数据增强**: 自动推断公司规模、提取域名等信息
- **质量评估**: 计算数据完整度，确保数据质量

### 📊 强大可视化分析
- **多维度图表**: 行业分布、公司规模、数据完整度等
- **地图可视化**: 企业省份分布地图、城市分布地图
- **区域统计**: TOP省份、TOP城市统计图表
- **交互式仪表板**: 实时数据概览和分析
- **数据筛选**: 支持按公司名称、数据源、完整度筛选
- **智能导出**: 支持筛选后数据导出，多种格式选择

### 💾 可靠数据存储
- **SQLite数据库**: 轻量级、高性能的本地存储
- **历史记录**: 完整的爬取历史和统计信息
- **数据备份**: 自动备份机制，保障数据安全

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Windows/macOS/Linux

### 安装步骤

#### 方法一：增强版启动（推荐）

1. **克隆项目**
```bash
git clone <项目地址>
cd customer-info-collector
```

2. **快速启动**
```bash
# 使用增强版启动脚本（包含功能演示）
python run_enhanced.py

# 或者直接启动Web界面
python -m streamlit run main.py
```

3. **体验新功能**
```bash
# 运行区域搜索演示
python demo_enhanced.py
```

#### 方法二：传统安装

1. **一键安装**
```bash
# Windows用户
python setup_env.py

# 或者使用通用安装脚本
python install.py
```

2. **启动系统**
```bash
# Windows用户（推荐）
start.bat

# 或者手动启动
python run.py
```

#### 方法二：手动安装

1. **创建虚拟环境**
```bash
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate
```

2. **安装依赖**
```bash
# 标准安装（推荐）
pip install -r requirements.txt

# 或者最小安装（快速体验）
pip install -r requirements_minimal.txt

# 或者完整功能安装（包含OCR、AI等高级功能）
pip install -r requirements_complete.txt
```

3. **启动系统**
```bash
python run.py
```

4. **访问Web界面**
- 自动打开浏览器访问 http://localhost:8501
- 或手动在浏览器中输入上述地址

## 🐍 虚拟环境管理

### 为什么使用虚拟环境？
- **依赖隔离**: 避免不同项目间的包版本冲突
- **环境一致性**: 确保开发和生产环境一致
- **便于管理**: 独立管理项目依赖，便于部署

### 虚拟环境操作

#### 创建和激活
```bash
# 自动创建（推荐）
python setup_env.py

# 手动创建
python -m venv venv

# 激活环境
# Windows:
venv\Scripts\activate
# 或双击: activate_env.bat

# macOS/Linux:
source venv/bin/activate
# 或运行: source activate_env.sh
```

#### 包管理
```bash
# 安装新包
pip install package_name

# 卸载包
pip uninstall package_name

# 查看已安装包
pip list

# 导出依赖列表
pip freeze > requirements-lock.txt

# 从锁定文件安装（精确版本）
pip install -r requirements-lock.txt
```

#### 退出环境
```bash
deactivate
```

## 📖 使用指南

### 基本操作流程

1. **输入行业关键词**
   - 在左侧边栏输入目标行业，如"人工智能"、"新能源汽车"
   - 设置搜索深度（建议10-20页）

2. **🆕 选择搜索区域**
   - 选择目标省份（如"广东省"、"北京市"）
   - 选择具体城市（如"深圳市"、"广州市"）
   - 选择"全国"进行全国搜索

3. **配置数据源**
   - 选择要使用的搜索引擎（百度、必应）
   - 选择是否启用企业目录爬取

4. **开始收集**
   - 点击"🚀 开始收集"按钮
   - 系统将显示实时进度和状态

5. **🆕 数据筛选与分析**
   - 使用筛选功能按公司名称、数据源等条件筛选
   - 查看统计图表、地图分析、综合仪表板
   - 分析区域分布和数据完整度

6. **🆕 智能导出**
   - 选择导出筛选后的数据或全部数据
   - 支持CSV、Excel、JSON多种格式
   - 自动生成包含区域信息的文件名

### 高级功能

#### 数据筛选与搜索
- 支持按公司名称、行业等字段筛选
- 模糊搜索功能，快速定位目标企业

#### 历史记录管理
- 查看所有爬取历史
- 数据源效果统计
- 系统性能监控

#### 数据质量控制
- 自动数据验证和清洗
- 重复数据智能识别
- 数据完整度评分

## 🏗️ 系统架构

```
customer-info-collector/
├── main.py                 # 主程序入口
├── run.py                  # 启动脚本
├── config.yaml            # 配置文件
├── requirements.txt        # 依赖包列表
├── src/                    # 源代码目录
│   ├── crawler_engine.py   # 核心爬虫引擎
│   ├── data_processor.py   # 数据处理模块
│   ├── visualizer.py       # 可视化模块
│   ├── database_manager.py # 数据库管理
│   └── utils/              # 工具模块
│       ├── logger.py       # 日志管理
│       └── anti_detection.py # 反反爬工具
├── data/                   # 数据存储目录
├── logs/                   # 日志文件目录
└── README.md              # 项目说明
```

## ⚙️ 配置说明

### config.yaml 主要配置项

```yaml
# 爬虫配置
crawler:
  concurrent_requests: 16    # 并发请求数
  download_delay: 1         # 请求延时(秒)
  retry_times: 3           # 重试次数

# 搜索引擎配置
search_engines:
  baidu:
    enabled: true          # 是否启用
    max_pages: 10         # 最大爬取页数
  
# 数据字段配置
data_fields:
  required:               # 必需字段
    - company_name
    - industry
    - website
  optional:               # 可选字段
    - phone
    - email
    - address
```

## 🛡️ 反反爬策略

### 专家级反反爬机制
1. **请求头轮换**: 随机User-Agent和请求头
2. **智能延时**: 随机延时 + 指数退避
3. **会话管理**: 自动重试和会话保持
4. **检测规避**: 自动识别反爬页面并调整策略

### 使用建议
- 合理设置爬取间隔，避免过于频繁的请求
- 遵守网站robots.txt协议
- 仅用于合法的商业研究目的

## 📊 数据字段说明

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 公司名称 | 企业全称 | 北京科技有限公司 |
| 行业 | 所属行业 | 人工智能 |
| 官网 | 企业官方网站 | https://example.com |
| 电话 | 联系电话 | 010-12345678 |
| 邮箱 | 联系邮箱 | <EMAIL> |
| 地址 | 企业地址 | 北京市朝阳区... |
| 公司规模 | 企业规模 | 大型/中型/小型 |
| 数据完整度 | 信息完整程度 | 85.5% |

## 🔧 故障排除

### 常见问题

1. **依赖包安装失败**
   ```bash
   # 使用国内镜像源
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

2. **爬取速度慢**
   - 检查网络连接
   - 适当增加并发数（config.yaml中的concurrent_requests）
   - 减少延时时间（download_delay）

3. **数据收集不到**
   - 检查关键词是否准确
   - 尝试不同的搜索引擎
   - 查看日志文件了解详细错误信息

4. **Web界面无法访问**
   - 确认端口8501未被占用
   - 检查防火墙设置
   - 尝试使用不同端口启动

## 📝 更新日志

### v1.0.0 (2024-06-30)
- ✨ 初始版本发布
- 🎯 支持多源数据采集
- 📊 完整的可视化分析
- 💾 SQLite数据库存储
- 🛡️ 专业反反爬机制

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 📞 技术支持

如有问题，请查看：
1. 项目文档和FAQ
2. 日志文件 (logs/crawler.log)
3. 提交Issue描述问题

---

**⚠️ 免责声明**: 本工具仅用于合法的商业研究目的，使用者需遵守相关法律法规和网站使用条款。开发者不承担因使用本工具而产生的任何法律责任。
