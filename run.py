#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户信息收集系统 - 启动脚本
快速启动Web界面
"""

import subprocess
import sys
import os
from pathlib import Path

def check_requirements():
    """检查依赖包是否安装"""
    try:
        import streamlit
        import plotly
        import pandas
        import requests
        import bs4  # 修正：应该是bs4而不是beautifulsoup4
        print("✅ 所有依赖包已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("正在尝试自动安装...")

        # 尝试自动安装依赖
        try:
            import subprocess
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                          check=True, capture_output=True)
            print("✅ 依赖包安装成功，请重新运行")
            return False
        except:
            print("❌ 自动安装失败，请手动运行: pip install -r requirements.txt")
            return False

def main():
    """主函数"""
    print("🕷️ 客户信息收集系统启动中...")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        return
    
    # 确保必要目录存在
    Path("data").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    # 启动Streamlit应用
    print("🚀 启动Web界面...")
    print("📱 浏览器将自动打开 http://localhost:8501")
    print("💡 如果浏览器未自动打开，请手动访问上述地址")
    print("⏹️  按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
