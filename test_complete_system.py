#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统测试脚本
验证真实爬虫系统的所有功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from real_crawler_integration import (
    real_crawler,
    test_real_crawler_system,
    crawl_real_data,
    get_real_crawler_status
)

def print_separator(title):
    """打印分隔符"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def test_system_status():
    """测试系统状态"""
    print_separator("系统状态检查")
    
    status = get_real_crawler_status()
    print(f"系统可用性: {'✅ 可用' if status['available'] else '❌ 不可用'}")
    print(f"状态信息: {status['message']}")
    
    if status['available']:
        summary = status.get('data_summary', {})
        print(f"数据文件总数: {summary.get('total_files', 0)}")
        print(f"平台数据: {summary.get('platforms', {})}")
        
        latest = summary.get('latest_crawl')
        if latest:
            print(f"最新爬取时间: {latest}")
        else:
            print("最新爬取时间: 暂无数据")
    
    return status['available']

def test_platform_connectivity():
    """测试平台连通性"""
    print_separator("平台连通性测试")
    
    test_result = test_real_crawler_system()
    
    print(f"测试结果: {'✅ 成功' if test_result['success'] else '❌ 失败'}")
    print(f"测试信息: {test_result['message']}")
    
    if 'platforms' in test_result:
        print("\n平台详情:")
        for platform, status in test_result['platforms'].items():
            icon = "✅" if status else "❌"
            print(f"  {icon} {platform.upper()}: {'连接成功' if status else '连接失败'}")
    
    if 'summary' in test_result:
        summary = test_result['summary']
        print(f"\n总结: {summary['success']}/{summary['total']} 个平台可用")
    
    return test_result['success']

def test_douyin_search():
    """测试抖音搜索功能"""
    print_separator("抖音搜索测试")
    
    try:
        # 测试搜索功能（不需要具体URL）
        print("正在测试抖音搜索功能...")
        
        # 这里我们只测试系统是否能正常初始化抖音爬虫
        result = crawl_real_data("douyin", "测试关键词")
        
        if result['success']:
            print("✅ 抖音爬虫初始化成功")
            print("💡 提示: 实际使用时请提供真实的抖音用户URL")
        else:
            print(f"⚠️ 抖音爬虫测试: {result['message']}")
            
    except Exception as e:
        print(f"❌ 抖音测试失败: {str(e)}")

def test_company_search():
    """测试企业信息查询"""
    print_separator("企业信息查询测试")
    
    try:
        # 测试知名企业
        test_companies = ["腾讯", "阿里巴巴", "百度"]
        
        for company in test_companies[:1]:  # 只测试第一个避免过多请求
            print(f"正在测试查询企业: {company}")
            
            result = crawl_real_data("company", company)
            
            if result['success']:
                print(f"✅ {company} 查询成功")
                data = result.get('data', {})
                if data:
                    print(f"   企业名称: {data.get('company_name', '未知')}")
                    print(f"   数据来源: {data.get('source', '企查查')}")
                break
            else:
                print(f"⚠️ {company} 查询失败: {result['message']}")
                
    except Exception as e:
        print(f"❌ 企业查询测试失败: {str(e)}")

def test_industry_crawl():
    """测试行业爬取功能"""
    print_separator("行业爬取测试")
    
    try:
        # 测试小规模行业爬取
        test_industry = "科技公司"
        test_region = "北京"
        
        print(f"正在测试行业爬取: {test_industry} ({test_region})")
        
        result = crawl_real_data("industry", test_industry, test_region)
        
        if result['success']:
            print("✅ 行业爬取功能正常")
            data = result.get('data', {})
            if data:
                companies = len(data.get('data', {}).get('companies', []))
                douyin_users = len(data.get('data', {}).get('douyin_users', []))
                kuaishou_users = len(data.get('data', {}).get('kuaishou_users', []))
                
                print(f"   找到企业: {companies} 个")
                print(f"   抖音用户: {douyin_users} 个")
                print(f"   快手用户: {kuaishou_users} 个")
        else:
            print(f"⚠️ 行业爬取测试: {result['message']}")
            
    except Exception as e:
        print(f"❌ 行业爬取测试失败: {str(e)}")

def test_data_export():
    """测试数据导出功能"""
    print_separator("数据导出测试")
    
    try:
        # 检查数据目录
        data_dir = Path("data")
        
        if data_dir.exists():
            print("✅ 数据目录存在")
            
            # 检查各平台数据目录
            platforms = ["douyin", "kuaishou", "qichacha", "industry", "reports"]
            
            for platform in platforms:
                platform_dir = data_dir / platform
                if platform_dir.exists():
                    files = list(platform_dir.glob("*.json"))
                    print(f"   {platform}: {len(files)} 个数据文件")
                else:
                    print(f"   {platform}: 目录不存在（正常，暂无数据）")
        else:
            print("⚠️ 数据目录不存在，将在首次爬取时创建")
            
    except Exception as e:
        print(f"❌ 数据导出测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 开始完整系统测试")
    print("测试时间:", Path(__file__).stat().st_mtime)
    
    # 1. 系统状态检查
    system_available = test_system_status()
    
    if not system_available:
        print("\n❌ 系统不可用，测试终止")
        return False
    
    # 2. 平台连通性测试
    platforms_ok = test_platform_connectivity()
    
    if not platforms_ok:
        print("\n⚠️ 部分平台连接失败，但继续测试其他功能")
    
    # 3. 功能测试
    test_douyin_search()
    test_company_search()
    test_industry_crawl()
    test_data_export()
    
    # 测试总结
    print_separator("测试总结")
    print("✅ 真实爬虫系统基础功能测试完成")
    print("💡 系统已准备就绪，可以开始使用")
    print("\n📋 使用建议:")
    print("   1. 首次使用建议设置 HEADLESS=false 观察过程")
    print("   2. 配置企查查账号以获取完整企业信息")
    print("   3. 建议配置代理池避免IP限制")
    print("   4. 大量爬取时请控制频率，遵守平台规则")
    
    print("\n🌐 启动Web界面:")
    print("   streamlit run main.py --server.port 8502")
    print("   然后访问: http://localhost:8502")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        sys.exit(1)
