#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的启动脚本
"""

import subprocess
import sys
import os

def main():
    print("🚀 启动客户信息收集系统")
    print("=" * 40)
    
    # 确保在正确的目录
    if not os.path.exists("main.py"):
        print("❌ 找不到main.py文件")
        print("请确保在项目根目录运行此脚本")
        return
    
    print("✅ 找到main.py文件")
    print("🌐 启动Web界面...")
    print("📱 浏览器地址: http://localhost:8501")
    print("⏹️ 按 Ctrl+C 停止服务")
    print("-" * 40)
    
    try:
        # 直接启动streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main.py"
        ])
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n💡 解决方案:")
        print("1. 确保已安装streamlit: pip install streamlit")
        print("2. 检查Python环境是否正确")
        print("3. 尝试运行: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
