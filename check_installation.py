#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装检查脚本 - 检查所有依赖包的安装状态
"""

import sys
import subprocess
import importlib

def check_package(package_name, import_name=None):
    """检查单个包的安装状态"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✅ {package_name}: {version}")
        return True
    except ImportError:
        print(f"❌ {package_name}: 未安装")
        return False
    except Exception as e:
        print(f"⚠️ {package_name}: 安装异常 - {e}")
        return False

def install_package(package_name):
    """安装包"""
    try:
        print(f"🔄 正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package_name
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出错: {e}")
        return False

def main():
    """主检查函数"""
    print("🔍 检查项目依赖包安装状态")
    print("=" * 50)
    
    # 定义需要检查的包
    packages = [
        ('streamlit', 'streamlit'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('plotly', 'plotly'),
        ('requests', 'requests'),
        ('beautifulsoup4', 'bs4'),
        ('openpyxl', 'openpyxl'),
        ('pyyaml', 'yaml'),
    ]
    
    missing_packages = []
    
    # 检查所有包
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    print("\n" + "=" * 50)
    
    if not missing_packages:
        print("🎉 所有依赖包都已正确安装！")
        print("\n可以运行以下命令启动系统:")
        print("python run_enhanced.py")
        return True
    
    print(f"⚠️ 发现 {len(missing_packages)} 个缺失的包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    # 询问是否自动安装
    print("\n" + "=" * 50)
    choice = input("是否自动安装缺失的包? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        print("\n🔄 开始自动安装...")
        
        success_count = 0
        for package in missing_packages:
            if install_package(package):
                success_count += 1
        
        print(f"\n📊 安装结果: {success_count}/{len(missing_packages)} 个包安装成功")
        
        if success_count == len(missing_packages):
            print("🎉 所有包安装完成！")
            return True
        else:
            print("⚠️ 部分包安装失败，请手动安装")
            return False
    else:
        print("\n💡 请手动安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

def check_streamlit_specifically():
    """专门检查Streamlit"""
    print("\n🔍 专门检查Streamlit安装状态")
    print("-" * 30)
    
    try:
        import streamlit as st
        print(f"✅ Streamlit版本: {st.__version__}")
        
        # 测试基本功能
        print("🧪 测试Streamlit基本功能...")
        
        # 创建一个简单的测试
        test_code = '''
import streamlit as st
st.set_page_config(page_title="测试")
st.write("Streamlit测试成功！")
'''
        
        with open('streamlit_test.py', 'w', encoding='utf-8') as f:
            f.write(test_code)
        
        print("✅ Streamlit功能正常")
        print("💡 可以运行: streamlit run streamlit_test.py 进行测试")
        
        return True
        
    except ImportError:
        print("❌ Streamlit未正确安装")
        print("\n🔧 建议的解决方案:")
        print("1. pip uninstall streamlit")
        print("2. pip cache purge")
        print("3. pip install streamlit")
        return False
    except Exception as e:
        print(f"⚠️ Streamlit安装异常: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 常见问题解决方案")
    print("=" * 50)
    
    print("1. 网络问题:")
    print("   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ streamlit")
    
    print("\n2. 权限问题:")
    print("   pip install --user streamlit")
    
    print("\n3. 虚拟环境问题:")
    print("   python -m venv venv")
    print("   venv\\Scripts\\activate  # Windows")
    print("   source venv/bin/activate  # Linux/Mac")
    print("   pip install streamlit")
    
    print("\n4. 完全重装:")
    print("   pip uninstall streamlit")
    print("   pip cache purge")
    print("   pip install --no-cache-dir streamlit")

if __name__ == "__main__":
    print("🕷️ 智能客户信息收集系统 - 依赖检查")
    print("=" * 60)
    
    # 基本检查
    main_result = main()
    
    # 专门检查Streamlit
    streamlit_result = check_streamlit_specifically()
    
    if not main_result or not streamlit_result:
        provide_solutions()
    
    print("\n" + "=" * 60)
    print("检查完成！")
