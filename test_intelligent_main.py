#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试升级后的main.py智能分析功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_intelligent_analyzer():
    """测试智能分析器"""
    print("Testing IntelligentAnalyzer...")
    
    try:
        from intelligent_analysis_framework import IntelligentAnalyzer
        print("✅ IntelligentAnalyzer imported successfully")
        
        # 测试基本功能
        analyzer = IntelligentAnalyzer()
        result = analyzer.analyze_industry_input('hotel construction')
        print(f"✅ Analysis successful, identified industry: {result['industry_info']['primary_industry']}")
        
        return True
        
    except Exception as e:
        print(f"❌ IntelligentAnalyzer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_imports():
    """测试main.py的导入"""
    print("\nTesting main.py imports...")
    
    try:
        # 测试所有导入
        from src.crawler_engine import CrawlerEngine
        from src.data_processor import DataProcessor
        from src.visualizer import DataVisualizer
        from src.database_manager import DatabaseManager
        from src.region_manager import RegionManager
        from src.utils.logger import setup_logger
        from intelligent_analysis_framework import IntelligentAnalyzer
        
        print("✅ All imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streamlit_compatibility():
    """测试Streamlit兼容性"""
    print("\nTesting Streamlit compatibility...")
    
    try:
        import streamlit as st
        print(f"✅ Streamlit version: {st.__version__}")
        return True
        
    except Exception as e:
        print(f"❌ Streamlit test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Testing upgraded main.py with intelligent analysis")
    print("=" * 60)
    
    tests = [
        ("Intelligent Analyzer", test_intelligent_analyzer),
        ("Main.py Imports", test_main_imports),
        ("Streamlit Compatibility", test_streamlit_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📋 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {len(results)}, Passed: {passed}, Failed: {len(results) - passed}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Ready to launch Streamlit app.")
        return True
    else:
        print(f"\n⚠️ {len(results) - passed} tests failed. Please check the issues.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
