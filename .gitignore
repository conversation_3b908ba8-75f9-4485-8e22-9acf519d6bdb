# 客户信息收集系统 - Git忽略文件

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 数据文件
data/*.db
data/*.sqlite
data/*.csv
data/*.xlsx
data/*.json
data/backups/

# 日志文件
logs/
*.log

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# IDE配置
.vscode/
.idea/
*.swp
*.swo
*~

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 爬虫缓存
.scrapy/

# Selenium
chromedriver*
geckodriver*
*.exe

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 配置文件备份
config.yaml.bak
config.yaml.backup
