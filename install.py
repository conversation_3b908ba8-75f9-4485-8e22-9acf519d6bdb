#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户信息收集系统 - 自动安装脚本
一键安装所有依赖和配置环境
"""

import subprocess
import sys
import os
from pathlib import Path
import platform

def print_banner():
    """打印横幅"""
    banner = """
    🕷️ 智能客户信息收集系统
    ================================
    专家级爬虫 + 数据分析 + 可视化
    ================================
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8+")
        print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def install_requirements():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    # 检查requirements.txt是否存在
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    try:
        # 升级pip
        print("   升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        print("   安装项目依赖...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True, text=True)
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        print("尝试使用国内镜像源...")
        
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt",
                "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"
            ], check=True)
            print("✅ 使用镜像源安装成功")
            return True
        except subprocess.CalledProcessError as e2:
            print(f"❌ 镜像源安装也失败: {e2}")
            return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建项目目录...")
    
    directories = ["data", "logs", "data/backups"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ {directory}/")
    
    print("✅ 目录创建完成")

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试安装...")
    
    test_imports = [
        ("streamlit", "Streamlit Web框架"),
        ("plotly", "Plotly可视化库"),
        ("pandas", "Pandas数据处理"),
        ("requests", "Requests网络库"),
        ("bs4", "BeautifulSoup解析库"),
        ("selenium", "Selenium自动化"),
        ("fake_useragent", "用户代理库"),
        ("fuzzywuzzy", "模糊匹配库")
    ]
    
    failed_imports = []
    
    for module, description in test_imports:
        try:
            __import__(module)
            print(f"   ✅ {description}")
        except ImportError:
            print(f"   ❌ {description}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ 以下模块导入失败: {', '.join(failed_imports)}")
        return False
    
    print("\n✅ 所有模块测试通过")
    return True

def show_usage_instructions():
    """显示使用说明"""
    instructions = """
🎉 安装完成！

📖 使用方法:

1. 🚀 启动Web界面:
   python run.py

2. 🧪 运行演示:
   python demo.py

3. 📋 查看详细文档:
   README.md

🌐 Web界面地址:
   http://localhost:8501

💡 使用提示:
   • 首次使用建议先运行demo.py了解系统功能
   • 合理设置爬取频率，避免被反爬
   • 定期备份数据库文件
   • 遵守网站使用条款和法律法规

📞 技术支持:
   • 查看logs/目录下的日志文件
   • 阅读README.md获取详细信息
   • 检查config.yaml配置文件

⚠️  注意事项:
   • 本工具仅供合法商业研究使用
   • 请遵守相关法律法规和网站条款
   • 建议在虚拟环境中运行
    """
    print(instructions)

def main():
    """主安装函数"""
    print_banner()

    # 检查Python版本
    if not check_python_version():
        return False

    # 显示系统信息
    print(f"\n💻 系统信息:")
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   Python路径: {sys.executable}")
    print(f"   工作目录: {os.getcwd()}")

    # 询问是否使用虚拟环境
    use_venv = input("\n🐍 是否使用虚拟环境？(推荐) (Y/n): ").strip().lower()

    if use_venv in ['', 'y', 'yes']:
        print("\n🏗️ 配置虚拟环境...")
        print("   运行: python setup_env.py")
        print("   这将创建独立的Python环境，避免包冲突")

        try:
            result = subprocess.run([sys.executable, "setup_env.py"], check=True)
            print("✅ 虚拟环境配置完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 虚拟环境配置失败，回退到全局安装")
        except FileNotFoundError:
            print("❌ setup_env.py文件不存在，回退到全局安装")

    print("\n📦 使用全局Python环境安装...")

    # 安装依赖
    if not install_requirements():
        print("\n❌ 安装失败，请检查网络连接和Python环境")
        return False

    # 创建目录
    create_directories()

    # 测试安装
    if not test_installation():
        print("\n❌ 安装测试失败，可能存在依赖问题")
        return False

    # 显示使用说明
    show_usage_instructions()

    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎊 安装成功完成！")
        else:
            print("\n💥 安装过程中出现问题")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 安装过程中出现未知错误: {e}")
        sys.exit(1)
