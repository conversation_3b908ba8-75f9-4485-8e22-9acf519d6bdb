#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR功能演示
展示OCR技术在数据爬取中的应用
"""

import sys
import os
import time
import re
from pathlib import Path
from typing import Dict, List, Any
import requests
from PIL import Image, ImageDraw, ImageFont
import io
import base64

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class OCRDemo:
    """OCR功能演示类"""
    
    def __init__(self):
        """初始化OCR演示"""
        self.demo_data = self._create_demo_data()
        
    def _create_demo_data(self) -> Dict[str, Any]:
        """创建演示数据"""
        return {
            'enterprise_info': [
                {
                    'company_name': '北京建设工程有限公司',
                    'legal_person': '张建国',
                    'phone': '010-88888888',
                    'address': '北京市朝阳区建国路88号',
                    'business_scope': '建筑工程施工、装修装饰、市政工程',
                    'registration_capital': '5000万元',
                    'ocr_confidence': 0.95
                },
                {
                    'company_name': '上海酒店管理集团',
                    'legal_person': '李明华',
                    'phone': '021-66666666',
                    'address': '上海市浦东新区陆家嘴金融区',
                    'business_scope': '酒店管理、餐饮服务、会议服务',
                    'registration_capital': '8000万元',
                    'ocr_confidence': 0.92
                },
                {
                    'company_name': '广州房地产开发有限公司',
                    'legal_person': '王建设',
                    'phone': '020-77777777',
                    'address': '广州市天河区珠江新城',
                    'business_scope': '房地产开发、物业管理、建筑设计',
                    'registration_capital': '12000万元',
                    'ocr_confidence': 0.88
                }
            ],
            'ocr_techniques': {
                'text_recognition': {
                    'name': '文字识别',
                    'description': '识别图片中的中英文文字',
                    'accuracy': '95%+',
                    'use_cases': ['企业证照识别', '招投标文件解析', '合同信息提取']
                },
                'layout_analysis': {
                    'name': '版面分析',
                    'description': '分析文档结构和布局',
                    'accuracy': '90%+',
                    'use_cases': ['表格识别', '段落分割', '标题提取']
                },
                'form_recognition': {
                    'name': '表单识别',
                    'description': '识别结构化表单数据',
                    'accuracy': '92%+',
                    'use_cases': ['企业信息表', '财务报表', '申请表格']
                },
                'handwriting_recognition': {
                    'name': '手写识别',
                    'description': '识别手写文字和签名',
                    'accuracy': '85%+',
                    'use_cases': ['签名验证', '手写备注', '填写表单']
                }
            }
        }
    
    def simulate_ocr_extraction(self, image_type: str, target_industry: str = None) -> Dict[str, Any]:
        """模拟OCR数据提取"""
        print(f"🔍 模拟OCR提取: {image_type}")
        
        # 模拟处理时间
        time.sleep(1)
        
        if image_type == "enterprise_certificate":
            return self._simulate_certificate_ocr(target_industry)
        elif image_type == "bidding_document":
            return self._simulate_bidding_ocr(target_industry)
        elif image_type == "business_license":
            return self._simulate_license_ocr(target_industry)
        elif image_type == "contact_form":
            return self._simulate_contact_ocr(target_industry)
        else:
            return {
                'success': False,
                'message': '不支持的图像类型',
                'data': []
            }
    
    def _simulate_certificate_ocr(self, industry: str = None) -> Dict[str, Any]:
        """模拟企业证书OCR识别"""
        certificates = [
            {
                'certificate_type': '建筑业企业资质证书',
                'company_name': '北京建设工程有限公司',
                'certificate_number': 'D111000001',
                'qualification_level': '建筑工程施工总承包壹级',
                'valid_until': '2025-12-31',
                'issuing_authority': '北京市住房和城乡建设委员会',
                'ocr_confidence': 0.96,
                'extracted_fields': [
                    {'field': '企业名称', 'value': '北京建设工程有限公司', 'confidence': 0.98},
                    {'field': '证书编号', 'value': 'D111000001', 'confidence': 0.95},
                    {'field': '资质等级', 'value': '建筑工程施工总承包壹级', 'confidence': 0.97}
                ]
            },
            {
                'certificate_type': '食品经营许可证',
                'company_name': '上海酒店管理集团',
                'certificate_number': 'JY13101000001',
                'business_scope': '热食类食品制售、冷食类食品制售',
                'valid_until': '2026-06-30',
                'issuing_authority': '上海市市场监督管理局',
                'ocr_confidence': 0.94,
                'extracted_fields': [
                    {'field': '企业名称', 'value': '上海酒店管理集团', 'confidence': 0.96},
                    {'field': '许可证编号', 'value': 'JY13101000001', 'confidence': 0.93},
                    {'field': '经营范围', 'value': '热食类食品制售、冷食类食品制售', 'confidence': 0.95}
                ]
            }
        ]
        
        # 根据行业筛选
        if industry:
            if '建筑' in industry or '工程' in industry:
                selected = [cert for cert in certificates if '建筑' in cert['certificate_type']]
            elif '酒店' in industry or '餐饮' in industry:
                selected = [cert for cert in certificates if '食品' in cert['certificate_type']]
            else:
                selected = certificates[:1]
        else:
            selected = certificates
        
        return {
            'success': True,
            'message': f'成功识别 {len(selected)} 个证书',
            'data': selected,
            'ocr_stats': {
                'total_fields': sum(len(cert['extracted_fields']) for cert in selected),
                'avg_confidence': sum(cert['ocr_confidence'] for cert in selected) / len(selected) if selected else 0,
                'processing_time': '1.2秒'
            }
        }
    
    def _simulate_bidding_ocr(self, industry: str = None) -> Dict[str, Any]:
        """模拟招投标文件OCR识别"""
        bidding_docs = [
            {
                'project_name': '北京市第三人民医院新建工程',
                'bidding_company': '北京建设工程有限公司',
                'bid_amount': '8500万元',
                'project_duration': '24个月',
                'contact_person': '张工程师',
                'contact_phone': '010-88888888',
                'submission_date': '2024-12-15',
                'ocr_confidence': 0.91,
                'extracted_sections': [
                    {'section': '项目概况', 'confidence': 0.93},
                    {'section': '技术要求', 'confidence': 0.89},
                    {'section': '商务条款', 'confidence': 0.92}
                ]
            },
            {
                'project_name': '上海国际酒店装修改造项目',
                'bidding_company': '上海装饰工程有限公司',
                'bid_amount': '3200万元',
                'project_duration': '8个月',
                'contact_person': '李设计师',
                'contact_phone': '021-66666666',
                'submission_date': '2024-12-10',
                'ocr_confidence': 0.88,
                'extracted_sections': [
                    {'section': '设计方案', 'confidence': 0.90},
                    {'section': '材料清单', 'confidence': 0.86},
                    {'section': '施工计划', 'confidence': 0.89}
                ]
            }
        ]
        
        # 根据行业筛选
        if industry:
            if '医院' in industry or '医疗' in industry:
                selected = [doc for doc in bidding_docs if '医院' in doc['project_name']]
            elif '酒店' in industry:
                selected = [doc for doc in bidding_docs if '酒店' in doc['project_name']]
            else:
                selected = bidding_docs[:1]
        else:
            selected = bidding_docs
        
        return {
            'success': True,
            'message': f'成功识别 {len(selected)} 个招投标文件',
            'data': selected,
            'ocr_stats': {
                'total_sections': sum(len(doc['extracted_sections']) for doc in selected),
                'avg_confidence': sum(doc['ocr_confidence'] for doc in selected) / len(selected) if selected else 0,
                'processing_time': '2.1秒'
            }
        }
    
    def _simulate_license_ocr(self, industry: str = None) -> Dict[str, Any]:
        """模拟营业执照OCR识别"""
        licenses = [
            {
                'company_name': '北京建设工程有限公司',
                'unified_credit_code': '91110000000000001X',
                'legal_person': '张建国',
                'registered_capital': '5000万元人民币',
                'establishment_date': '2010-03-15',
                'business_scope': '建筑工程施工；装修装饰；市政工程；园林绿化',
                'address': '北京市朝阳区建国路88号',
                'ocr_confidence': 0.97,
                'extracted_fields': [
                    {'field': '企业名称', 'value': '北京建设工程有限公司', 'confidence': 0.99},
                    {'field': '统一社会信用代码', 'value': '91110000000000001X', 'confidence': 0.96},
                    {'field': '法定代表人', 'value': '张建国', 'confidence': 0.98}
                ]
            }
        ]
        
        return {
            'success': True,
            'message': f'成功识别营业执照',
            'data': licenses,
            'ocr_stats': {
                'total_fields': len(licenses[0]['extracted_fields']),
                'avg_confidence': licenses[0]['ocr_confidence'],
                'processing_time': '0.8秒'
            }
        }
    
    def _simulate_contact_ocr(self, industry: str = None) -> Dict[str, Any]:
        """模拟联系表单OCR识别"""
        contacts = [
            {
                'form_type': '企业联系信息表',
                'company_name': '广州房地产开发有限公司',
                'contact_person': '王总经理',
                'position': '总经理',
                'phone': '020-77777777',
                'mobile': '***********',
                'email': '<EMAIL>',
                'address': '广州市天河区珠江新城',
                'business_focus': '高端住宅开发、商业地产',
                'ocr_confidence': 0.89,
                'handwritten_fields': [
                    {'field': '签名', 'confidence': 0.75},
                    {'field': '备注', 'confidence': 0.82}
                ]
            }
        ]
        
        return {
            'success': True,
            'message': f'成功识别联系表单',
            'data': contacts,
            'ocr_stats': {
                'total_fields': 8,
                'avg_confidence': contacts[0]['ocr_confidence'],
                'handwritten_fields': len(contacts[0]['handwritten_fields']),
                'processing_time': '1.5秒'
            }
        }
    
    def get_ocr_capabilities(self) -> Dict[str, Any]:
        """获取OCR能力说明"""
        return {
            'supported_formats': [
                'PNG', 'JPG', 'JPEG', 'BMP', 'TIFF', 'PDF'
            ],
            'supported_languages': [
                '简体中文', '繁体中文', '英文', '数字'
            ],
            'recognition_types': [
                '印刷体文字', '手写文字', '表格数据', '印章识别'
            ],
            'accuracy_rates': {
                '印刷体中文': '95%+',
                '印刷体英文': '98%+',
                '手写中文': '85%+',
                '数字识别': '99%+',
                '表格识别': '90%+'
            },
            'processing_speed': {
                '单页文档': '< 2秒',
                '多页文档': '< 5秒/页',
                '高清图片': '< 3秒'
            }
        }
    
    def demonstrate_ocr_workflow(self, industry: str, document_types: List[str]) -> Dict[str, Any]:
        """演示OCR工作流程"""
        print(f"🚀 开始OCR工作流程演示")
        print(f"目标行业: {industry}")
        print(f"文档类型: {', '.join(document_types)}")
        
        results = {}
        total_data = []
        
        for doc_type in document_types:
            print(f"\n📄 处理文档类型: {doc_type}")
            result = self.simulate_ocr_extraction(doc_type, industry)
            
            if result['success']:
                print(f"✅ {result['message']}")
                results[doc_type] = result
                total_data.extend(result['data'])
            else:
                print(f"❌ {result['message']}")
                results[doc_type] = result
        
        # 生成总结报告
        summary = {
            'total_documents': len(document_types),
            'successful_extractions': len([r for r in results.values() if r['success']]),
            'total_data_points': len(total_data),
            'avg_confidence': sum(
                item.get('ocr_confidence', 0) for item in total_data
            ) / len(total_data) if total_data else 0,
            'processing_time': f"{len(document_types) * 1.5:.1f}秒"
        }
        
        return {
            'success': True,
            'message': f'OCR工作流程完成，处理了 {summary["successful_extractions"]}/{summary["total_documents"]} 个文档',
            'results': results,
            'summary': summary,
            'extracted_data': total_data
        }

def main():
    """主演示函数"""
    demo = OCRDemo()
    
    print("🔍 OCR数据爬取技术演示")
    print("=" * 60)
    
    # 演示OCR能力
    capabilities = demo.get_ocr_capabilities()
    print("\n📋 OCR技术能力:")
    print(f"支持格式: {', '.join(capabilities['supported_formats'])}")
    print(f"支持语言: {', '.join(capabilities['supported_languages'])}")
    print(f"识别类型: {', '.join(capabilities['recognition_types'])}")
    
    # 演示不同行业的OCR应用
    test_scenarios = [
        {
            'industry': '建筑工程',
            'documents': ['enterprise_certificate', 'bidding_document', 'business_license']
        },
        {
            'industry': '酒店餐饮',
            'documents': ['enterprise_certificate', 'contact_form', 'business_license']
        },
        {
            'industry': '房地产开发',
            'documents': ['bidding_document', 'contact_form', 'business_license']
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n" + "="*60)
        print(f"🎯 行业场景: {scenario['industry']}")
        print("="*60)
        
        workflow_result = demo.demonstrate_ocr_workflow(
            scenario['industry'], 
            scenario['documents']
        )
        
        if workflow_result['success']:
            summary = workflow_result['summary']
            print(f"\n📊 处理总结:")
            print(f"   文档数量: {summary['total_documents']}")
            print(f"   成功提取: {summary['successful_extractions']}")
            print(f"   数据条目: {summary['total_data_points']}")
            print(f"   平均置信度: {summary['avg_confidence']:.2f}")
            print(f"   处理时间: {summary['processing_time']}")
            
            # 显示部分提取数据
            extracted_data = workflow_result['extracted_data']
            if extracted_data:
                print(f"\n📋 提取数据示例:")
                for i, item in enumerate(extracted_data[:2]):
                    if 'company_name' in item:
                        print(f"   {i+1}. 企业: {item['company_name']}")
                        if 'contact_phone' in item:
                            print(f"      电话: {item['contact_phone']}")
                        if 'ocr_confidence' in item:
                            print(f"      置信度: {item['ocr_confidence']:.2f}")

if __name__ == "__main__":
    main()
