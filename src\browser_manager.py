#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器管理模块
负责Playwright浏览器实例的创建、管理和销毁
"""

import asyncio
import random
import logging
from typing import Optional, Dict, List
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright
from .proxy_manager import get_proxy_manager, ProxyInfo
from .fingerprint_manager import get_fingerprint_manager, BrowserFingerprint
from .behavior_simulator import get_behavior_simulator
from .captcha_solver import get_captcha_solver, CaptchaType

class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, headless: bool = True, max_browsers: int = 3, use_proxy: bool = False):
        """
        初始化浏览器管理器

        Args:
            headless: 是否无头模式运行
            max_browsers: 最大浏览器实例数
            use_proxy: 是否使用代理
        """
        self.headless = headless
        self.max_browsers = max_browsers
        self.use_proxy = use_proxy
        self.playwright: Optional[Playwright] = None
        self.browsers: List[Browser] = []
        self.contexts: List[BrowserContext] = []
        self.logger = logging.getLogger(__name__)

        # 代理管理器
        self.proxy_manager = get_proxy_manager() if use_proxy else None

        # 指纹管理器
        self.fingerprint_manager = get_fingerprint_manager()

        # 行为模拟器
        self.behavior_simulator = get_behavior_simulator()

        # 验证码解决器
        self.captcha_solver = get_captcha_solver()
        
        # 用户代理池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        
        # 浏览器启动参数
        self.browser_args = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding'
        ]
    
    async def start(self):
        """启动Playwright"""
        if self.playwright is None:
            self.playwright = await async_playwright().start()
            self.logger.info("✅ Playwright启动成功")
    
    async def create_browser(self, proxy: Optional[str] = None) -> Browser:
        """
        创建新的浏览器实例
        
        Args:
            proxy: 代理服务器地址 (格式: http://host:port)
            
        Returns:
            Browser实例
        """
        if self.playwright is None:
            await self.start()
        
        # 构建启动选项
        launch_options = {
            'headless': self.headless,
            'args': self.browser_args.copy()
        }
        
        # 添加代理配置
        if proxy:
            launch_options['proxy'] = {'server': proxy}
            self.logger.info(f"🌐 使用代理: {proxy}")
        
        try:
            browser = await self.playwright.chromium.launch(**launch_options)
            self.browsers.append(browser)
            self.logger.info(f"🚀 浏览器实例创建成功 (总数: {len(self.browsers)})")
            return browser
            
        except Exception as e:
            self.logger.error(f"❌ 浏览器创建失败: {e}")
            raise
    
    async def create_context(self, browser: Browser, proxy: ProxyInfo = None, fingerprint: BrowserFingerprint = None, **kwargs) -> BrowserContext:
        """
        创建浏览器上下文

        Args:
            browser: 浏览器实例
            proxy: 代理信息
            fingerprint: 浏览器指纹
            **kwargs: 额外的上下文选项

        Returns:
            BrowserContext实例
        """
        # 获取代理配置
        if self.use_proxy and proxy is None and self.proxy_manager:
            proxy = await self.proxy_manager.get_proxy()

        # 生成或使用指定的指纹
        if fingerprint is None:
            fingerprint = self.fingerprint_manager.generate_fingerprint()

        # 使用指纹管理器获取上下文选项
        context_options = self.fingerprint_manager.get_context_options(fingerprint)

        # 添加代理配置
        if proxy:
            context_options['proxy'] = {
                'server': f"{proxy.protocol}://{proxy.host}:{proxy.port}"
            }
            if proxy.username and proxy.password:
                context_options['proxy']['username'] = proxy.username
                context_options['proxy']['password'] = proxy.password

            self.logger.debug(f"🌐 使用代理: {proxy.host}:{proxy.port}")

        # 合并用户提供的选项
        context_options.update(kwargs)
        
        try:
            context = await browser.new_context(**context_options)
            self.contexts.append(context)
            
            # 添加高级反检测脚本
            anti_detection_script = self.fingerprint_manager.get_anti_detection_script(fingerprint)
            await context.add_init_script(anti_detection_script)
            
            self.logger.info(f"📄 浏览器上下文创建成功 (总数: {len(self.contexts)})")
            return context
            
        except Exception as e:
            self.logger.error(f"❌ 上下文创建失败: {e}")
            raise
    
    async def create_page(self, context: BrowserContext) -> Page:
        """
        创建新页面
        
        Args:
            context: 浏览器上下文
            
        Returns:
            Page实例
        """
        try:
            page = await context.new_page()
            
            # 设置页面超时
            page.set_default_timeout(30000)  # 30秒
            page.set_default_navigation_timeout(30000)  # 30秒
            
            self.logger.info("📃 新页面创建成功")
            return page
            
        except Exception as e:
            self.logger.error(f"❌ 页面创建失败: {e}")
            raise

    async def human_like_navigate(self, page: Page, url: str, wait_time: float = None) -> bool:
        """
        人性化页面导航

        Args:
            page: 页面对象
            url: 目标URL
            wait_time: 导航后等待时间

        Returns:
            是否导航成功
        """
        try:
            # 导航到页面
            await page.goto(url, timeout=30000)

            # 模拟页面加载后的等待和活动
            if wait_time is None:
                wait_time = random.uniform(2.0, 5.0)

            await self.behavior_simulator.wait_with_random_activity(page, wait_time)

            self.logger.info(f"🌐 人性化导航成功: {url}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 人性化导航失败: {url} - {e}")
            return False

    async def human_like_search(self, page: Page, search_selector: str, search_text: str,
                               submit_selector: str = None) -> bool:
        """
        人性化搜索操作

        Args:
            page: 页面对象
            search_selector: 搜索框选择器
            search_text: 搜索文本
            submit_selector: 提交按钮选择器（可选）

        Returns:
            是否搜索成功
        """
        try:
            # 人性化输入搜索文本
            if not await self.behavior_simulator.human_like_typing(page, search_selector, search_text):
                return False

            # 随机暂停
            await asyncio.sleep(random.uniform(0.5, 1.5))

            # 提交搜索
            if submit_selector:
                # 点击搜索按钮
                if not await self.behavior_simulator.human_like_click(page, submit_selector):
                    return False
            else:
                # 按回车键
                await page.keyboard.press("Enter")

            # 等待搜索结果加载
            await self.behavior_simulator.wait_with_random_activity(page, random.uniform(3.0, 6.0))

            self.logger.info(f"🔍 人性化搜索完成: {search_text}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 人性化搜索失败: {search_text} - {e}")
            return False

    async def simulate_browsing_session(self, page: Page, duration: float = 30.0) -> None:
        """
        模拟浏览会话

        Args:
            page: 页面对象
            duration: 会话持续时间（秒）
        """
        try:
            start_time = asyncio.get_event_loop().time()

            while asyncio.get_event_loop().time() - start_time < duration:
                # 随机选择行为
                action = random.choice([
                    "scroll_down",
                    "scroll_up",
                    "mouse_move",
                    "pause",
                    "read"
                ])

                if action == "scroll_down":
                    await self.behavior_simulator.human_like_scroll(page, "down")
                elif action == "scroll_up":
                    await self.behavior_simulator.human_like_scroll(page, "up")
                elif action == "mouse_move":
                    await self.behavior_simulator.random_mouse_movement(page, 1.0)
                elif action == "pause":
                    await asyncio.sleep(random.uniform(1.0, 3.0))
                elif action == "read":
                    await self.behavior_simulator.simulate_reading_behavior(page, random.uniform(2.0, 5.0))

                # 行为间隔
                await asyncio.sleep(random.uniform(0.5, 2.0))

            self.logger.info(f"🎭 浏览会话模拟完成: {duration:.1f}s")

        except Exception as e:
            self.logger.error(f"❌ 浏览会话模拟失败: {e}")

    async def handle_captcha(self, page: Page, captcha_selector: str,
                           input_selector: str = None, captcha_type: CaptchaType = CaptchaType.TEXT) -> bool:
        """
        处理验证码

        Args:
            page: 页面对象
            captcha_selector: 验证码图片选择器
            input_selector: 验证码输入框选择器（文字验证码需要）
            captcha_type: 验证码类型

        Returns:
            是否处理成功
        """
        try:
            # 识别验证码
            captcha_result = await self.captcha_solver.solve_captcha(page, captcha_selector, captcha_type)

            if not captcha_result.success:
                self.logger.warning(f"⚠️ 验证码识别失败: {captcha_result.error_message}")
                return False

            # 执行验证码动作
            success = await self.captcha_solver.execute_captcha_action(
                page, captcha_result, captcha_type, input_selector
            )

            if success:
                self.logger.info(f"✅ 验证码处理成功: {captcha_type.value}")
            else:
                self.logger.warning(f"⚠️ 验证码动作执行失败")

            return success

        except Exception as e:
            self.logger.error(f"❌ 验证码处理异常: {e}")
            return False

    async def smart_login_with_captcha(self, page: Page, username: str, password: str,
                                     username_selector: str, password_selector: str,
                                     captcha_selector: str = None, captcha_input_selector: str = None,
                                     submit_selector: str = None) -> bool:
        """
        智能登录（包含验证码处理）

        Args:
            page: 页面对象
            username: 用户名
            password: 密码
            username_selector: 用户名输入框选择器
            password_selector: 密码输入框选择器
            captcha_selector: 验证码图片选择器
            captcha_input_selector: 验证码输入框选择器
            submit_selector: 提交按钮选择器

        Returns:
            是否登录成功
        """
        try:
            # 输入用户名
            if not await self.behavior_simulator.human_like_typing(page, username_selector, username):
                return False

            await asyncio.sleep(random.uniform(0.5, 1.0))

            # 输入密码
            if not await self.behavior_simulator.human_like_typing(page, password_selector, password):
                return False

            await asyncio.sleep(random.uniform(0.5, 1.0))

            # 处理验证码（如果存在）
            if captcha_selector and captcha_input_selector:
                try:
                    # 检查验证码是否存在
                    await page.wait_for_selector(captcha_selector, timeout=3000)

                    if not await self.handle_captcha(page, captcha_selector, captcha_input_selector):
                        self.logger.warning("⚠️ 验证码处理失败，尝试继续登录")

                except Exception:
                    self.logger.debug("🔍 未发现验证码，继续登录流程")

            await asyncio.sleep(random.uniform(0.5, 1.0))

            # 提交登录
            if submit_selector:
                if not await self.behavior_simulator.human_like_click(page, submit_selector):
                    return False
            else:
                await page.keyboard.press("Enter")

            # 等待登录结果
            await self.behavior_simulator.wait_with_random_activity(page, random.uniform(3.0, 6.0))

            self.logger.info("✅ 智能登录流程完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 智能登录失败: {e}")
            return False
    
    async def get_or_create_browser(self, proxy: Optional[str] = None) -> Browser:
        """
        获取或创建浏览器实例
        
        Args:
            proxy: 代理服务器地址
            
        Returns:
            Browser实例
        """
        # 如果没有浏览器或达到最大数量，创建新的
        if not self.browsers or len(self.browsers) >= self.max_browsers:
            return await self.create_browser(proxy)
        
        # 返回现有的浏览器
        return self.browsers[0]
    
    async def close_context(self, context: BrowserContext):
        """关闭浏览器上下文"""
        try:
            await context.close()
            if context in self.contexts:
                self.contexts.remove(context)
            self.logger.info(f"📄 上下文已关闭 (剩余: {len(self.contexts)})")
        except Exception as e:
            self.logger.error(f"❌ 关闭上下文失败: {e}")
    
    async def close_browser(self, browser: Browser):
        """关闭浏览器实例"""
        try:
            await browser.close()
            if browser in self.browsers:
                self.browsers.remove(browser)
            self.logger.info(f"🚀 浏览器已关闭 (剩余: {len(self.browsers)})")
        except Exception as e:
            self.logger.error(f"❌ 关闭浏览器失败: {e}")
    
    async def close_all(self):
        """关闭所有浏览器实例和上下文"""
        self.logger.info("🔄 开始关闭所有浏览器资源...")
        
        # 关闭所有上下文
        for context in self.contexts.copy():
            await self.close_context(context)
        
        # 关闭所有浏览器
        for browser in self.browsers.copy():
            await self.close_browser(browser)
        
        # 停止Playwright
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None
            self.logger.info("✅ Playwright已停止")
        
        self.logger.info("✅ 所有浏览器资源已清理完成")
    
    def __del__(self):
        """析构函数，确保资源清理"""
        if self.browsers or self.contexts or self.playwright:
            self.logger.warning("⚠️ 检测到未清理的浏览器资源，建议调用close_all()")

# 全局浏览器管理器实例
_browser_manager: Optional[BrowserManager] = None

def get_browser_manager(headless: bool = True, max_browsers: int = 3, use_proxy: bool = False) -> BrowserManager:
    """
    获取全局浏览器管理器实例

    Args:
        headless: 是否无头模式
        max_browsers: 最大浏览器数量
        use_proxy: 是否使用代理

    Returns:
        BrowserManager实例
    """
    global _browser_manager
    if _browser_manager is None:
        _browser_manager = BrowserManager(headless=headless, max_browsers=max_browsers, use_proxy=use_proxy)
    return _browser_manager

async def cleanup_browser_manager():
    """清理全局浏览器管理器"""
    global _browser_manager
    if _browser_manager:
        await _browser_manager.close_all()
        _browser_manager = None
