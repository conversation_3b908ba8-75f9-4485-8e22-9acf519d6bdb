#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强搜索引擎测试脚本
测试通过搜索引擎查找专业网站内容的功能
"""

import sys
import os
from pathlib import Path
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_enhanced_search_import():
    """测试增强搜索引擎导入"""
    print("🔍 测试增强搜索引擎导入...")
    
    try:
        from src.enhanced_search_engine import EnhancedSearchEngine
        print("✅ EnhancedSearchEngine 导入成功")
        return True
    except Exception as e:
        print(f"❌ EnhancedSearchEngine 导入失败: {e}")
        traceback.print_exc()
        return False

def test_enhanced_search_init():
    """测试增强搜索引擎初始化"""
    print("\n🚀 测试增强搜索引擎初始化...")
    
    try:
        from src.enhanced_search_engine import EnhancedSearchEngine
        
        search_engine = EnhancedSearchEngine()
        
        if hasattr(search_engine, 'session') and search_engine.session:
            print("✅ 增强搜索引擎会话初始化成功")
        else:
            print("❌ 增强搜索引擎会话初始化失败")
            return False
        
        if hasattr(search_engine, 'professional_domains'):
            print("✅ 专业域名配置加载成功")
            print(f"   招标网站: {len(search_engine.professional_domains.get('bidding', []))} 个")
            print(f"   房地产网站: {len(search_engine.professional_domains.get('real_estate', []))} 个")
            print(f"   酒店网站: {len(search_engine.professional_domains.get('hotel', []))} 个")
        else:
            print("❌ 专业域名配置加载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 增强搜索引擎初始化失败: {e}")
        traceback.print_exc()
        return False

def test_query_generation():
    """测试查询生成功能"""
    print("\n🔤 测试查询生成功能...")
    
    try:
        from src.enhanced_search_engine import EnhancedSearchEngine
        
        search_engine = EnhancedSearchEngine()
        
        # 测试招标查询生成
        bidding_queries = search_engine._generate_professional_queries("bidding", "酒店建设", "广东省", "深圳市")
        print(f"✅ 招标查询生成: {len(bidding_queries)} 个")
        if bidding_queries:
            print(f"   示例: {bidding_queries[0]}")
        
        # 测试房地产查询生成
        real_estate_queries = search_engine._generate_professional_queries("real_estate", "房地产开发", "广东省", "深圳市")
        print(f"✅ 房地产查询生成: {len(real_estate_queries)} 个")
        if real_estate_queries:
            print(f"   示例: {real_estate_queries[0]}")
        
        # 测试酒店查询生成
        hotel_queries = search_engine._generate_professional_queries("hotel", "酒店管理", "广东省", "深圳市")
        print(f"✅ 酒店查询生成: {len(hotel_queries)} 个")
        if hotel_queries:
            print(f"   示例: {hotel_queries[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 查询生成测试失败: {e}")
        traceback.print_exc()
        return False

def test_content_filtering():
    """测试内容过滤功能"""
    print("\n🔍 测试内容过滤功能...")
    
    try:
        from src.enhanced_search_engine import EnhancedSearchEngine
        
        search_engine = EnhancedSearchEngine()
        
        # 测试专业网站检查
        test_urls = [
            "http://www.ccgp.gov.cn/search/result",  # 政府采购网
            "https://www.fang.com/project/123",      # 搜房网
            "https://www.meadin.com/hotel/news",     # 迈点网
            "https://zhihu.com/question/123",        # 知乎（应该被过滤）
            "https://baidu.com/search"               # 百度（应该被过滤）
        ]
        
        for url in test_urls:
            is_bidding = search_engine._is_professional_source(url, "bidding")
            is_real_estate = search_engine._is_professional_source(url, "real_estate")
            is_hotel = search_engine._is_professional_source(url, "hotel")
            
            print(f"URL: {url}")
            print(f"  招标专业源: {is_bidding}, 房地产专业源: {is_real_estate}, 酒店专业源: {is_hotel}")
        
        # 测试内容相关性检查
        test_contents = [
            ("深圳市某酒店建设工程招标公告", "招标", "bidding"),
            ("广州房地产开发项目投资", "房地产", "real_estate"),
            ("北京五星级酒店开业典礼", "酒店", "hotel"),
            ("某公司招聘软件工程师", "招聘", "bidding"),  # 应该被过滤
        ]
        
        for title, desc, search_type in test_contents:
            is_relevant = search_engine._is_relevant_content(title, desc, search_type)
            print(f"内容: {title} -> 相关性: {is_relevant}")
        
        return True
        
    except Exception as e:
        print(f"❌ 内容过滤测试失败: {e}")
        traceback.print_exc()
        return False

def test_information_extraction():
    """测试信息提取功能"""
    print("\n📊 测试信息提取功能...")
    
    try:
        from src.enhanced_search_engine import EnhancedSearchEngine
        
        search_engine = EnhancedSearchEngine()
        
        # 测试公司名称提取
        test_titles = [
            "深圳市建筑工程有限公司中标公告",
            "广东省酒店管理集团投资项目",
            "北京房地产开发股份有限公司新项目"
        ]
        
        for title in test_titles:
            company = search_engine._extract_company_name(title, "")
            print(f"✅ 从 '{title}' 提取公司: {company}")
        
        # 测试位置提取
        test_descriptions = [
            "项目位置：深圳市南山区科技园",
            "地址：广州市天河区珠江新城",
            "北京市朝阳区建国门外大街"
        ]
        
        for desc in test_descriptions:
            location = search_engine._extract_location("", desc)
            print(f"✅ 从 '{desc}' 提取位置: {location}")
        
        # 测试招标信息提取
        bidding_content = "深圳市某酒店装修工程招标公告，项目预算500万元，公告日期2024-01-15"
        bidding_info = search_engine._extract_bidding_info("招标公告", bidding_content)
        print(f"✅ 招标信息提取: {bidding_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 信息提取测试失败: {e}")
        traceback.print_exc()
        return False

def test_real_search():
    """测试真实搜索功能"""
    print("\n🌐 测试真实搜索功能...")
    
    try:
        from src.enhanced_search_engine import EnhancedSearchEngine
        
        search_engine = EnhancedSearchEngine()
        
        # 进行小规模真实搜索测试
        print("开始小规模搜索测试...")
        results = search_engine.search_professional_content(
            industry="酒店建设",
            province="广东省", 
            city="深圳市",
            search_types=["bidding"]  # 只测试招标类型
        )
        
        if results.empty:
            print("⚠️ 未获取到搜索结果，这可能是正常的")
            return True  # 不算失败，因为可能是网络或其他原因
        else:
            print(f"✅ 获取到 {len(results)} 条搜索结果")
            
            # 显示结果样本
            print("结果样本:")
            for i, row in results.head(3).iterrows():
                print(f"{i+1}. {row.get('title', 'N/A')[:50]}...")
                print(f"   公司: {row.get('company_name', 'N/A')}")
                print(f"   来源: {row.get('source', 'N/A')}")
                print(f"   类型: {row.get('data_type', 'N/A')}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ 真实搜索测试失败: {e}")
        traceback.print_exc()
        return False

def test_integrated_crawler():
    """测试集成后的爬虫"""
    print("\n🕷️ 测试集成后的爬虫...")
    
    try:
        from src.crawler_engine import CrawlerEngine
        
        crawler = CrawlerEngine()
        
        # 检查增强搜索引擎是否成功集成
        if hasattr(crawler, 'enhanced_search') and crawler.enhanced_search:
            print("✅ 增强搜索引擎成功集成到主爬虫")
        else:
            print("⚠️ 增强搜索引擎未成功集成，但主爬虫仍可工作")
        
        # 测试配置
        crawler_config = {
            'industry': '酒店建设',
            'province': '广东省',
            'city': '深圳市',
            'search_depth': 1,
            'use_baidu': False,
            'use_bing': False,
            'use_business_dirs': False,
            'use_professional_sources': False,  # 禁用直接专业源
            'use_enhanced_search': True  # 启用增强搜索
        }
        
        print("开始集成测试...")
        raw_data = crawler.start_crawling(crawler_config)
        
        if raw_data.empty:
            print("⚠️ 未获取到数据，这可能是正常的")
        else:
            print(f"✅ 获取到 {len(raw_data)} 条数据")
            print("数据样本:")
            print(raw_data.head())
        
        return True
        
    except Exception as e:
        print(f"❌ 集成爬虫测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始增强搜索引擎测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("模块导入", test_enhanced_search_import()))
    test_results.append(("引擎初始化", test_enhanced_search_init()))
    test_results.append(("查询生成", test_query_generation()))
    test_results.append(("内容过滤", test_content_filtering()))
    test_results.append(("信息提取", test_information_extraction()))
    test_results.append(("真实搜索", test_real_search()))
    test_results.append(("集成测试", test_integrated_crawler()))
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("📋 增强搜索引擎测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总测试项: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed >= total * 0.8:  # 80%以上通过率
        print("\n🎉 增强搜索引擎测试表现优秀！")
        print("💡 优势:")
        print("- 通过搜索引擎绕过直接访问限制")
        print("- 能够找到专业网站的相关内容")
        print("- 智能过滤和信息提取")
        return True
    else:
        print(f"\n⚠️ 增强搜索引擎需要进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
