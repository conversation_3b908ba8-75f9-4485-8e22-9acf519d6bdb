#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器指纹伪造功能测试脚本
验证指纹管理器和增强的反检测功能
"""

import asyncio
import sys
import logging
import json
from src.fingerprint_manager import FingerprintManager, get_fingerprint_manager
from src.browser_manager import get_browser_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_fingerprint_generation():
    """测试指纹生成功能"""
    print("🔍 测试指纹生成功能...")
    
    try:
        fingerprint_manager = FingerprintManager()
        
        # 生成不同平台的指纹
        platforms = ["windows", "macos", "linux"]
        
        for platform in platforms:
            fingerprint = fingerprint_manager.generate_fingerprint(platform_hint=platform)
            print(f"✅ {platform.upper()} 指纹生成成功:")
            print(f"   User-Agent: {fingerprint.user_agent[:80]}...")
            print(f"   Platform: {fingerprint.platform}")
            print(f"   Viewport: {fingerprint.viewport}")
            print(f"   Timezone: {fingerprint.timezone}")
            print(f"   Languages: {fingerprint.languages}")
            print(f"   Hardware: {fingerprint.hardware_concurrency} cores, {fingerprint.device_memory}GB RAM")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 指纹生成测试失败: {e}")
        return False

async def test_anti_detection_script():
    """测试反检测脚本生成"""
    print("🔍 测试反检测脚本生成...")
    
    try:
        fingerprint_manager = FingerprintManager()
        fingerprint = fingerprint_manager.generate_fingerprint()
        
        # 生成反检测脚本
        script = fingerprint_manager.get_anti_detection_script(fingerprint)
        
        print("✅ 反检测脚本生成成功")
        print(f"   脚本长度: {len(script)} 字符")
        
        # 检查关键功能
        required_features = [
            "webdriver",
            "platform",
            "hardwareConcurrency",
            "deviceMemory",
            "languages",
            "screen",
            "WebGL",
            "Canvas",
            "plugins"
        ]
        
        missing_features = []
        for feature in required_features:
            if feature not in script:
                missing_features.append(feature)
        
        if missing_features:
            print(f"⚠️ 缺少功能: {missing_features}")
        else:
            print("✅ 所有关键反检测功能都已包含")
        
        return len(missing_features) == 0
        
    except Exception as e:
        print(f"❌ 反检测脚本测试失败: {e}")
        return False

async def test_context_options():
    """测试上下文选项生成"""
    print("🔍 测试上下文选项生成...")
    
    try:
        fingerprint_manager = FingerprintManager()
        fingerprint = fingerprint_manager.generate_fingerprint()
        
        # 获取上下文选项
        options = fingerprint_manager.get_context_options(fingerprint)
        
        print("✅ 上下文选项生成成功")
        print(f"   User-Agent: {options['user_agent'][:80]}...")
        print(f"   Viewport: {options['viewport']}")
        print(f"   Locale: {options['locale']}")
        print(f"   Timezone: {options['timezone_id']}")
        
        # 检查必要的头部
        headers = options.get('extra_http_headers', {})
        required_headers = ['Accept-Language', 'Accept-Encoding', 'Sec-Ch-Ua']
        
        missing_headers = []
        for header in required_headers:
            if header not in headers:
                missing_headers.append(header)
        
        if missing_headers:
            print(f"⚠️ 缺少头部: {missing_headers}")
        else:
            print("✅ 所有必要的HTTP头部都已包含")
        
        return len(missing_headers) == 0
        
    except Exception as e:
        print(f"❌ 上下文选项测试失败: {e}")
        return False

async def test_browser_integration():
    """测试浏览器集成"""
    print("🔍 测试浏览器集成...")
    
    try:
        # 创建浏览器管理器
        browser_manager = get_browser_manager(headless=True)
        await browser_manager.start()
        
        # 创建浏览器实例
        browser = await browser_manager.get_or_create_browser()
        
        # 创建带指纹的上下文
        context = await browser_manager.create_context(browser)
        print("✅ 带指纹的浏览器上下文创建成功")
        
        # 创建页面并测试指纹
        page = await browser_manager.create_page(context)
        
        # 访问测试页面
        await page.goto("http://httpbin.org/headers", timeout=15000)
        
        # 获取页面内容
        content = await page.evaluate('document.body.textContent')
        headers_data = json.loads(content)
        
        print("✅ 页面访问成功")
        print(f"   User-Agent: {headers_data['headers'].get('User-Agent', 'N/A')[:80]}...")
        print(f"   Accept-Language: {headers_data['headers'].get('Accept-Language', 'N/A')}")
        
        # 测试反检测功能
        webdriver_check = await page.evaluate('navigator.webdriver')
        platform_check = await page.evaluate('navigator.platform')
        languages_check = await page.evaluate('navigator.languages')
        
        print(f"   WebDriver检测: {webdriver_check}")
        print(f"   Platform: {platform_check}")
        print(f"   Languages: {languages_check}")
        
        # 清理
        await page.close()
        await browser_manager.close_context(context)
        
        from src.browser_manager import cleanup_browser_manager
        await cleanup_browser_manager()
        
        # 检查反检测效果
        success = webdriver_check is None or webdriver_check == False
        
        if success:
            print("✅ 反检测功能正常工作")
        else:
            print("⚠️ 反检测功能可能需要改进")
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器集成测试失败: {e}")
        return False

async def test_fingerprint_consistency():
    """测试指纹一致性"""
    print("🔍 测试指纹一致性...")
    
    try:
        fingerprint_manager = FingerprintManager()
        
        # 生成多个指纹并检查一致性
        fingerprints = []
        for i in range(5):
            fp = fingerprint_manager.generate_fingerprint()
            fingerprints.append(fp)
        
        # 检查指纹的多样性
        user_agents = set(fp.user_agent for fp in fingerprints)
        viewports = set(f"{fp.viewport['width']}x{fp.viewport['height']}" for fp in fingerprints)
        timezones = set(fp.timezone for fp in fingerprints)
        
        print(f"✅ 生成了 {len(fingerprints)} 个指纹")
        print(f"   User-Agent 多样性: {len(user_agents)}/{len(fingerprints)}")
        print(f"   Viewport 多样性: {len(viewports)}/{len(fingerprints)}")
        print(f"   Timezone 多样性: {len(timezones)}/{len(fingerprints)}")
        
        # 检查指纹内部一致性
        for i, fp in enumerate(fingerprints):
            # 检查viewport不超过screen
            if fp.viewport['width'] > fp.screen['width'] or fp.viewport['height'] > fp.screen['height']:
                print(f"⚠️ 指纹 {i+1} viewport超过screen尺寸")
                return False
            
            # 检查平台与User-Agent一致性
            if "Windows" in fp.user_agent and fp.platform != "Win32":
                print(f"⚠️ 指纹 {i+1} 平台与User-Agent不一致")
                return False
        
        print("✅ 所有指纹内部一致性检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 指纹一致性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("浏览器指纹伪造功能测试")
    print("=" * 60)
    
    async def run_all_tests():
        # 测试指纹生成
        test1 = await test_fingerprint_generation()
        
        # 测试反检测脚本
        test2 = await test_anti_detection_script()
        
        # 测试上下文选项
        test3 = await test_context_options()
        
        # 测试浏览器集成
        test4 = await test_browser_integration()
        
        # 测试指纹一致性
        test5 = await test_fingerprint_consistency()
        
        return test1, test2, test3, test4, test5
    
    # 运行所有测试
    results = asyncio.run(run_all_tests())
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 4:  # 至少4个测试通过
        print("✅ 浏览器指纹伪造功能基本正常")
        print("💡 建议：可以在实际爬虫中测试反检测效果")
        sys.exit(0)
    else:
        print("❌ 浏览器指纹伪造功能测试失败，需要检查实现")
        sys.exit(1)

if __name__ == "__main__":
    main()
