#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP真实数据爬虫
使用HTTP请求方式爬取真实企业和招投标数据
"""

import sys
import os
import time
import re
import json
import requests
from pathlib import Path
from typing import Dict, List, Any
from urllib.parse import quote, urljoin
import pandas as pd
from bs4 import BeautifulSoup
import random

class HTTPRealCrawler:
    """HTTP真实数据爬虫"""
    
    def __init__(self):
        """初始化爬虫"""
        self.session = requests.Session()
        self.setup_session()
        
        print("🚀 HTTP真实数据爬虫初始化完成")
        print("📌 使用HTTP请求方式，无需浏览器驱动")
        print("✅ 专门收集真实数据，不包含任何模拟数据")
    
    def setup_session(self):
        """设置HTTP会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        }
        self.session.headers.update(headers)
        self.session.timeout = 30
        
        # 设置重试机制
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def crawl_ccgp_data(self, keyword: str, region: str = None, max_pages: int = 3) -> List[Dict[str, Any]]:
        """从中国政府采购网爬取真实采购信息"""
        print(f"🔍 正在从中国政府采购网爬取: {keyword}")
        
        projects = []
        
        try:
            for page in range(1, max_pages + 1):
                print(f"📄 正在爬取第 {page} 页...")
                
                # 构建搜索参数
                search_url = "http://search.ccgp.gov.cn/bxsearch"
                params = {
                    'searchtype': '1',
                    'page_index': str(page),
                    'bidSort': '0',
                    'buyerName': '',
                    'projectId': '',
                    'pinMu': '0',
                    'bidType': '0',
                    'dbselect': 'bidx',
                    'kw': keyword,
                    'start_time': '',
                    'end_time': '',
                    'timeType': '6',
                    'displayZone': region or '',
                    'zoneId': '',
                    'pppStatus': '0',
                    'agentName': ''
                }
                
                try:
                    print(f"📡 请求URL: {search_url}")
                    response = self.session.get(search_url, params=params)
                    
                    if response.status_code == 200:
                        page_projects = self._parse_ccgp_response(response.text)
                        projects.extend(page_projects)
                        print(f"✅ 第 {page} 页获取到 {len(page_projects)} 条真实数据")
                    else:
                        print(f"❌ 第 {page} 页请求失败: {response.status_code}")
                        
                except Exception as e:
                    print(f"❌ 第 {page} 页请求异常: {e}")
                
                # 随机延迟避免被封
                time.sleep(random.uniform(2, 4))
            
            print(f"🎉 中国政府采购网爬取完成，共获取 {len(projects)} 条真实采购信息")
            return projects
            
        except Exception as e:
            print(f"❌ 中国政府采购网爬取失败: {e}")
            return projects
    
    def _parse_ccgp_response(self, html: str) -> List[Dict[str, Any]]:
        """解析中国政府采购网响应数据"""
        projects = []
        
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找项目列表
            project_items = soup.find_all('tr')
            
            for item in project_items:
                try:
                    # 查找项目标题链接
                    title_link = item.find('a')
                    if not title_link:
                        continue
                    
                    project_title = title_link.get_text(strip=True)
                    detail_url = title_link.get('href', '')
                    
                    # 过滤掉太短的标题
                    if len(project_title) < 10:
                        continue
                    
                    # 提取表格中的其他信息
                    cells = item.find_all('td')
                    
                    publish_date = ""
                    purchasing_unit = ""
                    project_amount = ""
                    
                    for cell in cells:
                        cell_text = cell.get_text(strip=True)
                        
                        # 提取日期
                        if re.match(r'\d{4}-\d{2}-\d{2}', cell_text):
                            publish_date = cell_text
                        
                        # 提取采购单位
                        if '采购人' in cell_text or len(cell_text) > 5 and '公司' in cell_text:
                            purchasing_unit = cell_text
                        
                        # 提取金额
                        if '万元' in cell_text or '元' in cell_text:
                            amount_match = re.search(r'[\d,]+\.?\d*\s*[万元|元]', cell_text)
                            if amount_match:
                                project_amount = amount_match.group()
                    
                    # 构建完整的详情URL
                    if detail_url and not detail_url.startswith('http'):
                        detail_url = urljoin('http://www.ccgp.gov.cn', detail_url)
                    
                    if project_title and len(project_title) > 10:
                        project_info = {
                            'project_title': project_title,
                            'project_type': '政府采购',
                            'purchasing_unit': purchasing_unit,
                            'project_amount': project_amount,
                            'publish_date': publish_date,
                            'detail_url': detail_url,
                            'source': '中国政府采购网',
                            'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                            'data_type': 'real_bidding_data'
                        }
                        projects.append(project_info)
                        
                except Exception as e:
                    continue  # 跳过解析失败的项目
            
        except Exception as e:
            print(f"❌ 响应解析失败: {e}")
        
        return projects
    
    def crawl_enterprise_info_api(self, keyword: str, region: str = None, max_results: int = 20) -> List[Dict[str, Any]]:
        """通过API方式获取企业信息"""
        print(f"🔍 正在通过API获取企业信息: {keyword}")
        
        companies = []
        
        try:
            # 使用公开的企业信息API（示例）
            # 注意：实际使用时需要替换为真实可用的API
            api_urls = [
                "https://api.qichacha.com/ECIV4/Search",  # 企查查API
                "https://open.api.tianyancha.com/services/open/search/2.0",  # 天眼查API
            ]
            
            for api_url in api_urls:
                try:
                    # 构建API请求参数
                    params = {
                        'key': keyword,
                        'pageNum': 1,
                        'pageSize': max_results
                    }
                    
                    if region:
                        params['province'] = region
                    
                    print(f"📡 请求API: {api_url}")
                    
                    # 发送API请求
                    response = self.session.get(api_url, params=params)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            api_companies = self._parse_api_response(data, api_url)
                            companies.extend(api_companies)
                            print(f"✅ 从API获取到 {len(api_companies)} 条企业数据")
                        except json.JSONDecodeError:
                            print(f"⚠️ API响应不是有效的JSON格式")
                    else:
                        print(f"⚠️ API请求失败: {response.status_code}")
                        
                except Exception as e:
                    print(f"⚠️ API请求异常: {e}")
                    continue
                
                # 延迟避免API限制
                time.sleep(1)
            
            # 如果API不可用，使用备用方案
            if not companies:
                print("⚠️ API方式获取失败，使用备用数据源...")
                companies = self._get_backup_enterprise_data(keyword, region)
            
            print(f"🎉 企业信息获取完成，共获取 {len(companies)} 条真实企业数据")
            return companies
            
        except Exception as e:
            print(f"❌ 企业信息获取失败: {e}")
            return []
    
    def _parse_api_response(self, data: Dict[str, Any], api_url: str) -> List[Dict[str, Any]]:
        """解析API响应数据"""
        companies = []
        
        try:
            # 根据不同API的响应格式解析数据
            if 'qichacha' in api_url:
                # 企查查API响应格式
                if 'Result' in data and data['Result']:
                    for item in data['Result']:
                        company_info = {
                            'company_name': item.get('Name', ''),
                            'legal_representative': item.get('OperName', ''),
                            'registration_capital': item.get('RegistCapi', ''),
                            'establishment_date': item.get('StartDate', ''),
                            'business_status': item.get('Status', ''),
                            'source': '企查查API',
                            'data_type': 'real_enterprise_data'
                        }
                        companies.append(company_info)
            
            elif 'tianyancha' in api_url:
                # 天眼查API响应格式
                if 'result' in data and data['result']:
                    for item in data['result']:
                        company_info = {
                            'company_name': item.get('name', ''),
                            'legal_representative': item.get('legalPersonName', ''),
                            'registration_capital': item.get('regCapital', ''),
                            'establishment_date': item.get('estiblishTime', ''),
                            'business_status': item.get('regStatus', ''),
                            'source': '天眼查API',
                            'data_type': 'real_enterprise_data'
                        }
                        companies.append(company_info)
            
        except Exception as e:
            print(f"❌ API响应解析失败: {e}")
        
        return companies
    
    def _get_backup_enterprise_data(self, keyword: str, region: str = None) -> List[Dict[str, Any]]:
        """备用企业数据获取方案"""
        print("🔄 使用备用方案获取企业数据...")
        
        # 这里可以实现其他数据获取方式
        # 例如：爬取公开的企业名录网站
        
        companies = []
        
        try:
            # 示例：从工商局公开信息网站获取数据
            backup_urls = [
                "http://www.gsxt.gov.cn",  # 国家企业信用信息公示系统
                "http://www.saic.gov.cn"   # 国家工商行政管理总局
            ]
            
            for url in backup_urls:
                try:
                    print(f"📡 尝试备用数据源: {url}")
                    
                    # 这里需要根据具体网站实现爬取逻辑
                    # 由于网站结构复杂，这里提供框架
                    
                    response = self.session.get(url, timeout=10)
                    if response.status_code == 200:
                        print(f"✅ 成功访问 {url}")
                        # 实际项目中需要解析HTML内容
                        
                except Exception as e:
                    print(f"⚠️ 备用数据源访问失败: {e}")
                    continue
            
        except Exception as e:
            print(f"❌ 备用方案失败: {e}")
        
        return companies
    
    def crawl_comprehensive_real_data(self, keyword: str, region: str = None, max_results: int = 50) -> Dict[str, List[Dict[str, Any]]]:
        """综合爬取真实数据"""
        print(f"\n🎯 开始综合真实数据爬取")
        print(f"关键词: {keyword}")
        print(f"地区: {region or '全国'}")
        print(f"目标数量: {max_results}")
        print("=" * 80)
        
        results = {
            'enterprises': [],
            'bidding_projects': [],
            'summary': {}
        }
        
        # 1. 爬取企业数据
        print("\n🔸 第一阶段: 爬取真实企业数据")
        enterprises = self.crawl_enterprise_info_api(keyword, region, max_results // 2)
        results['enterprises'] = enterprises
        
        # 2. 爬取招投标数据
        print("\n🔸 第二阶段: 爬取真实招投标数据")
        bidding_projects = self.crawl_ccgp_data(keyword, region, 3)
        results['bidding_projects'] = bidding_projects
        
        # 3. 生成汇总信息
        results['summary'] = {
            'keyword': keyword,
            'region': region or '全国',
            'enterprise_count': len(enterprises),
            'bidding_project_count': len(bidding_projects),
            'total_count': len(enterprises) + len(bidding_projects),
            'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'data_sources': ['中国政府采购网', '企业信息API'],
            'data_verification': '所有数据均来自真实官方或权威商业平台'
        }
        
        print(f"\n📊 真实数据爬取完成:")
        print(f"  企业数据: {len(enterprises)} 条")
        print(f"  招投标数据: {len(bidding_projects)} 条")
        print(f"  总计: {len(enterprises) + len(bidding_projects)} 条")
        print(f"  数据来源: 真实官方网站和API")
        
        return results
    
    def save_real_data(self, data: Dict[str, Any], filename_prefix: str = None):
        """保存真实数据"""
        if not filename_prefix:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename_prefix = f"real_data_{timestamp}"
        
        try:
            # 保存完整数据
            full_filename = f"{filename_prefix}_complete.json"
            with open(full_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ 完整真实数据已保存: {full_filename}")
            
            # 分别保存企业数据
            if data['enterprises']:
                enterprise_filename = f"{filename_prefix}_enterprises.csv"
                df = pd.DataFrame(data['enterprises'])
                df.to_csv(enterprise_filename, index=False, encoding='utf-8-sig')
                print(f"✅ 真实企业数据已保存: {enterprise_filename}")
            
            # 分别保存招投标数据
            if data['bidding_projects']:
                bidding_filename = f"{filename_prefix}_bidding.csv"
                df = pd.DataFrame(data['bidding_projects'])
                df.to_csv(bidding_filename, index=False, encoding='utf-8-sig')
                print(f"✅ 真实招投标数据已保存: {bidding_filename}")
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")

def main():
    """主函数"""
    crawler = HTTPRealCrawler()
    
    try:
        # 测试真实数据爬取
        test_cases = [
            {"keyword": "建筑工程", "region": "北京"},
            {"keyword": "酒店管理", "region": "上海"},
            {"keyword": "房地产开发", "region": "深圳"}
        ]
        
        for case in test_cases:
            print(f"\n{'='*100}")
            print(f"🧪 测试真实数据爬取: {case['keyword']} - {case['region']}")
            print(f"{'='*100}")
            
            # 爬取真实数据
            results = crawler.crawl_comprehensive_real_data(
                keyword=case['keyword'],
                region=case['region'],
                max_results=30
            )
            
            # 显示结果
            if results['enterprises'] or results['bidding_projects']:
                print(f"\n📋 爬取到的真实数据:")
                
                # 显示企业数据
                for i, company in enumerate(results['enterprises'][:3], 1):
                    print(f"  企业{i}: {company['company_name']}")
                    print(f"         来源: {company['source']}")
                
                # 显示招投标数据
                for i, project in enumerate(results['bidding_projects'][:3], 1):
                    print(f"  项目{i}: {project['project_title'][:50]}...")
                    print(f"         来源: {project['source']}")
                
                # 保存数据
                filename_prefix = f"real_data_{case['keyword']}_{case['region']}"
                crawler.save_real_data(results, filename_prefix)
            else:
                print("❌ 未获取到真实数据")
    
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
