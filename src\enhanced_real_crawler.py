#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版真实数据爬虫
通过多种策略获取真实的企业信息，而不是演示数据
"""

import requests
import time
import random
import re
import json
import pandas as pd
from urllib.parse import urljoin, urlparse, quote
from typing import List, Dict, Any, Optional
import yaml
from pathlib import Path
from datetime import datetime, timedelta

try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None

try:
    from fake_useragent import UserAgent
except ImportError:
    UserAgent = None

from .utils.logger import get_logger
from .region_manager import RegionManager

class EnhancedRealCrawler:
    """增强版真实数据爬虫类"""
    
    def __init__(self):
        """初始化增强爬虫"""
        self.logger = get_logger(__name__)
        self.ua = UserAgent() if UserAgent else None
        self.session = requests.Session()
        self.region_manager = RegionManager()
        
        # 设置更真实的请求头
        self._setup_realistic_session()
        
        # 真实数据源配置
        self.real_sources = {
            'search_engines': [
                'https://www.baidu.com/s',
                'https://www.sogou.com/web',
                'https://www.so.com/s'
            ],
            'business_directories': [
                'https://www.11467.com/search/',
                'https://www.qiyeku.com/search/',
                'https://www.b2b168.com/s-'
            ],
            'industry_sites': [
                'https://www.chinabidding.com.cn/search/',
                'https://www.zbytb.com/search/',
                'https://www.okcis.cn/search/'
            ]
        }
        
    def _setup_realistic_session(self):
        """设置更真实的会话"""
        headers = {
            'User-Agent': self.ua.random if self.ua else 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
            'DNT': '1'
        }
        self.session.headers.update(headers)

        # 设置超时
        self.session.timeout = 30

        # 禁用SSL验证以避免证书问题
        self.session.verify = False

        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
    def search_real_business_data(self, industry: str, province: str = None, city: str = None, 
                                 max_results: int = 50) -> pd.DataFrame:
        """搜索真实的企业数据"""
        self.logger.info(f"开始搜索真实企业数据: {industry}")
        
        all_results = []
        
        # 1. 搜索引擎爬取
        search_results = self._search_from_search_engines(industry, province, city, max_results//3)
        all_results.extend(search_results)
        
        # 2. 企业目录网站爬取
        directory_results = self._search_from_business_directories(industry, province, city, max_results//3)
        all_results.extend(directory_results)
        
        # 3. 行业网站爬取
        industry_results = self._search_from_industry_sites(industry, province, city, max_results//3)
        all_results.extend(industry_results)
        
        # 转换为DataFrame
        df = pd.DataFrame(all_results)
        
        # 数据清洗和去重
        if not df.empty:
            df = self._clean_and_deduplicate(df)
        
        self.logger.info(f"真实数据搜索完成，获得 {len(df)} 条数据")
        return df
    
    def _search_from_search_engines(self, industry: str, province: str = None, city: str = None, 
                                   max_results: int = 20) -> List[Dict]:
        """从搜索引擎获取真实数据"""
        results = []
        
        # 构建搜索关键词
        keywords = self._build_search_keywords(industry, province, city)
        
        for search_engine in self.real_sources['search_engines']:
            for keyword in keywords[:3]:  # 限制关键词数量
                try:
                    self.logger.info(f"搜索引擎查询: {keyword}")
                    
                    # 构建搜索URL
                    if 'baidu.com' in search_engine:
                        search_url = f"{search_engine}?wd={quote(keyword)}"
                    elif 'sogou.com' in search_engine:
                        search_url = f"{search_engine}?query={quote(keyword)}"
                    elif 'so.com' in search_engine:
                        search_url = f"{search_engine}?q={quote(keyword)}"
                    else:
                        continue
                    
                    # 发送请求
                    response = self.session.get(search_url)
                    if response.status_code == 200:
                        # 解析搜索结果
                        page_results = self._parse_search_engine_results(
                            response.text, keyword, industry
                        )
                        results.extend(page_results)
                        
                        if len(results) >= max_results:
                            break
                    
                    # 随机延时
                    time.sleep(random.uniform(2, 5))
                    
                except Exception as e:
                    self.logger.error(f"搜索引擎查询出错: {e}")
                    continue
                
                if len(results) >= max_results:
                    break
            
            if len(results) >= max_results:
                break
        
        return results[:max_results]
    
    def _search_from_business_directories(self, industry: str, province: str = None, city: str = None, 
                                        max_results: int = 20) -> List[Dict]:
        """从企业目录网站获取真实数据"""
        results = []
        
        keywords = self._build_search_keywords(industry, province, city)
        
        for directory_url in self.real_sources['business_directories']:
            for keyword in keywords[:2]:  # 限制关键词数量
                try:
                    self.logger.info(f"企业目录查询: {keyword}")
                    
                    # 构建搜索URL
                    search_url = f"{directory_url}{quote(keyword)}"
                    
                    # 发送请求
                    response = self.session.get(search_url)
                    if response.status_code == 200:
                        # 解析企业目录结果
                        page_results = self._parse_business_directory_results(
                            response.text, keyword, industry
                        )
                        results.extend(page_results)
                        
                        if len(results) >= max_results:
                            break
                    
                    # 随机延时
                    time.sleep(random.uniform(3, 6))
                    
                except Exception as e:
                    self.logger.error(f"企业目录查询出错: {e}")
                    continue
                
                if len(results) >= max_results:
                    break
            
            if len(results) >= max_results:
                break
        
        return results[:max_results]

    def _parse_business_directory_results(self, html_content: str, keyword: str, industry: str) -> List[Dict]:
        """解析企业目录搜索结果"""
        results = []

        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 通用的企业信息提取规则
            company_selectors = [
                '.company-item', '.enterprise-item', '.result-item',
                '.company-info', '.business-item', '.search-result',
                '.list-item', '.item', '.company'
            ]

            for selector in company_selectors:
                items = soup.select(selector)
                if items:
                    for item in items[:10]:  # 限制每个选择器的结果数量
                        company_data = self._extract_company_from_element(item, keyword, industry)
                        if company_data:
                            results.append(company_data)
                    break  # 找到有效选择器就停止

            # 如果没有找到结构化数据，尝试从文本中提取
            if not results:
                results = self._extract_companies_from_text(html_content, keyword, industry)

        except Exception as e:
            self.logger.error(f"解析企业目录结果失败: {str(e)}")

        return results[:5]  # 限制返回结果数量

    def _extract_company_from_element(self, element, keyword: str, industry: str) -> Optional[Dict]:
        """从HTML元素中提取企业信息"""
        try:
            # 提取公司名称
            name_selectors = ['.company-name', '.title', 'h3', 'h4', '.name', 'a[title]', '.corp-name']
            company_name = None

            for selector in name_selectors:
                name_elem = element.select_one(selector)
                if name_elem:
                    company_name = name_elem.get_text(strip=True) or name_elem.get('title', '').strip()
                    if company_name and len(company_name) > 3:
                        break

            if not company_name:
                return None

            # 提取其他信息
            contact_elem = element.select_one('.contact, .phone, .tel, .mobile')
            contact = contact_elem.get_text(strip=True) if contact_elem else ''

            address_elem = element.select_one('.address, .location, .addr, .area')
            address = address_elem.get_text(strip=True) if address_elem else ''

            return {
                'company_name': company_name,
                'contact': contact,
                'address': address,
                'industry': industry,
                'source': '企业目录',
                'data_type': 'business_directory',
                'keyword': keyword
            }

        except Exception as e:
            self.logger.error(f"提取企业信息失败: {str(e)}")
            return None

    def _extract_companies_from_text(self, text: str, keyword: str, industry: str) -> List[Dict]:
        """从文本中提取企业信息"""
        results = []

        try:
            # 使用正则表达式匹配企业名称模式
            company_patterns = [
                r'([^，。！？\s]{2,20}(?:有限公司|股份有限公司|集团|企业|公司))',
                r'([^，。！？\s]{2,20}(?:建设|工程|装饰|设计)(?:有限公司|公司))',
                r'([^，。！？\s]{2,20}(?:酒店|宾馆|度假村)(?:管理|投资)?(?:有限公司|公司)?)'
            ]

            for pattern in company_patterns:
                matches = re.findall(pattern, text)
                for match in matches[:3]:  # 限制每个模式的匹配数量
                    if len(match) > 3 and any(word in match for word in keyword.split()):
                        results.append({
                            'company_name': match,
                            'contact': '',
                            'address': '',
                            'industry': industry,
                            'source': '文本提取',
                            'data_type': 'text_extraction',
                            'keyword': keyword
                        })

        except Exception as e:
            self.logger.error(f"文本提取失败: {str(e)}")

        return results[:3]

    def _search_from_industry_sites(self, industry: str, province: str = None, city: str = None,
                                   max_results: int = 20) -> List[Dict]:
        """从行业网站获取真实数据"""
        results = []
        
        keywords = self._build_search_keywords(industry, province, city)
        
        for industry_url in self.real_sources['industry_sites']:
            for keyword in keywords[:2]:  # 限制关键词数量
                try:
                    self.logger.info(f"行业网站查询: {keyword}")
                    
                    # 构建搜索URL
                    search_url = f"{industry_url}{quote(keyword)}"
                    
                    # 发送请求
                    response = self.session.get(search_url)
                    if response.status_code == 200:
                        # 解析行业网站结果
                        page_results = self._parse_industry_site_results(
                            response.text, keyword, industry
                        )
                        results.extend(page_results)

                        if len(results) >= max_results:
                            break

                    # 随机延时
                    time.sleep(random.uniform(3, 6))

                except Exception as e:
                    self.logger.error(f"行业网站查询出错: {e}")
                    continue
                
                if len(results) >= max_results:
                    break
            
            if len(results) >= max_results:
                break
        
        return results[:max_results]
    
    def _build_search_keywords(self, industry: str, province: str = None, city: str = None) -> List[str]:
        """构建搜索关键词"""
        keywords = []
        
        # 基础关键词
        base_keywords = [
            f"{industry} 公司",
            f"{industry} 企业",
            f"{industry} 厂家",
            f"{industry} 供应商"
        ]
        
        # 添加地区信息
        if province or city:
            region = self.region_manager.get_region_display_name(province, city)
            for base in base_keywords:
                keywords.append(f"{region} {base}")
        else:
            keywords.extend(base_keywords)
        
        # 添加行业特定关键词
        if '酒店' in industry:
            keywords.extend([
                f"{industry} 建设公司",
                f"{industry} 装修公司",
                f"{industry} 设计公司"
            ])
        elif '房地产' in industry:
            keywords.extend([
                f"{industry} 开发公司",
                f"{industry} 投资公司",
                f"{industry} 建设公司"
            ])
        
        return keywords[:10]  # 限制关键词数量
    
    def _parse_search_engine_results(self, html_content: str, keyword: str, industry: str) -> List[Dict]:
        """解析搜索引擎结果"""
        results = []
        
        if not BeautifulSoup:
            return self._parse_with_regex(html_content, keyword, industry, 'search_engine')
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找搜索结果项
            result_items = soup.find_all(['div', 'li'], class_=re.compile(r'(result|item)'))
            
            for item in result_items[:10]:  # 限制结果数量
                try:
                    # 提取标题和链接
                    title_elem = item.find(['h3', 'h4', 'a'])
                    if not title_elem:
                        continue
                    
                    title = title_elem.get_text().strip()
                    link = title_elem.get('href', '') if title_elem.name == 'a' else ''
                    
                    # 提取描述
                    desc_elem = item.find(['p', 'span'], class_=re.compile(r'(desc|content|abstract)'))
                    description = desc_elem.get_text().strip() if desc_elem else ''
                    
                    # 验证是否为有效的企业结果
                    if self._is_valid_company_result(title, description, industry):
                        company_info = {
                            'company_name': self._extract_company_name(title),
                            'industry': industry,
                            'website': self._clean_url(link),
                            'description': description[:200],
                            'source': 'search_engine',
                            'search_keyword': keyword,
                            'data_type': 'real_data'
                        }
                        
                        # 尝试提取联系信息
                        contact_info = self._extract_contact_info(description)
                        company_info.update(contact_info)
                        
                        results.append(company_info)
                        
                except Exception as e:
                    self.logger.debug(f"解析搜索结果项出错: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"解析搜索引擎结果出错: {e}")
        
        return results

    def _is_valid_company_result(self, title: str, description: str, industry: str) -> bool:
        """验证是否为有效的企业结果"""
        # 检查必须包含的关键词
        company_keywords = ['公司', '企业', '集团', '有限', '股份', '科技', '实业']
        text = (title + " " + description).lower()

        if not any(keyword in text for keyword in company_keywords):
            return False

        # 排除无关结果
        exclude_keywords = ['招聘', '求职', '新闻', '百科', '知乎', '贴吧', '论坛']
        if any(keyword in text for keyword in exclude_keywords):
            return False

        # 检查是否与目标行业相关
        if industry.lower() not in text:
            return False

        return True

    def _extract_company_name(self, title: str) -> str:
        """从标题中提取公司名称"""
        # 移除常见的后缀
        title = re.sub(r'[-_|].*$', '', title)
        title = re.sub(r'官网|首页|主页', '', title)

        # 提取公司名称模式
        patterns = [
            r'(.+?有限公司)',
            r'(.+?股份有限公司)',
            r'(.+?集团)',
            r'(.+?科技)',
            r'(.+?实业)',
        ]

        for pattern in patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1).strip()

        return title.strip()

    def _clean_url(self, url: str) -> str:
        """清理URL"""
        if not url:
            return ""

        if url.startswith('http'):
            return url
        elif url.startswith('//'):
            return 'https:' + url
        elif url.startswith('/'):
            return url  # 相对URL，需要基础域名
        else:
            return ""

    def _extract_contact_info(self, text: str) -> Dict:
        """提取联系信息"""
        info = {}

        # 提取电话号码
        phone_patterns = [
            r'电话[:：]\s*([0-9\-\s\(\)]{7,20})',
            r'(\d{3,4}[-\s]?\d{7,8})',
            r'(1[3-9]\d{9})'
        ]

        for pattern in phone_patterns:
            match = re.search(pattern, text)
            if match:
                info['phone'] = match.group(1).strip()
                break

        # 提取邮箱
        email_pattern = r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
        email_match = re.search(email_pattern, text)
        if email_match:
            info['email'] = email_match.group(1)

        return info

    def _parse_with_regex(self, html_content: str, keyword: str, industry: str, source: str) -> List[Dict]:
        """使用正则表达式解析HTML内容"""
        results = []

        # 提取公司名称的正则模式
        company_patterns = [
            r'([^<>]*(?:' + keyword + r')[^<>]*(?:公司|企业|集团|有限|股份|科技|实业))',
            r'title="([^"]*(?:' + keyword + r')[^"]*(?:公司|企业|集团|有限|股份|科技|实业)[^"]*)"',
            r'>([^<]*(?:' + keyword + r')[^<]*(?:公司|企业|集团|有限|股份|科技|实业)[^<]*)<'
        ]

        found_companies = set()

        for pattern in company_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                company_name = self._clean_text(match)
                if (self._is_valid_company_result(company_name, '', industry) and
                    company_name not in found_companies and
                    len(company_name) > 5):

                    found_companies.add(company_name)

                    company_info = {
                        'company_name': company_name,
                        'industry': industry,
                        'source': source,
                        'search_keyword': keyword,
                        'data_type': 'real_data',
                        'description': f'{industry}相关企业'
                    }

                    results.append(company_info)

                    if len(results) >= 10:
                        break

        return results

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def _clean_and_deduplicate(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗和去重数据"""
        if df.empty:
            return df

        # 去除重复的公司名称
        df = df.drop_duplicates(subset=['company_name'], keep='first')

        # 清理公司名称
        df['company_name'] = df['company_name'].str.strip()

        # 过滤掉名称过短的记录
        df = df[df['company_name'].str.len() > 5]

        # 重置索引
        df = df.reset_index(drop=True)

        return df

    def _parse_industry_site_results(self, html_content: str, keyword: str, industry: str) -> List[Dict]:
        """解析行业网站搜索结果"""
        results = []

        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 行业网站的企业信息提取规则
            industry_selectors = [
                '.member-item', '.company-list-item', '.enterprise-info',
                '.member-info', '.corp-item', '.business-card',
                '.company-profile', '.member-card', '.enterprise-card'
            ]

            for selector in industry_selectors:
                items = soup.select(selector)
                if items:
                    for item in items[:8]:  # 限制每个选择器的结果数量
                        company_data = self._extract_industry_company_info(item, keyword, industry)
                        if company_data:
                            results.append(company_data)
                    break  # 找到有效选择器就停止

            # 如果没有找到结构化数据，尝试从链接和标题中提取
            if not results:
                results = self._extract_from_links_and_titles(soup, keyword, industry)

        except Exception as e:
            self.logger.error(f"解析行业网站结果失败: {str(e)}")

        return results[:5]  # 限制返回结果数量

    def _extract_industry_company_info(self, element, keyword: str, industry: str) -> Optional[Dict]:
        """从行业网站元素中提取企业信息"""
        try:
            # 提取公司名称 - 行业网站通常有更规范的结构
            name_selectors = [
                '.company-name', '.corp-name', '.member-name', '.enterprise-name',
                '.title', 'h3', 'h4', '.name', 'a[title]', '.business-name'
            ]
            company_name = None

            for selector in name_selectors:
                name_elem = element.select_one(selector)
                if name_elem:
                    company_name = name_elem.get_text(strip=True) or name_elem.get('title', '').strip()
                    if company_name and len(company_name) > 3:
                        break

            if not company_name:
                return None

            # 提取联系信息
            contact_selectors = ['.contact', '.phone', '.tel', '.mobile', '.telephone']
            contact = ''
            for selector in contact_selectors:
                contact_elem = element.select_one(selector)
                if contact_elem:
                    contact = contact_elem.get_text(strip=True)
                    if contact:
                        break

            # 提取地址信息
            address_selectors = ['.address', '.location', '.addr', '.area', '.region']
            address = ''
            for selector in address_selectors:
                address_elem = element.select_one(selector)
                if address_elem:
                    address = address_elem.get_text(strip=True)
                    if address:
                        break

            # 提取业务描述
            desc_selectors = ['.description', '.business-scope', '.main-business', '.intro']
            description = ''
            for selector in desc_selectors:
                desc_elem = element.select_one(selector)
                if desc_elem:
                    description = desc_elem.get_text(strip=True)[:100]  # 限制长度
                    if description:
                        break

            return {
                'company_name': company_name,
                'contact': contact,
                'address': address,
                'description': description,
                'industry': industry,
                'source': '行业网站',
                'data_type': 'industry_site',
                'keyword': keyword
            }

        except Exception as e:
            self.logger.error(f"提取行业企业信息失败: {str(e)}")
            return None

    def _extract_from_links_and_titles(self, soup, keyword: str, industry: str) -> List[Dict]:
        """从链接和标题中提取企业信息"""
        results = []

        try:
            # 查找包含企业关键词的链接
            links = soup.find_all('a', href=True)

            for link in links[:20]:  # 限制检查的链接数量
                title = link.get('title', '') or link.get_text(strip=True)

                if title and len(title) > 3:
                    # 检查是否包含企业相关关键词
                    company_keywords = ['公司', '企业', '集团', '有限', '股份', '建设', '工程', '装饰', '设计']

                    if any(kw in title for kw in company_keywords):
                        # 进一步验证是否与搜索关键词相关
                        if any(word in title for word in keyword.split()):
                            results.append({
                                'company_name': title,
                                'contact': '',
                                'address': '',
                                'description': '',
                                'industry': industry,
                                'source': '链接提取',
                                'data_type': 'link_extraction',
                                'keyword': keyword
                            })

                            if len(results) >= 3:
                                break

        except Exception as e:
            self.logger.error(f"链接提取失败: {str(e)}")

        return results
