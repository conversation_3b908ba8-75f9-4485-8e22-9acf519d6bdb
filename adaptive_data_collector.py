#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应数据收集器
改进信息收集方式，不依赖固定方式，采用多种策略动态收集真实数据
"""

import requests
import time
import json
import re
import random
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin, quote
import pandas as pd
from pathlib import Path

class AdaptiveDataCollector:
    """自适应数据收集器"""
    
    def __init__(self):
        """初始化收集器"""
        self.session = requests.Session()
        self.setup_session()
        
        # 动态数据源发现
        self.discovered_sources = []
        self.working_sources = []
        self.failed_sources = []
        
        print("🔄 自适应数据收集器")
        print("✅ 动态发现和测试数据源")
        print("✅ 多种收集策略并行")
        print("✅ 实时调整收集方式")
        print("❌ 不依赖固定的网站列表")
    
    def setup_session(self):
        """设置HTTP会话"""
        # 随机化User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        headers = {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session.headers.update(headers)
        self.session.timeout = 15
    
    def discover_data_sources(self, keyword: str, region: str = None) -> List[Dict[str, Any]]:
        """动态发现数据源"""
        print(f"\n🔍 动态发现数据源: {keyword}")
        
        discovered = []
        
        # 策略1: 搜索引擎发现
        search_discovered = self._discover_via_search_engines(keyword, region)
        discovered.extend(search_discovered)
        
        # 策略2: 政府网站目录发现
        gov_discovered = self._discover_government_sites(keyword, region)
        discovered.extend(gov_discovered)
        
        # 策略3: 行业协会网站发现
        industry_discovered = self._discover_industry_sites(keyword)
        discovered.extend(industry_discovered)
        
        # 策略4: 新闻媒体网站发现
        news_discovered = self._discover_news_sites(keyword, region)
        discovered.extend(news_discovered)
        
        self.discovered_sources = discovered
        print(f"✅ 发现 {len(discovered)} 个潜在数据源")
        
        return discovered
    
    def _discover_via_search_engines(self, keyword: str, region: str = None) -> List[Dict[str, Any]]:
        """通过搜索引擎发现数据源"""
        print("🔎 通过搜索引擎发现数据源...")
        
        # 构建搜索查询
        search_queries = [
            f"{keyword} 招标 {region or ''}",
            f"{keyword} 企业 {region or ''}",
            f"{keyword} 项目 {region or ''}",
            f"{keyword} 公司 {region or ''}"
        ]
        
        discovered = []
        
        # 使用可访问的搜索API或网站
        search_engines = [
            "https://www.bing.com/search?q=",
            "https://duckduckgo.com/?q=",
        ]
        
        for query in search_queries[:2]:  # 限制查询数量
            for search_engine in search_engines:
                try:
                    search_url = f"{search_engine}{quote(query)}"
                    print(f"📡 搜索: {query}")
                    
                    response = self.session.get(search_url, timeout=10)
                    if response.status_code == 200:
                        # 从搜索结果中提取链接
                        soup = BeautifulSoup(response.text, 'html.parser')
                        links = soup.find_all('a', href=True)
                        
                        for link in links[:5]:  # 只取前5个链接
                            href = link.get('href')
                            if href and ('gov.cn' in href or 'bidding' in href or 'tender' in href):
                                discovered.append({
                                    'url': href,
                                    'source_type': 'search_discovered',
                                    'discovery_method': 'search_engine',
                                    'relevance_score': self._calculate_relevance(href, keyword),
                                    'discovered_via': search_engine
                                })
                    
                    time.sleep(random.uniform(2, 4))
                    
                except Exception as e:
                    print(f"⚠️ 搜索引擎发现失败: {e}")
                    continue
        
        return discovered
    
    def _discover_government_sites(self, keyword: str, region: str = None) -> List[Dict[str, Any]]:
        """发现政府网站"""
        print("🏛️ 发现政府网站...")
        
        # 根据地区动态构建政府网站列表
        gov_sites = []
        
        if region:
            # 根据地区构建政府网站
            region_sites = {
                '北京': ['beijing.gov.cn', 'bjjs.gov.cn', 'bjzfcg.gov.cn'],
                '上海': ['shanghai.gov.cn', 'shzfcg.gov.cn'],
                '深圳': ['sz.gov.cn', 'szzfcg.cn'],
                '广州': ['gz.gov.cn', 'gzgov.gov.cn']
            }
            
            for city, sites in region_sites.items():
                if city in region:
                    for site in sites:
                        gov_sites.append(f"http://{site}")
        
        # 通用政府网站
        gov_sites.extend([
            "http://www.gov.cn",
            "http://www.ccgp.gov.cn",
            "http://www.cebpubservice.com"
        ])
        
        discovered = []
        for site in gov_sites:
            discovered.append({
                'url': site,
                'source_type': 'government',
                'discovery_method': 'government_directory',
                'relevance_score': 0.8,
                'site_category': 'official_government'
            })
        
        return discovered
    
    def _discover_industry_sites(self, keyword: str) -> List[Dict[str, Any]]:
        """发现行业网站"""
        print("🏭 发现行业网站...")
        
        # 根据关键词动态匹配行业网站
        industry_mapping = {
            '建筑': ['chinabidding.com.cn', 'ccea.org.cn', 'cscec.com'],
            '酒店': ['chinahotel.org.cn', 'hotels.cn'],
            '房地产': ['creb.org.cn', 'fdc.com.cn'],
            '工程': ['cces.org.cn', 'chinabidding.com.cn']
        }
        
        discovered = []
        for industry, sites in industry_mapping.items():
            if industry in keyword:
                for site in sites:
                    discovered.append({
                        'url': f"http://{site}",
                        'source_type': 'industry',
                        'discovery_method': 'industry_mapping',
                        'relevance_score': 0.9,
                        'industry_category': industry
                    })
        
        return discovered
    
    def _discover_news_sites(self, keyword: str, region: str = None) -> List[Dict[str, Any]]:
        """发现新闻媒体网站"""
        print("📰 发现新闻媒体网站...")
        
        news_sites = [
            "http://www.xinhuanet.com",
            "http://www.people.com.cn",
            "http://www.chinanews.com"
        ]
        
        discovered = []
        for site in news_sites:
            discovered.append({
                'url': site,
                'source_type': 'news_media',
                'discovery_method': 'news_directory',
                'relevance_score': 0.6,
                'media_type': 'official_news'
            })
        
        return discovered
    
    def _calculate_relevance(self, url: str, keyword: str) -> float:
        """计算URL与关键词的相关性"""
        relevance = 0.0
        
        # 基于URL内容计算相关性
        if keyword.lower() in url.lower():
            relevance += 0.3
        
        # 基于域名类型
        if 'gov.cn' in url:
            relevance += 0.4
        elif 'bidding' in url or 'tender' in url:
            relevance += 0.3
        elif '.org' in url:
            relevance += 0.2
        
        return min(relevance, 1.0)
    
    def test_and_validate_sources(self, sources: List[Dict[str, Any]]) -> Dict[str, List]:
        """测试和验证数据源"""
        print(f"\n🧪 测试和验证 {len(sources)} 个数据源...")
        
        working = []
        failed = []
        
        for source in sources:
            try:
                print(f"🌐 测试: {source['url']}")
                
                response = self.session.get(source['url'], timeout=10)
                
                if response.status_code == 200:
                    # 验证页面内容质量
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 基本质量检查
                    title = soup.find('title')
                    content_length = len(response.text)
                    
                    if title and content_length > 1000:
                        source['status'] = 'working'
                        source['title'] = title.get_text(strip=True)
                        source['content_length'] = content_length
                        source['test_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
                        working.append(source)
                        print(f"✅ 可用: {source['url']}")
                    else:
                        source['status'] = 'low_quality'
                        failed.append(source)
                        print(f"⚠️ 质量低: {source['url']}")
                else:
                    source['status'] = 'inaccessible'
                    source['status_code'] = response.status_code
                    failed.append(source)
                    print(f"❌ 不可访问: {source['url']} ({response.status_code})")
                
            except Exception as e:
                source['status'] = 'error'
                source['error'] = str(e)
                failed.append(source)
                print(f"❌ 错误: {source['url']} - {e}")
            
            # 随机延迟
            time.sleep(random.uniform(1, 3))
        
        self.working_sources = working
        self.failed_sources = failed
        
        print(f"📊 测试结果: {len(working)} 个可用, {len(failed)} 个失败")
        return {'working': working, 'failed': failed}
    
    def extract_data_adaptively(self, sources: List[Dict[str, Any]], keyword: str) -> List[Dict[str, Any]]:
        """自适应数据提取"""
        print(f"\n📊 自适应数据提取: {keyword}")
        
        extracted_data = []
        
        for source in sources:
            try:
                print(f"🔍 提取数据: {source['url']}")
                
                response = self.session.get(source['url'], timeout=15)
                if response.status_code != 200:
                    continue
                
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 根据网站类型选择提取策略
                if source['source_type'] == 'government':
                    data = self._extract_government_data(soup, source, keyword)
                elif source['source_type'] == 'industry':
                    data = self._extract_industry_data(soup, source, keyword)
                elif source['source_type'] == 'news_media':
                    data = self._extract_news_data(soup, source, keyword)
                else:
                    data = self._extract_generic_data(soup, source, keyword)
                
                extracted_data.extend(data)
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                print(f"⚠️ 数据提取失败 {source['url']}: {e}")
                continue
        
        print(f"✅ 总共提取 {len(extracted_data)} 条数据")
        return extracted_data
    
    def _extract_government_data(self, soup: BeautifulSoup, source: Dict, keyword: str) -> List[Dict[str, Any]]:
        """提取政府网站数据"""
        data = []
        
        # 查找可能的招标信息
        potential_items = soup.find_all(['div', 'li', 'tr'], class_=re.compile(r'(notice|tender|bid|project)', re.I))
        
        for item in potential_items[:5]:
            text = item.get_text(strip=True)
            if keyword in text and len(text) > 20:
                
                # 尝试提取链接
                link = item.find('a')
                detail_url = link.get('href') if link else ''
                if detail_url and not detail_url.startswith('http'):
                    detail_url = urljoin(source['url'], detail_url)
                
                data.append({
                    'title': text[:100],
                    'source_url': source['url'],
                    'detail_url': detail_url,
                    'source_type': 'government',
                    'extraction_method': 'government_pattern',
                    'keyword': keyword,
                    'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'data_quality': 'extracted_from_real_site'
                })
        
        return data
    
    def _extract_industry_data(self, soup: BeautifulSoup, source: Dict, keyword: str) -> List[Dict[str, Any]]:
        """提取行业网站数据"""
        data = []
        
        # 查找行业相关信息
        potential_items = soup.find_all(['div', 'article', 'section'], class_=re.compile(r'(news|info|item)', re.I))
        
        for item in potential_items[:3]:
            text = item.get_text(strip=True)
            if keyword in text and len(text) > 30:
                
                title_elem = item.find(['h1', 'h2', 'h3', 'h4'])
                title = title_elem.get_text(strip=True) if title_elem else text[:50]
                
                data.append({
                    'title': title,
                    'content_preview': text[:200],
                    'source_url': source['url'],
                    'source_type': 'industry',
                    'extraction_method': 'industry_pattern',
                    'keyword': keyword,
                    'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'data_quality': 'extracted_from_real_site'
                })
        
        return data
    
    def _extract_news_data(self, soup: BeautifulSoup, source: Dict, keyword: str) -> List[Dict[str, Any]]:
        """提取新闻网站数据"""
        data = []
        
        # 查找新闻文章
        articles = soup.find_all(['article', 'div'], class_=re.compile(r'(article|news)', re.I))
        
        for article in articles[:2]:
            text = article.get_text(strip=True)
            if keyword in text:
                
                headline = article.find(['h1', 'h2', 'h3'])
                headline_text = headline.get_text(strip=True) if headline else text[:50]
                
                data.append({
                    'headline': headline_text,
                    'content_preview': text[:150],
                    'source_url': source['url'],
                    'source_type': 'news_media',
                    'extraction_method': 'news_pattern',
                    'keyword': keyword,
                    'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'data_quality': 'extracted_from_real_site'
                })
        
        return data
    
    def _extract_generic_data(self, soup: BeautifulSoup, source: Dict, keyword: str) -> List[Dict[str, Any]]:
        """通用数据提取"""
        data = []
        
        # 通用文本提取
        all_text = soup.get_text()
        if keyword in all_text:
            
            # 查找包含关键词的段落
            paragraphs = soup.find_all('p')
            relevant_paragraphs = [p.get_text(strip=True) for p in paragraphs if keyword in p.get_text()]
            
            if relevant_paragraphs:
                data.append({
                    'content': ' '.join(relevant_paragraphs[:3]),
                    'source_url': source['url'],
                    'source_type': 'generic',
                    'extraction_method': 'generic_pattern',
                    'keyword': keyword,
                    'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'data_quality': 'extracted_from_real_site'
                })
        
        return data
    
    def comprehensive_adaptive_collection(self, keyword: str, region: str = None) -> Dict[str, Any]:
        """综合自适应数据收集"""
        print(f"\n🎯 开始综合自适应数据收集")
        print(f"关键词: {keyword}")
        print(f"地区: {region or '全国'}")
        print("=" * 80)
        
        # 1. 动态发现数据源
        discovered_sources = self.discover_data_sources(keyword, region)
        
        # 2. 测试和验证数据源
        validation_results = self.test_and_validate_sources(discovered_sources)
        
        # 3. 自适应数据提取
        extracted_data = self.extract_data_adaptively(validation_results['working'], keyword)
        
        # 4. 生成结果
        results = {
            'keyword': keyword,
            'region': region or '全国',
            'discovered_sources_count': len(discovered_sources),
            'working_sources_count': len(validation_results['working']),
            'failed_sources_count': len(validation_results['failed']),
            'extracted_data_count': len(extracted_data),
            'discovered_sources': discovered_sources,
            'working_sources': validation_results['working'],
            'failed_sources': validation_results['failed'],
            'extracted_data': extracted_data,
            'collection_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'collection_method': 'adaptive_multi_strategy'
        }
        
        print(f"\n📊 自适应收集完成:")
        print(f"  发现数据源: {len(discovered_sources)} 个")
        print(f"  可用数据源: {len(validation_results['working'])} 个")
        print(f"  提取数据: {len(extracted_data)} 条")
        print(f"  成功率: {len(validation_results['working'])/len(discovered_sources)*100:.1f}%")
        
        return results
    
    def save_adaptive_results(self, results: Dict[str, Any], filename_prefix: str = None):
        """保存自适应收集结果"""
        if not filename_prefix:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename_prefix = f"adaptive_collection_{timestamp}"
        
        try:
            # 保存完整结果
            full_filename = f"{filename_prefix}_complete.json"
            with open(full_filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"✅ 完整结果已保存: {full_filename}")
            
            # 保存提取的数据
            if results['extracted_data']:
                data_filename = f"{filename_prefix}_data.csv"
                df = pd.DataFrame(results['extracted_data'])
                df.to_csv(data_filename, index=False, encoding='utf-8-sig')
                print(f"✅ 提取数据已保存: {data_filename}")
            
            # 保存数据源分析
            sources_filename = f"{filename_prefix}_sources.csv"
            all_sources = results['working_sources'] + results['failed_sources']
            if all_sources:
                df_sources = pd.DataFrame(all_sources)
                df_sources.to_csv(sources_filename, index=False, encoding='utf-8-sig')
                print(f"✅ 数据源分析已保存: {sources_filename}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

def main():
    """主函数"""
    collector = AdaptiveDataCollector()
    
    try:
        # 测试自适应收集
        test_cases = [
            {"keyword": "建筑工程", "region": "北京"},
            {"keyword": "酒店管理", "region": "上海"}
        ]
        
        for case in test_cases:
            print(f"\n{'='*100}")
            print(f"🧪 测试自适应收集: {case['keyword']} - {case['region']}")
            print(f"{'='*100}")
            
            # 进行自适应收集
            results = collector.comprehensive_adaptive_collection(
                keyword=case['keyword'],
                region=case['region']
            )
            
            # 保存结果
            filename_prefix = f"adaptive_{case['keyword']}_{case['region']}"
            collector.save_adaptive_results(results, filename_prefix)
            
            print(f"\n✅ {case['keyword']} - {case['region']} 自适应收集完成")
    
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
