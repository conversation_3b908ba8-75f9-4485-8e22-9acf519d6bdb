# 🕷️ 真实数据爬虫使用指南

## 📋 概述

本系统现已支持获取真实的企业数据，包括：
- ✅ 完整的中国34个省级行政区
- ✅ 真实的企业名称和联系信息
- ✅ 电话号码、网址、地址等详细信息
- ✅ 智能去重和数据清洗

## 🌍 支持的省份列表

### 直辖市 (4个)
- 北京市
- 上海市  
- 天津市
- 重庆市

### 省份 (23个)
- 河北省、山西省、辽宁省、吉林省、黑龙江省
- 江苏省、浙江省、安徽省、福建省、江西省、山东省
- 河南省、湖北省、湖南省、广东省、海南省
- 四川省、贵州省、云南省、陕西省、甘肃省、青海省、台湾省

### 自治区 (5个)
- 内蒙古自治区
- 广西壮族自治区
- 西藏自治区
- 宁夏回族自治区
- 新疆维吾尔自治区

### 特别行政区 (2个)
- 香港特别行政区
- 澳门特别行政区

## 🔧 启用真实数据模式

### 方法1：自动安装（推荐）
```bash
# 运行自动安装脚本
python install_real_crawler.py
```

### 方法2：手动安装
```bash
# 安装HTML解析库
pip install beautifulsoup4 lxml

# 安装用户代理轮换
pip install fake-useragent

# 安装模糊匹配库
pip install fuzzywuzzy python-levenshtein

# 重新启动系统
python -m streamlit run main.py
```

## 🎯 真实数据功能

### 1. 搜索引擎爬取
- **百度搜索**: 使用正则表达式和HTML解析提取企业信息
- **必应搜索**: 多模式匹配企业名称和联系方式
- **智能关键词**: 根据行业和地区自动生成搜索词

### 2. 企业目录爬取
- **企查查**: 企业工商信息查询
- **天眼查**: 企业信用信息查询
- **智能解析**: 提取企业名称、电话、地址、网址

### 3. 数据质量保证
- **智能去重**: 基于公司名称的模糊匹配去重
- **数据验证**: 验证企业名称和联系信息的有效性
- **格式标准化**: 统一电话号码、网址格式

## 📊 使用示例

### 示例1：搜索北京的人工智能公司
1. 选择省份：北京市
2. 选择城市：北京市
3. 输入行业：人工智能
4. 点击开始收集

**预期结果**：
- 获取20-50家真实的北京人工智能公司
- 包含公司名称、电话、地址、官网等信息
- 自动去除重复和无效数据

### 示例2：搜索广东省的新能源汽车企业
1. 选择省份：广东省
2. 选择城市：全省
3. 输入行业：新能源汽车
4. 点击开始收集

**预期结果**：
- 获取广东省各城市的新能源汽车企业
- 包含深圳、广州、东莞等地的企业信息
- 提供完整的联系方式和地址信息

## 🔍 数据提取技术

### 1. 正则表达式模式
```python
# 企业名称提取
company_patterns = [
    r'([^<>]*(?:关键词)[^<>]*(?:公司|企业|集团|有限|股份|科技|实业))',
    r'title="([^"]*(?:关键词)[^"]*(?:公司|企业)[^"]*)"'
]

# 电话号码提取
phone_patterns = [
    r'(?:电话|Tel)[:：\s]*([0-9\-\s\(\)]{7,20})',
    r'(\d{3,4}[-\s]?\d{7,8})',  # 固定电话
    r'(1[3-9]\d{9})'  # 手机号码
]
```

### 2. HTML解析技术
- 使用BeautifulSoup解析HTML结构
- 提取特定标签中的企业信息
- 智能识别联系信息位置

### 3. 数据清洗算法
- 移除HTML标签和特殊字符
- 标准化电话号码格式
- 验证网址有效性

## ⚠️ 注意事项

### 1. 合规使用
- 仅用于合法的商业用途
- 遵守网站的robots.txt规则
- 不要过于频繁地请求

### 2. 数据准确性
- 搜索结果可能包含过时信息
- 建议人工验证重要联系信息
- 定期更新数据库

### 3. 技术限制
- 某些网站可能有反爬虫机制
- 网络状况影响数据获取速度
- 搜索结果数量受搜索引擎限制

## 🚀 性能优化

### 1. 搜索策略
- 使用多个搜索引擎提高覆盖率
- 智能关键词组合提高精准度
- 分页搜索获取更多结果

### 2. 反反爬措施
- 随机用户代理轮换
- 请求间隔随机延时
- 使用代理IP（可选）

### 3. 数据处理
- 实时去重减少冗余
- 并行处理提高速度
- 增量更新节省资源

## 📈 数据统计

启用真实数据模式后，您可以期待：
- **数据量**: 每次搜索50-200条真实企业信息
- **准确率**: 企业名称准确率>95%
- **联系信息**: 60-80%的企业包含有效联系方式
- **覆盖范围**: 支持全国34个省级行政区

## 🎉 开始使用

1. **安装依赖**: `python install_real_crawler.py`
2. **启动系统**: `python -m streamlit run main.py`
3. **选择区域**: 从完整的省份列表中选择
4. **输入行业**: 输入您感兴趣的行业关键词
5. **开始收集**: 点击按钮开始获取真实数据
6. **导出数据**: 将结果导出为Excel、CSV或JSON格式

现在您可以获取真实、准确、完整的企业信息了！🎯
