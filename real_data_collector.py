#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据收集器
专门用于收集真实的企业信息，不包含任何模拟数据
"""

import sys
import os
import time
import random
import re
import json
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
from urllib.parse import urlencode, quote
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class RealDataCollector:
    """真实数据收集器"""
    
    def __init__(self):
        """初始化真实数据收集器"""
        self.session = requests.Session()
        self.driver = None
        self.setup_session()
        self.setup_browser()
        
        # 真实数据源配置
        self.data_sources = {
            'qichacha': {
                'name': '企查查',
                'base_url': 'https://www.qichacha.com',
                'search_url': 'https://www.qichacha.com/search',
                'enabled': True
            },
            'tianyancha': {
                'name': '天眼查',
                'base_url': 'https://www.tianyancha.com',
                'search_url': 'https://www.tianyancha.com/search',
                'enabled': True
            },
            'aiqicha': {
                'name': '爱企查',
                'base_url': 'https://aiqicha.baidu.com',
                'search_url': 'https://aiqicha.baidu.com/s',
                'enabled': True
            },
            'gsxt': {
                'name': '国家企业信用信息公示系统',
                'base_url': 'http://www.gsxt.gov.cn',
                'search_url': 'http://www.gsxt.gov.cn/index.html',
                'enabled': True
            },
            'bidding_sites': {
                'name': '招投标网站',
                'sites': [
                    'https://www.chinabidding.com.cn',
                    'https://www.zbytb.com',
                    'https://www.okcis.cn'
                ],
                'enabled': True
            }
        }
        
        print("🚀 真实数据收集器初始化完成")
        print("📊 支持的真实数据源:")
        for key, source in self.data_sources.items():
            if source.get('enabled', True):
                print(f"  ✅ {source['name']}")
    
    def setup_session(self):
        """设置HTTP会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        self.session.headers.update(headers)
        self.session.timeout = 30
        
        # 设置代理（如果需要）
        # self.session.proxies = {
        #     'http': 'http://proxy:port',
        #     'https': 'https://proxy:port'
        # }
    
    def setup_browser(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            # chrome_options.add_argument('--headless')  # 可选：无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ 浏览器初始化成功")
            
        except Exception as e:
            print(f"⚠️ 浏览器初始化失败: {e}")
            print("   将使用HTTP请求方式进行数据收集")
            self.driver = None
    
    def search_qichacha(self, keyword: str, region: str = None) -> List[Dict[str, Any]]:
        """从企查查搜索真实企业信息"""
        print(f"🔍 正在从企查查搜索: {keyword}")
        
        if not self.driver:
            print("❌ 需要浏览器支持")
            return []
        
        try:
            # 构建搜索URL
            search_url = f"https://www.qichacha.com/search?key={quote(keyword)}"
            if region:
                search_url += f"&province={quote(region)}"
            
            print(f"📡 访问URL: {search_url}")
            
            # 访问搜索页面
            self.driver.get(search_url)
            time.sleep(3)
            
            # 等待搜索结果加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "m_srchList"))
                )
            except:
                print("⚠️ 搜索结果加载超时")
                return []
            
            # 提取企业信息
            companies = []
            company_elements = self.driver.find_elements(By.CSS_SELECTOR, ".m_srchList tbody tr")
            
            for element in company_elements[:10]:  # 限制前10个结果
                try:
                    # 提取企业名称
                    name_element = element.find_element(By.CSS_SELECTOR, ".ma_h1 a")
                    company_name = name_element.text.strip()
                    detail_url = name_element.get_attribute('href')
                    
                    # 提取法定代表人
                    legal_rep = ""
                    try:
                        legal_element = element.find_element(By.XPATH, ".//td[contains(text(), '法定代表人')]/following-sibling::td")
                        legal_rep = legal_element.text.strip()
                    except:
                        pass
                    
                    # 提取注册资本
                    capital = ""
                    try:
                        capital_element = element.find_element(By.XPATH, ".//td[contains(text(), '注册资本')]/following-sibling::td")
                        capital = capital_element.text.strip()
                    except:
                        pass
                    
                    # 提取成立时间
                    establish_date = ""
                    try:
                        date_element = element.find_element(By.XPATH, ".//td[contains(text(), '成立时间')]/following-sibling::td")
                        establish_date = date_element.text.strip()
                    except:
                        pass
                    
                    # 提取经营状态
                    status = ""
                    try:
                        status_element = element.find_element(By.XPATH, ".//td[contains(text(), '经营状态')]/following-sibling::td")
                        status = status_element.text.strip()
                    except:
                        pass
                    
                    if company_name:
                        company_info = {
                            'company_name': company_name,
                            'legal_representative': legal_rep,
                            'registration_capital': capital,
                            'establishment_date': establish_date,
                            'business_status': status,
                            'detail_url': detail_url,
                            'source': '企查查',
                            'search_keyword': keyword,
                            'region': region or '全国',
                            'data_type': 'real_enterprise_data'
                        }
                        companies.append(company_info)
                        
                except Exception as e:
                    print(f"⚠️ 解析企业信息失败: {e}")
                    continue
            
            print(f"✅ 从企查查获取到 {len(companies)} 条真实企业信息")
            return companies
            
        except Exception as e:
            print(f"❌ 企查查搜索失败: {e}")
            return []
    
    def search_tianyancha(self, keyword: str, region: str = None) -> List[Dict[str, Any]]:
        """从天眼查搜索真实企业信息"""
        print(f"🔍 正在从天眼查搜索: {keyword}")
        
        if not self.driver:
            print("❌ 需要浏览器支持")
            return []
        
        try:
            # 构建搜索URL
            search_url = f"https://www.tianyancha.com/search?key={quote(keyword)}"
            
            print(f"📡 访问URL: {search_url}")
            
            # 访问搜索页面
            self.driver.get(search_url)
            time.sleep(3)
            
            # 等待搜索结果加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "search-result-single"))
                )
            except:
                print("⚠️ 搜索结果加载超时")
                return []
            
            # 提取企业信息
            companies = []
            company_elements = self.driver.find_elements(By.CLASS_NAME, "search-result-single")
            
            for element in company_elements[:10]:  # 限制前10个结果
                try:
                    # 提取企业名称
                    name_element = element.find_element(By.CSS_SELECTOR, ".name a")
                    company_name = name_element.text.strip()
                    detail_url = name_element.get_attribute('href')
                    
                    # 提取其他信息
                    info_text = element.text
                    
                    # 使用正则表达式提取结构化信息
                    legal_rep = self._extract_pattern(info_text, r'法定代表人[：:]\s*([^\s\n]+)')
                    capital = self._extract_pattern(info_text, r'注册资本[：:]\s*([^\s\n]+)')
                    status = self._extract_pattern(info_text, r'经营状态[：:]\s*([^\s\n]+)')
                    
                    if company_name:
                        company_info = {
                            'company_name': company_name,
                            'legal_representative': legal_rep,
                            'registration_capital': capital,
                            'business_status': status,
                            'detail_url': detail_url,
                            'source': '天眼查',
                            'search_keyword': keyword,
                            'region': region or '全国',
                            'data_type': 'real_enterprise_data'
                        }
                        companies.append(company_info)
                        
                except Exception as e:
                    print(f"⚠️ 解析企业信息失败: {e}")
                    continue
            
            print(f"✅ 从天眼查获取到 {len(companies)} 条真实企业信息")
            return companies
            
        except Exception as e:
            print(f"❌ 天眼查搜索失败: {e}")
            return []
    
    def search_aiqicha(self, keyword: str, region: str = None) -> List[Dict[str, Any]]:
        """从爱企查搜索真实企业信息"""
        print(f"🔍 正在从爱企查搜索: {keyword}")

        if not self.driver:
            print("❌ 需要浏览器支持")
            return []

        try:
            # 构建搜索URL
            search_url = f"https://aiqicha.baidu.com/s?q={quote(keyword)}&t=0"
            if region:
                search_url += f"&province={quote(region)}"

            print(f"📡 访问URL: {search_url}")

            # 访问搜索页面
            self.driver.get(search_url)
            time.sleep(3)

            # 等待搜索结果加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".result-list, .search-list"))
                )
            except:
                print("⚠️ 搜索结果加载超时")
                return []

            # 提取企业信息
            companies = []
            company_elements = self.driver.find_elements(By.CSS_SELECTOR, ".result-item, .search-item")

            for element in company_elements[:10]:  # 限制前10个结果
                try:
                    # 提取企业名称
                    name_element = element.find_element(By.CSS_SELECTOR, ".company-name a, .title a")
                    company_name = name_element.text.strip()
                    detail_url = name_element.get_attribute('href')

                    # 提取其他信息
                    info_text = element.text

                    # 使用正则表达式提取结构化信息
                    legal_rep = self._extract_pattern(info_text, r'法定代表人[：:]\s*([^\s\n]+)')
                    capital = self._extract_pattern(info_text, r'注册资本[：:]\s*([^\s\n]+)')
                    status = self._extract_pattern(info_text, r'经营状态[：:]\s*([^\s\n]+)')

                    if company_name:
                        company_info = {
                            'company_name': company_name,
                            'legal_representative': legal_rep,
                            'registration_capital': capital,
                            'business_status': status,
                            'detail_url': detail_url,
                            'source': '爱企查',
                            'search_keyword': keyword,
                            'region': region or '全国',
                            'data_type': 'real_enterprise_data'
                        }
                        companies.append(company_info)

                except Exception as e:
                    print(f"⚠️ 解析企业信息失败: {e}")
                    continue

            print(f"✅ 从爱企查获取到 {len(companies)} 条真实企业信息")
            return companies

        except Exception as e:
            print(f"❌ 爱企查搜索失败: {e}")
            return []
    
    def search_bidding_sites(self, keyword: str, region: str = None) -> List[Dict[str, Any]]:
        """从招投标网站搜索真实项目信息"""
        print(f"🔍 正在从招投标网站搜索: {keyword}")
        
        companies = []
        
        for site_url in self.data_sources['bidding_sites']['sites']:
            try:
                print(f"📡 搜索网站: {site_url}")
                
                # 这里需要根据具体网站的搜索接口进行实现
                # 每个招投标网站的结构都不同，需要单独适配
                
                # 示例：使用通用搜索方式
                search_params = {
                    'keyword': keyword,
                    'region': region or ''
                }
                
                # 实际实现中需要分析每个网站的具体搜索接口
                # response = self.session.get(f"{site_url}/search", params=search_params)
                
                print(f"⚠️ {site_url} 需要具体适配实现")
                
            except Exception as e:
                print(f"❌ 搜索 {site_url} 失败: {e}")
                continue
        
        return companies
    
    def collect_real_data(self, industry: str, region: str = None, max_results: int = 50) -> List[Dict[str, Any]]:
        """收集真实数据"""
        print(f"🎯 开始收集真实数据")
        print(f"行业关键词: {industry}")
        print(f"地区范围: {region or '全国'}")
        print(f"目标数量: {max_results}")
        print("=" * 60)
        
        all_companies = []
        
        # 1. 从企查查收集数据
        if self.data_sources['qichacha']['enabled']:
            try:
                qichacha_data = self.search_qichacha(industry, region)
                all_companies.extend(qichacha_data)
                time.sleep(2)  # 避免请求过快
            except Exception as e:
                print(f"❌ 企查查数据收集失败: {e}")
        
        # 2. 从天眼查收集数据
        if self.data_sources['tianyancha']['enabled']:
            try:
                tianyancha_data = self.search_tianyancha(industry, region)
                all_companies.extend(tianyancha_data)
                time.sleep(2)  # 避免请求过快
            except Exception as e:
                print(f"❌ 天眼查数据收集失败: {e}")
        
        # 3. 从爱企查收集数据
        if self.data_sources['aiqicha']['enabled']:
            try:
                aiqicha_data = self.search_aiqicha(industry, region)
                all_companies.extend(aiqicha_data)
                time.sleep(2)  # 避免请求过快
            except Exception as e:
                print(f"❌ 爱企查数据收集失败: {e}")
        
        # 4. 从招投标网站收集数据
        if self.data_sources['bidding_sites']['enabled']:
            try:
                bidding_data = self.search_bidding_sites(industry, region)
                all_companies.extend(bidding_data)
            except Exception as e:
                print(f"❌ 招投标数据收集失败: {e}")
        
        # 数据去重和清理
        unique_companies = self._deduplicate_companies(all_companies)
        
        # 限制结果数量
        final_results = unique_companies[:max_results]
        
        print(f"\n📊 数据收集完成:")
        print(f"  原始数据: {len(all_companies)} 条")
        print(f"  去重后: {len(unique_companies)} 条")
        print(f"  最终结果: {len(final_results)} 条")
        
        return final_results
    
    def _extract_pattern(self, text: str, pattern: str) -> str:
        """使用正则表达式提取信息"""
        match = re.search(pattern, text)
        return match.group(1) if match else ""
    
    def _deduplicate_companies(self, companies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """企业数据去重"""
        seen_names = set()
        unique_companies = []
        
        for company in companies:
            name = company.get('company_name', '')
            if name and name not in seen_names:
                seen_names.add(name)
                unique_companies.append(company)
        
        return unique_companies
    
    def save_to_file(self, data: List[Dict[str, Any]], filename: str = None):
        """保存数据到文件"""
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"real_company_data_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 数据已保存到: {filename}")
            
            # 同时保存为CSV格式
            if data:
                csv_filename = filename.replace('.json', '.csv')
                df = pd.DataFrame(data)
                df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                print(f"✅ CSV格式已保存到: {csv_filename}")
                
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
    
    def close(self):
        """关闭资源"""
        if self.driver:
            self.driver.quit()
            print("✅ 浏览器已关闭")

def main():
    """主函数"""
    collector = RealDataCollector()
    
    try:
        # 测试真实数据收集
        test_cases = [
            {"industry": "建筑工程", "region": "北京"},
            {"industry": "酒店管理", "region": "上海"},
            {"industry": "房地产开发", "region": "深圳"}
        ]
        
        for case in test_cases:
            print(f"\n{'='*80}")
            print(f"🧪 测试案例: {case['industry']} - {case['region']}")
            print(f"{'='*80}")
            
            # 收集真实数据
            real_data = collector.collect_real_data(
                industry=case['industry'],
                region=case['region'],
                max_results=20
            )
            
            if real_data:
                print(f"\n📋 收集到的真实企业信息:")
                for i, company in enumerate(real_data[:5], 1):
                    print(f"  {i}. {company['company_name']}")
                    print(f"     来源: {company['source']}")
                    print(f"     法人: {company.get('legal_representative', 'N/A')}")
                    print(f"     资本: {company.get('registration_capital', 'N/A')}")
                    print(f"     状态: {company.get('business_status', 'N/A')}")
                    print()
                
                # 保存数据
                filename = f"real_data_{case['industry']}_{case['region']}.json"
                collector.save_to_file(real_data, filename)
            else:
                print("❌ 未收集到真实数据")
    
    finally:
        collector.close()

if __name__ == "__main__":
    main()
