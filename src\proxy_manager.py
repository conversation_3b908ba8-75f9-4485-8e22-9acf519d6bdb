#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理池管理模块
支持代理IP的获取、验证、轮换和失效处理
"""

import asyncio
import aiohttp
import random
import time
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import os

class ProxyStatus(Enum):
    """代理状态枚举"""
    UNKNOWN = "unknown"
    ACTIVE = "active"
    FAILED = "failed"
    TIMEOUT = "timeout"
    BANNED = "banned"

@dataclass
class ProxyInfo:
    """代理信息数据类"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = "http"
    status: ProxyStatus = ProxyStatus.UNKNOWN
    last_used: float = field(default_factory=time.time)
    success_count: int = 0
    failure_count: int = 0
    response_time: float = 0.0
    created_time: float = field(default_factory=time.time)
    
    @property
    def url(self) -> str:
        """获取代理URL"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.0
    
    @property
    def is_healthy(self) -> bool:
        """判断代理是否健康"""
        return (
            self.status == ProxyStatus.ACTIVE and
            self.success_rate >= 0.7 and
            self.response_time < 10.0
        )

class ProxyManager:
    """代理池管理器"""
    
    def __init__(self, config_file: str = "config/proxy_config.json"):
        """
        初始化代理管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.config_file = config_file
        self.proxies: List[ProxyInfo] = []
        self.current_index = 0
        self.max_retries = 3
        self.timeout = 10
        self.test_url = "http://httpbin.org/ip"
        self.min_success_rate = 0.7
        self.max_failure_count = 5
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # 更新配置
                self.timeout = config.get('timeout', self.timeout)
                self.test_url = config.get('test_url', self.test_url)
                self.min_success_rate = config.get('min_success_rate', self.min_success_rate)
                self.max_failure_count = config.get('max_failure_count', self.max_failure_count)
                
                # 加载代理列表
                proxy_list = config.get('proxies', [])
                for proxy_data in proxy_list:
                    proxy = ProxyInfo(
                        host=proxy_data['host'],
                        port=proxy_data['port'],
                        username=proxy_data.get('username'),
                        password=proxy_data.get('password'),
                        protocol=proxy_data.get('protocol', 'http')
                    )
                    self.proxies.append(proxy)
                
                self.logger.info(f"✅ 加载配置文件成功，共 {len(self.proxies)} 个代理")
            else:
                self.logger.warning(f"⚠️ 配置文件不存在: {self.config_file}")
                self._create_default_config()
                
        except Exception as e:
            self.logger.error(f"❌ 加载配置文件失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "timeout": 10,
            "test_url": "http://httpbin.org/ip",
            "min_success_rate": 0.7,
            "max_failure_count": 5,
            "proxies": [
                {
                    "host": "127.0.0.1",
                    "port": 8080,
                    "protocol": "http",
                    "username": None,
                    "password": None
                }
            ]
        }
        
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            self.logger.info(f"✅ 创建默认配置文件: {self.config_file}")
        except Exception as e:
            self.logger.error(f"❌ 创建默认配置文件失败: {e}")
    
    async def initialize(self):
        """初始化代理池"""
        if not self.proxies:
            self.logger.warning("⚠️ 代理池为空，将使用直连模式")
            return
        
        self.logger.info("🔍 开始验证代理池...")
        await self._validate_all_proxies()
        
        # 移除失效代理
        active_proxies = [p for p in self.proxies if p.status == ProxyStatus.ACTIVE]
        self.logger.info(f"✅ 代理池初始化完成，可用代理: {len(active_proxies)}/{len(self.proxies)}")
    
    async def _validate_all_proxies(self):
        """验证所有代理"""
        tasks = []
        for proxy in self.proxies:
            task = asyncio.create_task(self._validate_proxy(proxy))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _validate_proxy(self, proxy: ProxyInfo) -> bool:
        """
        验证单个代理
        
        Args:
            proxy: 代理信息
            
        Returns:
            是否验证成功
        """
        start_time = time.time()
        
        try:
            connector = aiohttp.TCPConnector()
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                
                proxy_url = proxy.url if proxy.host != "127.0.0.1" else None
                
                async with session.get(
                    self.test_url,
                    proxy=proxy_url
                ) as response:
                    
                    if response.status == 200:
                        response_time = time.time() - start_time
                        proxy.response_time = response_time
                        proxy.status = ProxyStatus.ACTIVE
                        proxy.success_count += 1
                        proxy.last_used = time.time()
                        
                        self.logger.debug(f"✅ 代理验证成功: {proxy.host}:{proxy.port} ({response_time:.2f}s)")
                        return True
                    else:
                        proxy.status = ProxyStatus.FAILED
                        proxy.failure_count += 1
                        self.logger.debug(f"❌ 代理验证失败: {proxy.host}:{proxy.port} (状态码: {response.status})")
                        return False
                        
        except asyncio.TimeoutError:
            proxy.status = ProxyStatus.TIMEOUT
            proxy.failure_count += 1
            self.logger.debug(f"⏰ 代理验证超时: {proxy.host}:{proxy.port}")
            return False
            
        except Exception as e:
            proxy.status = ProxyStatus.FAILED
            proxy.failure_count += 1
            self.logger.debug(f"❌ 代理验证异常: {proxy.host}:{proxy.port} - {e}")
            return False
    
    async def get_proxy(self) -> Optional[ProxyInfo]:
        """
        获取一个可用的代理
        
        Returns:
            代理信息，如果没有可用代理则返回None
        """
        if not self.proxies:
            return None
        
        # 过滤健康的代理
        healthy_proxies = [p for p in self.proxies if p.is_healthy]
        
        if not healthy_proxies:
            # 如果没有健康的代理，尝试重新验证
            self.logger.warning("⚠️ 没有健康的代理，尝试重新验证...")
            await self._validate_all_proxies()
            healthy_proxies = [p for p in self.proxies if p.is_healthy]
            
            if not healthy_proxies:
                self.logger.error("❌ 没有可用的代理")
                return None
        
        # 按成功率和响应时间排序
        healthy_proxies.sort(key=lambda p: (-p.success_rate, p.response_time))
        
        # 选择最佳代理
        selected_proxy = healthy_proxies[0]
        selected_proxy.last_used = time.time()
        
        self.logger.debug(f"🎯 选择代理: {selected_proxy.host}:{selected_proxy.port}")
        return selected_proxy
    
    async def get_random_proxy(self) -> Optional[ProxyInfo]:
        """
        随机获取一个可用的代理
        
        Returns:
            代理信息，如果没有可用代理则返回None
        """
        healthy_proxies = [p for p in self.proxies if p.is_healthy]
        
        if not healthy_proxies:
            return await self.get_proxy()
        
        selected_proxy = random.choice(healthy_proxies)
        selected_proxy.last_used = time.time()
        
        self.logger.debug(f"🎲 随机选择代理: {selected_proxy.host}:{selected_proxy.port}")
        return selected_proxy

    async def mark_proxy_failed(self, proxy: ProxyInfo, reason: str = "unknown"):
        """
        标记代理失败

        Args:
            proxy: 代理信息
            reason: 失败原因
        """
        proxy.failure_count += 1
        proxy.status = ProxyStatus.FAILED

        self.logger.warning(f"⚠️ 代理标记为失败: {proxy.host}:{proxy.port} - {reason}")

        # 如果失败次数过多，标记为禁用
        if proxy.failure_count >= self.max_failure_count:
            proxy.status = ProxyStatus.BANNED
            self.logger.error(f"🚫 代理被禁用: {proxy.host}:{proxy.port} (失败次数: {proxy.failure_count})")

    async def mark_proxy_success(self, proxy: ProxyInfo):
        """
        标记代理成功

        Args:
            proxy: 代理信息
        """
        proxy.success_count += 1
        proxy.status = ProxyStatus.ACTIVE
        proxy.last_used = time.time()

        self.logger.debug(f"✅ 代理标记为成功: {proxy.host}:{proxy.port}")

    def add_proxy(self, host: str, port: int, username: str = None, password: str = None, protocol: str = "http"):
        """
        添加新代理

        Args:
            host: 代理主机
            port: 代理端口
            username: 用户名
            password: 密码
            protocol: 协议
        """
        proxy = ProxyInfo(
            host=host,
            port=port,
            username=username,
            password=password,
            protocol=protocol
        )

        self.proxies.append(proxy)
        self.logger.info(f"➕ 添加新代理: {host}:{port}")

    def remove_proxy(self, host: str, port: int):
        """
        移除代理

        Args:
            host: 代理主机
            port: 代理端口
        """
        self.proxies = [p for p in self.proxies if not (p.host == host and p.port == port)]
        self.logger.info(f"➖ 移除代理: {host}:{port}")

    def get_proxy_stats(self) -> Dict:
        """
        获取代理池统计信息

        Returns:
            统计信息字典
        """
        total = len(self.proxies)
        active = len([p for p in self.proxies if p.status == ProxyStatus.ACTIVE])
        failed = len([p for p in self.proxies if p.status == ProxyStatus.FAILED])
        timeout = len([p for p in self.proxies if p.status == ProxyStatus.TIMEOUT])
        banned = len([p for p in self.proxies if p.status == ProxyStatus.BANNED])

        avg_response_time = 0.0
        if active > 0:
            active_proxies = [p for p in self.proxies if p.status == ProxyStatus.ACTIVE]
            avg_response_time = sum(p.response_time for p in active_proxies) / active

        return {
            "total": total,
            "active": active,
            "failed": failed,
            "timeout": timeout,
            "banned": banned,
            "success_rate": active / total if total > 0 else 0.0,
            "avg_response_time": avg_response_time
        }

    def save_config(self):
        """保存配置到文件"""
        try:
            config = {
                "timeout": self.timeout,
                "test_url": self.test_url,
                "min_success_rate": self.min_success_rate,
                "max_failure_count": self.max_failure_count,
                "proxies": [
                    {
                        "host": p.host,
                        "port": p.port,
                        "username": p.username,
                        "password": p.password,
                        "protocol": p.protocol
                    }
                    for p in self.proxies
                ]
            }

            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.logger.info(f"✅ 配置保存成功: {self.config_file}")

        except Exception as e:
            self.logger.error(f"❌ 配置保存失败: {e}")

    async def cleanup_failed_proxies(self):
        """清理失效的代理"""
        before_count = len(self.proxies)

        # 移除被禁用的代理
        self.proxies = [p for p in self.proxies if p.status != ProxyStatus.BANNED]

        # 重置长时间未使用的失败代理状态
        current_time = time.time()
        for proxy in self.proxies:
            if (proxy.status in [ProxyStatus.FAILED, ProxyStatus.TIMEOUT] and
                current_time - proxy.last_used > 3600):  # 1小时
                proxy.status = ProxyStatus.UNKNOWN
                proxy.failure_count = 0

        after_count = len(self.proxies)
        removed_count = before_count - after_count

        if removed_count > 0:
            self.logger.info(f"🧹 清理失效代理: 移除 {removed_count} 个代理")

# 全局代理管理器实例
_proxy_manager: Optional[ProxyManager] = None

def get_proxy_manager(config_file: str = "config/proxy_config.json") -> ProxyManager:
    """
    获取全局代理管理器实例

    Args:
        config_file: 配置文件路径

    Returns:
        代理管理器实例
    """
    global _proxy_manager
    if _proxy_manager is None:
        _proxy_manager = ProxyManager(config_file)
    return _proxy_manager

async def cleanup_proxy_manager():
    """清理全局代理管理器"""
    global _proxy_manager
    if _proxy_manager is not None:
        await _proxy_manager.cleanup_failed_proxies()
        _proxy_manager = None
