#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据爬虫启动器
启动真实的企业和招投标数据爬取
"""

import sys
import os
import time
import json
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入爬虫模块
try:
    from enterprise_crawler import EnterpriseCrawler
    print("✅ 企业爬虫模块加载成功")
except ImportError as e:
    print(f"❌ 企业爬虫模块加载失败: {e}")
    EnterpriseCrawler = None

try:
    from bidding_crawler import BiddingCrawler
    print("✅ 招投标爬虫模块加载成功")
except ImportError as e:
    print(f"❌ 招投标爬虫模块加载失败: {e}")
    BiddingCrawler = None

class RealDataCrawlerManager:
    """真实数据爬虫管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.enterprise_crawler = None
        self.bidding_crawler = None
        
        print("🎯 真实数据爬虫管理器")
        print("=" * 60)
        print("📌 重要说明:")
        print("   ✅ 本系统只收集真实数据")
        print("   ❌ 不包含任何模拟或虚假数据")
        print("   🔍 数据来源于真实网站")
        print("   📊 所有数据都经过验证")
        print("=" * 60)
    
    def initialize_crawlers(self):
        """初始化爬虫"""
        print("\n🚀 正在初始化爬虫...")
        
        # 初始化企业爬虫
        if EnterpriseCrawler:
            try:
                self.enterprise_crawler = EnterpriseCrawler()
                print("✅ 企业爬虫初始化成功")
            except Exception as e:
                print(f"❌ 企业爬虫初始化失败: {e}")
        
        # 初始化招投标爬虫
        if BiddingCrawler:
            try:
                self.bidding_crawler = BiddingCrawler()
                print("✅ 招投标爬虫初始化成功")
            except Exception as e:
                print(f"❌ 招投标爬虫初始化失败: {e}")
    
    def crawl_enterprise_data(self, keyword: str, region: str = None, max_results: int = 50) -> List[Dict[str, Any]]:
        """爬取企业数据"""
        print(f"\n🏢 开始爬取企业数据")
        print(f"关键词: {keyword}")
        print(f"地区: {region or '全国'}")
        print(f"目标数量: {max_results}")
        
        if not self.enterprise_crawler:
            print("❌ 企业爬虫未初始化")
            return []
        
        try:
            companies = self.enterprise_crawler.crawl_enterprises(keyword, region, max_results)
            
            if companies:
                print(f"✅ 成功爬取 {len(companies)} 条企业数据")
                
                # 显示部分结果
                print(f"\n📋 企业数据预览:")
                for i, company in enumerate(companies[:3], 1):
                    print(f"  {i}. {company['company_name']}")
                    print(f"     来源: {company['source']}")
                    print(f"     法人: {company.get('legal_representative', 'N/A')}")
                    print(f"     资本: {company.get('registration_capital', 'N/A')}")
                    print(f"     状态: {company.get('business_status', 'N/A')}")
                    print()
                
                return companies
            else:
                print("❌ 未获取到企业数据")
                return []
                
        except Exception as e:
            print(f"❌ 企业数据爬取失败: {e}")
            return []
    
    def crawl_bidding_data(self, keyword: str, region: str = None, max_results: int = 50) -> List[Dict[str, Any]]:
        """爬取招投标数据"""
        print(f"\n📋 开始爬取招投标数据")
        print(f"关键词: {keyword}")
        print(f"地区: {region or '全国'}")
        print(f"目标数量: {max_results}")
        
        if not self.bidding_crawler:
            print("❌ 招投标爬虫未初始化")
            return []
        
        try:
            projects = self.bidding_crawler.crawl_bidding_projects(keyword, region, max_results)
            
            if projects:
                print(f"✅ 成功爬取 {len(projects)} 条招投标数据")
                
                # 显示部分结果
                print(f"\n📋 招投标数据预览:")
                for i, project in enumerate(projects[:3], 1):
                    print(f"  {i}. {project['project_title']}")
                    print(f"     来源: {project['source']}")
                    print(f"     类型: {project.get('project_type', 'N/A')}")
                    print(f"     金额: {project.get('project_amount', 'N/A')}")
                    print(f"     发布: {project.get('publish_date', 'N/A')}")
                    print()
                
                return projects
            else:
                print("❌ 未获取到招投标数据")
                return []
                
        except Exception as e:
            print(f"❌ 招投标数据爬取失败: {e}")
            return []
    
    def crawl_comprehensive_data(self, keyword: str, region: str = None, max_results_per_type: int = 30) -> Dict[str, List[Dict[str, Any]]]:
        """综合爬取数据"""
        print(f"\n🎯 开始综合数据爬取")
        print(f"关键词: {keyword}")
        print(f"地区: {region or '全国'}")
        print(f"每类数据目标数量: {max_results_per_type}")
        print("=" * 80)
        
        results = {
            'enterprises': [],
            'bidding_projects': [],
            'summary': {}
        }
        
        # 1. 爬取企业数据
        print("\n🔸 第一阶段: 爬取企业数据")
        enterprises = self.crawl_enterprise_data(keyword, region, max_results_per_type)
        results['enterprises'] = enterprises
        
        # 2. 爬取招投标数据
        print("\n🔸 第二阶段: 爬取招投标数据")
        bidding_projects = self.crawl_bidding_data(keyword, region, max_results_per_type)
        results['bidding_projects'] = bidding_projects
        
        # 3. 生成汇总信息
        results['summary'] = {
            'keyword': keyword,
            'region': region or '全国',
            'enterprise_count': len(enterprises),
            'bidding_project_count': len(bidding_projects),
            'total_count': len(enterprises) + len(bidding_projects),
            'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'data_sources': []
        }
        
        # 统计数据源
        sources = set()
        for item in enterprises + bidding_projects:
            sources.add(item.get('source', '未知'))
        results['summary']['data_sources'] = list(sources)
        
        print(f"\n📊 综合爬取完成:")
        print(f"  企业数据: {len(enterprises)} 条")
        print(f"  招投标数据: {len(bidding_projects)} 条")
        print(f"  总计: {len(enterprises) + len(bidding_projects)} 条")
        print(f"  数据源: {', '.join(sources)}")
        
        return results
    
    def save_comprehensive_data(self, data: Dict[str, Any], filename_prefix: str = None):
        """保存综合数据"""
        if not filename_prefix:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename_prefix = f"real_data_{timestamp}"
        
        try:
            # 保存完整数据
            full_filename = f"{filename_prefix}_complete.json"
            with open(full_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ 完整数据已保存: {full_filename}")
            
            # 分别保存企业数据
            if data['enterprises']:
                enterprise_filename = f"{filename_prefix}_enterprises.json"
                with open(enterprise_filename, 'w', encoding='utf-8') as f:
                    json.dump(data['enterprises'], f, ensure_ascii=False, indent=2)
                print(f"✅ 企业数据已保存: {enterprise_filename}")
                
                # 保存为CSV
                import pandas as pd
                df = pd.DataFrame(data['enterprises'])
                csv_filename = f"{filename_prefix}_enterprises.csv"
                df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                print(f"✅ 企业CSV已保存: {csv_filename}")
            
            # 分别保存招投标数据
            if data['bidding_projects']:
                bidding_filename = f"{filename_prefix}_bidding.json"
                with open(bidding_filename, 'w', encoding='utf-8') as f:
                    json.dump(data['bidding_projects'], f, ensure_ascii=False, indent=2)
                print(f"✅ 招投标数据已保存: {bidding_filename}")
                
                # 保存为CSV
                import pandas as pd
                df = pd.DataFrame(data['bidding_projects'])
                csv_filename = f"{filename_prefix}_bidding.csv"
                df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                print(f"✅ 招投标CSV已保存: {csv_filename}")
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def close(self):
        """关闭所有爬虫"""
        print("\n🔄 正在关闭爬虫...")
        
        if self.enterprise_crawler:
            try:
                self.enterprise_crawler.close()
            except Exception as e:
                print(f"⚠️ 关闭企业爬虫失败: {e}")
        
        if self.bidding_crawler:
            try:
                self.bidding_crawler.close()
            except Exception as e:
                print(f"⚠️ 关闭招投标爬虫失败: {e}")
        
        print("✅ 所有爬虫已关闭")

def main():
    """主函数"""
    manager = RealDataCrawlerManager()
    
    try:
        # 初始化爬虫
        manager.initialize_crawlers()
        
        # 测试数据爬取
        test_cases = [
            {"keyword": "建筑工程", "region": "北京"},
            {"keyword": "酒店管理", "region": "上海"},
            {"keyword": "房地产开发", "region": "深圳"}
        ]
        
        for case in test_cases:
            print(f"\n{'='*100}")
            print(f"🧪 测试案例: {case['keyword']} - {case['region']}")
            print(f"{'='*100}")
            
            # 综合爬取数据
            results = manager.crawl_comprehensive_data(
                keyword=case['keyword'],
                region=case['region'],
                max_results_per_type=20
            )
            
            # 保存数据
            filename_prefix = f"real_data_{case['keyword']}_{case['region']}"
            manager.save_comprehensive_data(results, filename_prefix)
            
            print(f"\n✅ {case['keyword']} - {case['region']} 数据爬取完成")
    
    finally:
        manager.close()

if __name__ == "__main__":
    main()
