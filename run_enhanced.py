#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版启动脚本 - 启动带有区域搜索功能的爬虫系统
"""

import sys
import os
import subprocess
from pathlib import Path
import time

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'streamlit',
        'pandas',
        'plotly',
        'requests',
        'beautifulsoup4'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺失依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包检查通过")
    return True

def check_project_structure():
    """检查项目结构"""
    print("\n📁 检查项目结构...")
    
    required_files = [
        'main.py',
        'config.yaml',
        'src/crawler_engine.py',
        'src/data_processor.py',
        'src/visualizer.py',
        'src/region_manager.py',
        'src/map_visualizer.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ 缺失文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 项目结构检查通过")
    return True

def create_directories():
    """创建必要的目录"""
    print("\n📂 创建必要目录...")
    
    directories = ['data', 'logs', 'data/backups']
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  ✅ {directory}")

def run_demo():
    """运行演示"""
    print("\n🎬 运行区域搜索演示...")
    
    try:
        result = subprocess.run([sys.executable, 'demo_enhanced.py'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 演示运行成功")
            print("\n演示输出:")
            print("-" * 40)
            print(result.stdout)
        else:
            print("❌ 演示运行失败")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 演示运行超时")
        return False
    except Exception as e:
        print(f"❌ 演示运行出错: {e}")
        return False
    
    return True

def start_streamlit():
    """启动Streamlit应用"""
    print("\n🚀 启动Web界面...")
    print("=" * 50)
    print("🌐 Web界面将在浏览器中自动打开")
    print("📍 地址: http://localhost:8501")
    print("⏹️ 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 启动Streamlit
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 'main.py',
            '--server.port=8501',
            '--server.address=localhost',
            '--browser.gatherUsageStats=false'
        ])
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def show_features():
    """显示新功能介绍"""
    print("\n🎉 新功能介绍")
    print("=" * 50)
    print("🌍 区域搜索功能:")
    print("  • 支持按省份搜索企业信息")
    print("  • 支持按城市精准搜索")
    print("  • 自动生成区域化搜索关键词")
    print("  • 智能区域验证和提示")
    
    print("\n🎨 增强UI界面:")
    print("  • 直观的省市选择器")
    print("  • 实时区域选择反馈")
    print("  • 数据筛选功能")
    print("  • 分类图表展示")
    
    print("\n🗺️ 地图可视化:")
    print("  • 企业省份分布地图")
    print("  • 企业城市分布地图")
    print("  • 区域统计图表")
    print("  • 交互式地图界面")
    
    print("\n💾 增强导出功能:")
    print("  • 支持筛选后数据导出")
    print("  • 多格式导出(CSV/Excel/JSON)")
    print("  • 自动生成统计摘要")
    print("  • 自定义文件名")
    
    print("\n📊 数据分析:")
    print("  • 区域分布统计")
    print("  • 数据完整度分析")
    print("  • 多维度图表展示")
    print("  • 综合仪表板")

def main():
    """主函数"""
    print("🕷️ 智能客户信息收集系统 - 增强版")
    print("=" * 60)
    print("版本: v2.0 (支持区域搜索)")
    print("作者: 爬虫专家")
    print("=" * 60)
    
    # 系统检查
    if not check_python_version():
        return
    
    # 显示新功能
    show_features()
    
    # 创建目录
    create_directories()
    
    # 检查项目结构
    if not check_project_structure():
        print("\n❌ 项目结构不完整，请检查文件")
        return
    
    # 运行演示
    print("\n" + "=" * 60)
    choice = input("是否运行区域搜索演示? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        if not run_demo():
            print("⚠️ 演示运行有问题，但可以继续启动主程序")
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖包不完整")
        print("💡 提示: 可以运行 'pip install -r requirements.txt' 安装所有依赖")
        
        choice = input("是否继续启动? (y/n): ").lower().strip()
        if choice not in ['y', 'yes', '是']:
            return
    
    # 启动应用
    print("\n" + "=" * 60)
    print("🚀 准备启动Web界面...")
    time.sleep(2)
    
    start_streamlit()

if __name__ == "__main__":
    main()
