#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业数据源爬虫模块
专注于招标中标、房地产项目、酒店建设等高价值信息源
"""

import requests
import time
import random
import re
import pandas as pd
from urllib.parse import urljoin, urlparse, quote
from typing import List, Dict, Any, Optional
import yaml
from pathlib import Path
from datetime import datetime, timedelta

try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None

try:
    from fake_useragent import UserAgent
except ImportError:
    UserAgent = None

from .utils.logger import get_logger
from .region_manager import RegionManager

class ProfessionalCrawler:
    """专业数据源爬虫类"""
    
    def __init__(self, config_path: str = "professional_sources_config.yaml"):
        """初始化专业爬虫"""
        self.logger = get_logger(__name__)
        self.config = self._load_config(config_path)
        self.ua = UserAgent() if UserAgent else None
        self.session = requests.Session()
        self.region_manager = RegionManager()
        
        # 设置请求头
        self._setup_session()
        
    def _load_config(self, config_path: str) -> Dict:
        """加载专业数据源配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载专业配置失败: {e}")
            return {}
    
    def _setup_session(self):
        """设置会话请求头"""
        headers = {
            'User-Agent': self.ua.random if self.ua else 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
    
    def search_professional_sources(self, industry: str, province: str = None, city: str = None, 
                                  search_types: List[str] = None) -> pd.DataFrame:
        """搜索专业数据源"""
        if not search_types:
            search_types = ['bidding', 'real_estate', 'hotel']
        
        all_results = []
        
        for search_type in search_types:
            self.logger.info(f"开始搜索 {search_type} 相关信息")
            
            if search_type == 'bidding':
                results = self._search_bidding_info(industry, province, city)
            elif search_type == 'real_estate':
                results = self._search_real_estate_info(industry, province, city)
            elif search_type == 'hotel':
                results = self._search_hotel_info(industry, province, city)
            else:
                continue
            
            all_results.extend(results)
            
            # 避免请求过快
            time.sleep(random.uniform(2, 4))
        
        return pd.DataFrame(all_results)
    
    def _search_bidding_info(self, industry: str, province: str = None, city: str = None) -> List[Dict]:
        """搜索招标中标信息"""
        results = []
        
        # 生成招标相关关键词
        keywords = self._generate_bidding_keywords(industry, province, city)
        
        # 搜索政府采购网站
        for site_config in self.config.get('bidding_sites', {}).get('government_procurement', []):
            if not site_config.get('enabled', False):
                continue
                
            site_results = self._search_bidding_site(site_config, keywords)
            results.extend(site_results)
            
            time.sleep(random.uniform(1, 2))
        
        # 搜索地方招标网站
        if province:
            regional_sites = self.config.get('bidding_sites', {}).get('regional_bidding', [])
            for site_config in regional_sites:
                if not site_config.get('enabled', False):
                    continue
                
                # 检查是否匹配地区
                if site_config.get('region') and province not in site_config.get('region', ''):
                    continue
                
                site_results = self._search_bidding_site(site_config, keywords)
                results.extend(site_results)
                
                time.sleep(random.uniform(1, 2))
        
        return results
    
    def _search_real_estate_info(self, industry: str, province: str = None, city: str = None) -> List[Dict]:
        """搜索房地产项目信息"""
        results = []
        
        # 生成房地产相关关键词
        keywords = self._generate_real_estate_keywords(industry, province, city)
        
        # 搜索房地产项目网站
        for site_config in self.config.get('real_estate_sites', {}).get('project_info', []):
            if not site_config.get('enabled', False):
                continue
                
            site_results = self._search_real_estate_site(site_config, keywords)
            results.extend(site_results)
            
            time.sleep(random.uniform(1, 2))
        
        return results
    
    def _search_hotel_info(self, industry: str, province: str = None, city: str = None) -> List[Dict]:
        """搜索酒店建设信息"""
        results = []
        
        # 生成酒店相关关键词
        keywords = self._generate_hotel_keywords(industry, province, city)
        
        # 搜索酒店行业网站
        for site_config in self.config.get('hotel_construction_sites', {}).get('industry_news', []):
            if not site_config.get('enabled', False):
                continue
                
            site_results = self._search_hotel_site(site_config, keywords)
            results.extend(site_results)
            
            time.sleep(random.uniform(1, 2))
        
        return results
    
    def _generate_bidding_keywords(self, industry: str, province: str = None, city: str = None) -> List[str]:
        """生成招标相关关键词"""
        keywords = []
        
        # 获取基础关键词模板
        bidding_keywords = self.config.get('search_keywords', {}).get('bidding_keywords', {})
        
        # 构建区域信息
        region = self.region_manager.get_region_display_name(province, city)
        
        # 根据行业生成关键词
        if '酒店' in industry or '旅游' in industry:
            base_keywords = bidding_keywords.get('construction', [])
        elif '房地产' in industry or '建筑' in industry:
            base_keywords = bidding_keywords.get('real_estate', [])
        else:
            base_keywords = bidding_keywords.get('general', [])
        
        # 替换模板变量
        for keyword_template in base_keywords:
            keyword = keyword_template.format(
                region=region,
                industry=industry,
                province=province or '',
                city=city or ''
            )
            keywords.append(keyword.strip())
        
        return keywords
    
    def _generate_real_estate_keywords(self, industry: str, province: str = None, city: str = None) -> List[str]:
        """生成房地产相关关键词"""
        keywords = []
        
        real_estate_keywords = self.config.get('search_keywords', {}).get('real_estate_keywords', {})
        region = self.region_manager.get_region_display_name(province, city)
        
        # 项目信息关键词
        for keyword_template in real_estate_keywords.get('project_info', []):
            keyword = keyword_template.format(region=region, industry=industry)
            keywords.append(keyword.strip())
        
        # 企业信息关键词
        for keyword_template in real_estate_keywords.get('company_info', []):
            keyword = keyword_template.format(region=region, industry=industry)
            keywords.append(keyword.strip())
        
        return keywords
    
    def _generate_hotel_keywords(self, industry: str, province: str = None, city: str = None) -> List[str]:
        """生成酒店相关关键词"""
        keywords = []
        
        hotel_keywords = self.config.get('search_keywords', {}).get('hotel_keywords', {})
        region = self.region_manager.get_region_display_name(province, city)
        
        # 项目信息关键词
        for keyword_template in hotel_keywords.get('project_info', []):
            keyword = keyword_template.format(region=region, industry=industry)
            keywords.append(keyword.strip())
        
        # 行业信息关键词
        for keyword_template in hotel_keywords.get('industry_info', []):
            keyword = keyword_template.format(region=region, industry=industry)
            keywords.append(keyword.strip())
        
        return keywords
    
    def _search_bidding_site(self, site_config: Dict, keywords: List[str]) -> List[Dict]:
        """搜索招标网站"""
        results = []
        site_name = site_config.get('name', '未知网站')
        
        self.logger.info(f"搜索招标网站: {site_name}")
        
        for keyword in keywords[:3]:  # 限制关键词数量
            try:
                # 构建搜索URL
                search_url = self._build_search_url(site_config, keyword)
                if not search_url:
                    continue
                
                # 发送请求
                response = self.session.get(search_url, timeout=30)
                if response.status_code != 200:
                    continue
                
                # 解析结果
                page_results = self._parse_bidding_results(response.text, site_config, keyword)
                results.extend(page_results)
                
                self.logger.info(f"{site_name} - 关键词 '{keyword}': {len(page_results)} 条结果")
                
                time.sleep(random.uniform(1, 2))
                
            except Exception as e:
                self.logger.error(f"搜索 {site_name} 出错: {e}")
                continue
        
        return results
    
    def _search_real_estate_site(self, site_config: Dict, keywords: List[str]) -> List[Dict]:
        """搜索房地产网站"""
        results = []
        site_name = site_config.get('name', '未知网站')
        
        self.logger.info(f"搜索房地产网站: {site_name}")
        
        # 实现房地产网站搜索逻辑
        # 由于房地产网站结构复杂，这里先返回空结果
        # 后续可以针对具体网站实现专门的解析逻辑
        
        return results
    
    def _search_hotel_site(self, site_config: Dict, keywords: List[str]) -> List[Dict]:
        """搜索酒店网站"""
        results = []
        site_name = site_config.get('name', '未知网站')
        
        self.logger.info(f"搜索酒店网站: {site_name}")
        
        # 实现酒店网站搜索逻辑
        # 由于酒店网站结构复杂，这里先返回空结果
        # 后续可以针对具体网站实现专门的解析逻辑
        
        return results
    
    def _build_search_url(self, site_config: Dict, keyword: str) -> Optional[str]:
        """构建搜索URL"""
        search_url = site_config.get('search_url')
        if not search_url:
            return None
        
        # 简单的URL构建，实际使用时需要根据具体网站调整
        if '?' in search_url:
            return f"{search_url}&keyword={quote(keyword)}"
        else:
            return f"{search_url}?keyword={quote(keyword)}"
    
    def _parse_bidding_results(self, html_content: str, site_config: Dict, keyword: str) -> List[Dict]:
        """解析招标结果"""
        results = []
        
        if not BeautifulSoup:
            return results
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找结果项 - 这里需要根据具体网站结构调整
            result_items = soup.find_all(['div', 'li', 'tr'], class_=re.compile(r'(result|item|row|list)'))
            
            for item in result_items:
                try:
                    # 提取项目信息
                    project_info = self._extract_bidding_info(item, site_config)
                    if project_info:
                        project_info['source'] = site_config.get('name', '未知来源')
                        project_info['search_keyword'] = keyword
                        project_info['data_type'] = 'bidding'
                        results.append(project_info)
                        
                except Exception as e:
                    self.logger.debug(f"解析招标项目出错: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"解析招标页面出错: {e}")
        
        return results
    
    def _extract_bidding_info(self, item_element, site_config: Dict) -> Optional[Dict]:
        """提取招标信息"""
        try:
            # 提取标题
            title_elem = item_element.find(['h1', 'h2', 'h3', 'h4', 'a'], class_=re.compile(r'(title|name|subject)'))
            if not title_elem:
                title_elem = item_element.find('a')
            
            if not title_elem:
                return None
            
            title = title_elem.get_text().strip()
            link = title_elem.get('href', '') if title_elem.name == 'a' else ''
            
            # 提取其他信息
            content = item_element.get_text()
            
            # 提取金额信息
            amount_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:万元|万|元)', content)
            amount = amount_match.group(1) if amount_match else ''
            
            # 提取日期信息
            date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', content)
            date = date_match.group(1) if date_match else ''
            
            # 过滤无效结果
            if not self._is_valid_bidding_result(title, content):
                return None
            
            return {
                'company_name': self._extract_company_from_title(title),
                'project_name': title,
                'industry': '招标项目',
                'website': urljoin(site_config.get('base_url', ''), link) if link else '',
                'description': content[:200] + '...' if len(content) > 200 else content,
                'amount': amount,
                'announcement_date': date,
                'location': self._extract_location_from_content(content)
            }
            
        except Exception as e:
            self.logger.debug(f"提取招标信息出错: {e}")
            return None
    
    def _is_valid_bidding_result(self, title: str, content: str) -> bool:
        """验证招标结果是否有效"""
        # 检查必须包含的关键词
        required_keywords = self.config.get('filtering_rules', {}).get('required_keywords', {}).get('bidding', [])

        for keyword_group in required_keywords:
            if not any(keyword in title or keyword in content for keyword in keyword_group):
                return False

        # 检查排除的关键词
        exclude_keywords = ['招聘', '求职', '新闻', '百科', '学校', '教育', '医院', '政府办公']
        if any(keyword in title for keyword in exclude_keywords):
            return False

        # 检查是否与目标行业相关
        target_keywords = ['酒店', '宾馆', '度假村', '民宿', '房地产', '地产', '建筑', '工程', '装修', '设计']
        if not any(keyword in title or keyword in content for keyword in target_keywords):
            return False

        return True
    
    def _extract_company_from_title(self, title: str) -> str:
        """从标题中提取公司名称"""
        # 查找公司名称模式
        company_patterns = [
            r'([^，。；：\s]+(?:公司|企业|集团|有限|股份|科技|建设|工程))',
            r'投标人[:：]\s*([^，。；\s]+)',
            r'中标单位[:：]\s*([^，。；\s]+)'
        ]
        
        for pattern in company_patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1).strip()
        
        return title[:50]  # 如果没找到，返回标题前50个字符
    
    def _extract_location_from_content(self, content: str) -> str:
        """从内容中提取位置信息"""
        # 查找地址模式
        location_patterns = [
            r'([^，。；\s]*(?:省|市|区|县|街道|路|号)[^，。；\s]*)',
            r'地址[:：]\s*([^，。；\n]+)',
            r'位置[:：]\s*([^，。；\n]+)'
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1).strip()
        
        return ''
