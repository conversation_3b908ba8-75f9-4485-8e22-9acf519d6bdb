#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始安装真实数据爬取系统...');

async function runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
        console.log(`📦 执行: ${command} ${args.join(' ')}`);
        
        const child = spawn(command, args, {
            stdio: 'inherit',
            shell: true,
            ...options
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`命令执行失败，退出码: ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

async function createEnvFile() {
    const envContent = `# 真实数据爬取系统配置文件

# 抖音配置
DOUYIN_USER_URL=
DOUYIN_COOKIES=

# 快手配置  
KUAISHOU_USER_URL=
KUAISHOU_COOKIES=

# 企查查配置 (需要真实账号)
QICHACHA_USERNAME=
QICHACHA_PASSWORD=
QICHACHA_COOKIES=

# 天眼查配置
TIANYANCHA_USERNAME=
TIANYANCHA_PASSWORD=
TIANYANCHA_API_KEY=

# 代理配置 (可选)
PROXY_HOST=
PROXY_PORT=
PROXY_USERNAME=
PROXY_PASSWORD=

# OCR验证码识别 (推荐配置)
OCR_API_KEY=
OCR_PROVIDER=2captcha

# 数据库配置 (可选)
MONGODB_URL=mongodb://localhost:27017/crawler_data
REDIS_URL=redis://localhost:6379

# 系统配置
HEADLESS=false
CONCURRENT_LIMIT=3
REQUEST_DELAY=2000
`;

    fs.writeFileSync('.env', envContent);
    console.log('✅ .env 配置文件已创建');
}

async function createDirectories() {
    const dirs = [
        'data',
        'data/douyin',
        'data/kuaishou', 
        'data/qichacha',
        'data/tianyancha',
        'data/industry',
        'data/reports',
        'logs'
    ];

    for (const dir of dirs) {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.log(`📁 创建目录: ${dir}`);
        }
    }
}

async function installDependencies() {
    console.log('\n📦 安装Node.js依赖...');
    
    try {
        // 检查是否有npm
        await runCommand('npm', ['--version']);
        
        // 安装依赖
        await runCommand('npm', ['install']);
        
        console.log('✅ Node.js依赖安装完成');
        
        // 安装浏览器
        console.log('\n🌐 安装Playwright浏览器...');
        await runCommand('npx', ['playwright', 'install']);
        
        console.log('✅ 浏览器安装完成');
        
    } catch (error) {
        console.error('❌ 依赖安装失败:', error.message);
        console.log('\n💡 请手动执行以下命令:');
        console.log('npm install');
        console.log('npx playwright install');
    }
}

async function testInstallation() {
    console.log('\n🧪 测试安装结果...');
    
    try {
        // 测试基本模块导入
        const CrawlerManager = require('./src/crawlerManager');
        console.log('✅ 爬虫模块导入成功');
        
        // 测试浏览器配置
        const BrowserManager = require('./src/config/browserConfig');
        const browserManager = new BrowserManager();
        console.log('✅ 浏览器配置模块正常');
        
        console.log('\n🎉 安装测试通过！');
        
    } catch (error) {
        console.error('❌ 安装测试失败:', error.message);
        return false;
    }
    
    return true;
}

async function showUsageInstructions() {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 真实数据爬取系统安装完成！');
    console.log('='.repeat(60));
    
    console.log('\n📋 使用说明:');
    console.log('');
    console.log('1. 配置账号信息 (编辑 .env 文件):');
    console.log('   - 企查查账号密码 (必需)');
    console.log('   - 代理服务器 (推荐)');
    console.log('   - OCR验证码服务 (推荐)');
    console.log('');
    console.log('2. 测试系统:');
    console.log('   node src/index.js test');
    console.log('');
    console.log('3. 爬取企业数据:');
    console.log('   node src/index.js qichacha 腾讯科技');
    console.log('');
    console.log('4. 爬取抖音数据:');
    console.log('   node src/index.js douyin https://www.douyin.com/user/xxx');
    console.log('');
    console.log('5. 爬取快手数据:');
    console.log('   node src/index.js kuaishou https://www.kuaishou.com/profile/xxx');
    console.log('');
    console.log('6. 按行业爬取:');
    console.log('   node src/index.js industry 酒店建设 广东省');
    console.log('');
    console.log('📁 数据保存位置: ./data/');
    console.log('📊 报告生成: node src/index.js report');
    console.log('');
    console.log('⚠️  重要提醒:');
    console.log('   - 请遵守各平台的使用条款');
    console.log('   - 建议配置代理避免IP被封');
    console.log('   - 企查查需要真实账号登录');
    console.log('   - 首次运行建议设置 HEADLESS=false 观察过程');
}

async function main() {
    try {
        console.log('开始安装真实数据爬取系统...\n');
        
        // 1. 创建配置文件
        await createEnvFile();
        
        // 2. 创建目录结构
        await createDirectories();
        
        // 3. 安装依赖
        await installDependencies();
        
        // 4. 测试安装
        const testPassed = await testInstallation();
        
        if (testPassed) {
            // 5. 显示使用说明
            await showUsageInstructions();
        } else {
            console.log('\n❌ 安装过程中出现问题，请检查错误信息');
        }
        
    } catch (error) {
        console.error('❌ 安装失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}
