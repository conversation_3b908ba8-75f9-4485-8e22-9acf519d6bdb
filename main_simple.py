#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户信息收集系统 - 简化版主程序
不依赖streamlit，使用命令行界面
"""

import sys
import os
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 检查是否有必要的依赖
try:
    import requests
    from bs4 import BeautifulSoup
    import yaml
except ImportError as e:
    print(f"❌ 缺少必要的依赖包: {e}")
    print("💡 请运行: pip install requests beautifulsoup4 pyyaml")
    sys.exit(1)

def print_banner():
    """打印横幅"""
    banner = """
    🕷️ 智能客户信息收集系统 - 简化版
    ================================
    专业级爬虫 + 数据分析 + 可视化
    ================================
    """
    print(banner)

def show_menu():
    """显示主菜单"""
    print("\n🎯 功能菜单:")
    print("  1. 🕷️  开始收集客户信息")
    print("  2. 📊 查看已收集数据")
    print("  3. 🔍 搜索客户信息")
    print("  4. 📈 数据统计分析")
    print("  5. 💾 导出数据")
    print("  6. ⚙️  系统设置")
    print("  7. 🔍 环境状态检查")
    print("  0. 🚪 退出系统")
    print()

def collect_customer_info():
    """收集客户信息"""
    print("\n🕷️ 客户信息收集")
    print("-" * 40)
    
    # 获取用户输入
    industry = input("请输入目标行业 (例如: 人工智能): ").strip()
    if not industry:
        industry = "人工智能"
        print(f"使用默认行业: {industry}")
    
    max_results = input("最大收集数量 (默认5): ").strip()
    try:
        max_results = int(max_results) if max_results else 5
    except ValueError:
        max_results = 5
    
    print(f"\n🚀 开始收集 '{industry}' 行业信息，最多 {max_results} 条...")
    
    # 这里调用simple_demo.py的功能
    try:
        from simple_demo import simple_web_crawler, save_to_database, display_results
        
        # 开始爬取
        results = simple_web_crawler(industry, max_results)
        
        if results:
            # 显示结果
            display_results(results)
            
            # 保存到数据库
            save_to_database(results, industry)
            
            print(f"\n✅ 成功收集并保存了 {len(results)} 条客户信息")
        else:
            print("❌ 未能收集到有效数据")
            
    except Exception as e:
        print(f"❌ 收集过程中出现错误: {e}")

def view_collected_data():
    """查看已收集数据"""
    print("\n📊 已收集数据")
    print("-" * 40)
    
    try:
        import sqlite3
        
        db_path = "data/demo_customer_data.db"
        if not Path(db_path).exists():
            print("❌ 数据库文件不存在，请先收集数据")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询总记录数
        cursor.execute("SELECT COUNT(*) FROM customer_info")
        total_count = cursor.fetchone()[0]
        
        if total_count == 0:
            print("📋 数据库中暂无数据")
            conn.close()
            return
        
        print(f"📊 数据库中共有 {total_count} 条记录")
        
        # 查询最近的记录
        limit = min(10, total_count)
        cursor.execute('''
            SELECT company_name, industry, website, created_at 
            FROM customer_info 
            ORDER BY created_at DESC 
            LIMIT ?
        ''', (limit,))
        
        records = cursor.fetchall()
        
        print(f"\n📋 最近的 {len(records)} 条记录:")
        for i, record in enumerate(records, 1):
            print(f"  {i}. {record[0]} ({record[1]}) - {record[3]}")
            if record[2]:
                print(f"     网站: {record[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查看数据时出现错误: {e}")

def search_customer_info():
    """搜索客户信息"""
    print("\n🔍 搜索客户信息")
    print("-" * 40)
    
    keyword = input("请输入搜索关键词: ").strip()
    if not keyword:
        print("❌ 请输入有效的搜索关键词")
        return
    
    try:
        import sqlite3
        
        db_path = "data/demo_customer_data.db"
        if not Path(db_path).exists():
            print("❌ 数据库文件不存在")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 搜索匹配的记录
        cursor.execute('''
            SELECT company_name, industry, website, description, created_at
            FROM customer_info 
            WHERE company_name LIKE ? OR description LIKE ?
            ORDER BY created_at DESC
        ''', (f'%{keyword}%', f'%{keyword}%'))
        
        results = cursor.fetchall()
        
        if results:
            print(f"\n🎯 找到 {len(results)} 条匹配记录:")
            for i, record in enumerate(results, 1):
                print(f"\n  {i}. {record[0]} ({record[1]})")
                if record[2]:
                    print(f"     网站: {record[2]}")
                if record[3]:
                    print(f"     描述: {record[3][:100]}...")
                print(f"     时间: {record[4]}")
        else:
            print(f"❌ 未找到包含 '{keyword}' 的记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 搜索时出现错误: {e}")

def data_statistics():
    """数据统计分析"""
    print("\n📈 数据统计分析")
    print("-" * 40)
    
    try:
        import sqlite3
        from collections import Counter
        
        db_path = "data/demo_customer_data.db"
        if not Path(db_path).exists():
            print("❌ 数据库文件不存在")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 总体统计
        cursor.execute("SELECT COUNT(*) FROM customer_info")
        total_count = cursor.fetchone()[0]
        print(f"📊 总记录数: {total_count}")
        
        if total_count == 0:
            print("📋 暂无数据进行统计")
            conn.close()
            return
        
        # 行业分布
        cursor.execute("SELECT industry, COUNT(*) FROM customer_info GROUP BY industry")
        industry_stats = cursor.fetchall()
        
        print(f"\n🏭 行业分布:")
        for industry, count in industry_stats:
            percentage = (count / total_count) * 100
            print(f"  • {industry}: {count} 条 ({percentage:.1f}%)")
        
        # 数据源统计
        cursor.execute("SELECT source, COUNT(*) FROM customer_info GROUP BY source")
        source_stats = cursor.fetchall()
        
        print(f"\n📡 数据源分布:")
        for source, count in source_stats:
            percentage = (count / total_count) * 100
            print(f"  • {source}: {count} 条 ({percentage:.1f}%)")
        
        # 完整度统计
        cursor.execute("SELECT company_name, website, description FROM customer_info")
        all_records = cursor.fetchall()
        
        complete_website = sum(1 for r in all_records if r[1])
        complete_desc = sum(1 for r in all_records if r[2])
        
        print(f"\n📋 数据完整度:")
        print(f"  • 公司名称: {total_count}/{total_count} (100.0%)")
        print(f"  • 网站信息: {complete_website}/{total_count} ({complete_website/total_count*100:.1f}%)")
        print(f"  • 描述信息: {complete_desc}/{total_count} ({complete_desc/total_count*100:.1f}%)")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 统计分析时出现错误: {e}")

def export_data():
    """导出数据"""
    print("\n💾 导出数据")
    print("-" * 40)
    
    try:
        import sqlite3
        import csv
        import json
        
        db_path = "data/demo_customer_data.db"
        if not Path(db_path).exists():
            print("❌ 数据库文件不存在")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM customer_info")
        records = cursor.fetchall()
        
        if not records:
            print("❌ 数据库中没有数据可导出")
            conn.close()
            return
        
        # 获取列名
        cursor.execute("PRAGMA table_info(customer_info)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 导出CSV
        csv_file = f"data/export_customer_data_{int(time.time())}.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(columns)
            writer.writerows(records)
        
        print(f"✅ CSV文件已导出: {csv_file}")
        
        # 导出JSON
        json_file = f"data/export_customer_data_{int(time.time())}.json"
        data = []
        for record in records:
            data.append(dict(zip(columns, record)))
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ JSON文件已导出: {json_file}")
        print(f"📊 共导出 {len(records)} 条记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 导出数据时出现错误: {e}")

def system_settings():
    """系统设置"""
    print("\n⚙️ 系统设置")
    print("-" * 40)
    
    print("🔧 当前配置:")
    print("  • 数据库路径: data/demo_customer_data.db")
    print("  • 默认搜索引擎: 百度")
    print("  • 默认延时: 1-3秒")
    print("  • 虚拟环境: customer_crawler_env")
    
    print("\n💡 可用操作:")
    print("  1. 清理数据库")
    print("  2. 备份数据库")
    print("  3. 查看日志")
    print("  0. 返回主菜单")
    
    choice = input("\n请选择操作 (0-3): ").strip()
    
    if choice == "1":
        confirm = input("⚠️ 确认清理数据库？这将删除所有数据 (y/N): ").strip().lower()
        if confirm in ['y', 'yes']:
            try:
                import sqlite3
                db_path = "data/demo_customer_data.db"
                if Path(db_path).exists():
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM customer_info")
                    conn.commit()
                    conn.close()
                    print("✅ 数据库已清理")
                else:
                    print("❌ 数据库文件不存在")
            except Exception as e:
                print(f"❌ 清理失败: {e}")
    
    elif choice == "2":
        try:
            import shutil
            db_path = "data/demo_customer_data.db"
            if Path(db_path).exists():
                backup_path = f"data/backup_customer_data_{int(time.time())}.db"
                shutil.copy2(db_path, backup_path)
                print(f"✅ 数据库已备份: {backup_path}")
            else:
                print("❌ 数据库文件不存在")
        except Exception as e:
            print(f"❌ 备份失败: {e}")
    
    elif choice == "3":
        log_dir = Path("logs")
        if log_dir.exists():
            log_files = list(log_dir.glob("*.log"))
            if log_files:
                print(f"📋 找到 {len(log_files)} 个日志文件:")
                for log_file in log_files:
                    size = log_file.stat().st_size
                    print(f"  • {log_file.name} ({size} bytes)")
            else:
                print("📋 暂无日志文件")
        else:
            print("❌ 日志目录不存在")

def check_environment():
    """检查环境状态"""
    print("\n🔍 环境状态检查")
    print("-" * 40)
    
    try:
        # 运行环境检查脚本
        import subprocess
        result = subprocess.run([sys.executable, "venv_status.py"], 
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("错误信息:", result.stderr)
    except Exception as e:
        print(f"❌ 环境检查失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    print(f"🕐 启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查数据目录
    Path("data").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    while True:
        try:
            show_menu()
            choice = input("请选择功能 (0-7): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用客户信息收集系统！")
                break
            elif choice == "1":
                collect_customer_info()
            elif choice == "2":
                view_collected_data()
            elif choice == "3":
                search_customer_info()
            elif choice == "4":
                data_statistics()
            elif choice == "5":
                export_data()
            elif choice == "6":
                system_settings()
            elif choice == "7":
                check_environment()
            else:
                print("❌ 无效选择，请重新输入")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 程序出现错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
