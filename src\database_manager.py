#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块 - 数据存储和检索
专家级数据库设计，支持高效查询和数据管理
"""

import sqlite3
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
import json
from datetime import datetime
import shutil

try:
    from .utils.logger import get_logger
except ImportError:
    from utils.logger import get_logger

class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self, db_path: str = "data/customer_data.db"):
        """初始化数据库管理器"""
        self.logger = get_logger(__name__)
        self.db_path = Path(db_path)
        
        # 确保数据目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建客户信息主表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS customer_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        company_name TEXT NOT NULL,
                        industry TEXT,
                        website TEXT,
                        phone TEXT,
                        email TEXT,
                        address TEXT,
                        contact_person TEXT,
                        company_size TEXT,
                        registration_capital TEXT,
                        business_scope TEXT,
                        description TEXT,
                        source TEXT,
                        domain TEXT,
                        data_completeness REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(company_name, industry)
                    )
                ''')
                
                # 创建爬取历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS crawl_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        industry TEXT NOT NULL,
                        total_records INTEGER,
                        success_records INTEGER,
                        crawl_config TEXT,
                        start_time TIMESTAMP,
                        end_time TIMESTAMP,
                        status TEXT,
                        error_message TEXT
                    )
                ''')
                
                # 创建数据源统计表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS source_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source_name TEXT NOT NULL,
                        industry TEXT,
                        records_count INTEGER,
                        success_rate REAL,
                        last_crawl TIMESTAMP,
                        UNIQUE(source_name, industry)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_company_name ON customer_info(company_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_industry ON customer_info(industry)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_source ON customer_info(source)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_created_at ON customer_info(created_at)')
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_data(self, df: pd.DataFrame, industry: str, crawl_config: Dict = None) -> bool:
        """保存数据到数据库"""
        try:
            start_time = datetime.now()
            
            with sqlite3.connect(self.db_path) as conn:
                # 记录爬取历史
                crawl_id = self._record_crawl_start(conn, industry, len(df), crawl_config, start_time)
                
                # 准备数据
                df_to_save = df.copy()
                
                # 确保必要字段存在
                required_fields = ['company_name', 'industry']
                for field in required_fields:
                    if field not in df_to_save.columns:
                        if field == 'industry':
                            df_to_save[field] = industry
                        else:
                            df_to_save[field] = ''
                
                # 添加时间戳（转换为字符串格式）
                timestamp_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
                df_to_save['created_at'] = timestamp_str
                df_to_save['updated_at'] = timestamp_str
                
                # 保存到数据库（使用REPLACE处理重复数据）
                success_count = 0
                for _, row in df_to_save.iterrows():
                    try:
                        # 检查是否已存在
                        existing = conn.execute(
                            'SELECT id FROM customer_info WHERE company_name = ? AND industry = ?',
                            (row.get('company_name', ''), industry)
                        ).fetchone()
                        
                        if existing:
                            # 更新现有记录
                            self._update_record(conn, existing[0], row)
                        else:
                            # 插入新记录
                            self._insert_record(conn, row)
                        
                        success_count += 1
                        
                    except Exception as e:
                        self.logger.warning(f"保存记录失败: {e}")
                        continue
                
                # 更新爬取历史
                end_time = datetime.now()
                self._record_crawl_end(conn, crawl_id, success_count, end_time, "SUCCESS")
                
                # 更新数据源统计
                self._update_source_stats(conn, df_to_save, industry)
                
                conn.commit()
                
                self.logger.info(f"数据保存完成: {success_count}/{len(df)} 条记录成功保存")
                return True
                
        except Exception as e:
            self.logger.error(f"数据保存失败: {e}")
            return False
    
    def _record_crawl_start(self, conn: sqlite3.Connection, industry: str, 
                           total_records: int, crawl_config: Dict, start_time: datetime) -> int:
        """记录爬取开始"""
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO crawl_history (industry, total_records, crawl_config, start_time, status)
            VALUES (?, ?, ?, ?, ?)
        ''', (industry, total_records, json.dumps(crawl_config) if crawl_config else None,
              start_time.strftime('%Y-%m-%d %H:%M:%S'), "RUNNING"))
        
        return cursor.lastrowid
    
    def _record_crawl_end(self, conn: sqlite3.Connection, crawl_id: int, 
                         success_records: int, end_time: datetime, status: str):
        """记录爬取结束"""
        conn.execute('''
            UPDATE crawl_history
            SET success_records = ?, end_time = ?, status = ?
            WHERE id = ?
        ''', (success_records, end_time.strftime('%Y-%m-%d %H:%M:%S'), status, crawl_id))
    
    def _insert_record(self, conn: sqlite3.Connection, row: pd.Series):
        """插入新记录"""
        fields = [
            'company_name', 'industry', 'website', 'phone', 'email', 'address',
            'contact_person', 'company_size', 'registration_capital', 'business_scope',
            'description', 'source', 'domain', 'data_completeness', 'created_at', 'updated_at'
        ]
        
        values = []
        placeholders = []
        
        for field in fields:
            if field in row and pd.notna(row[field]):
                values.append(row[field])
                placeholders.append('?')
            else:
                values.append(None)
                placeholders.append('?')
        
        sql = f'''
            INSERT INTO customer_info ({', '.join(fields)})
            VALUES ({', '.join(placeholders)})
        '''
        
        conn.execute(sql, values)
    
    def _update_record(self, conn: sqlite3.Connection, record_id: int, row: pd.Series):
        """更新现有记录"""
        update_fields = [
            'website', 'phone', 'email', 'address', 'contact_person',
            'company_size', 'registration_capital', 'business_scope',
            'description', 'source', 'domain', 'data_completeness'
        ]
        
        set_clauses = []
        values = []
        
        for field in update_fields:
            if field in row and pd.notna(row[field]):
                set_clauses.append(f'{field} = ?')
                values.append(row[field])
        
        if set_clauses:
            set_clauses.append('updated_at = CURRENT_TIMESTAMP')
            values.append(record_id)
            
            sql = f'''
                UPDATE customer_info 
                SET {', '.join(set_clauses)}
                WHERE id = ?
            '''
            
            conn.execute(sql, values)
    
    def _update_source_stats(self, conn: sqlite3.Connection, df: pd.DataFrame, industry: str):
        """更新数据源统计"""
        if 'source' not in df.columns:
            return
        
        source_counts = df['source'].value_counts()
        
        for source, count in source_counts.items():
            # 计算成功率（简化版本）
            success_rate = 100.0  # 这里可以根据实际情况计算
            
            conn.execute('''
                INSERT OR REPLACE INTO source_stats 
                (source_name, industry, records_count, success_rate, last_crawl)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (source, industry, count, success_rate))
    
    def get_data(self, industry: str = None, limit: int = None) -> pd.DataFrame:
        """获取数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                sql = "SELECT * FROM customer_info"
                params = []
                
                if industry:
                    sql += " WHERE industry = ?"
                    params.append(industry)
                
                sql += " ORDER BY created_at DESC"
                
                if limit:
                    sql += " LIMIT ?"
                    params.append(limit)
                
                df = pd.read_sql_query(sql, conn, params=params)
                return df
                
        except Exception as e:
            self.logger.error(f"数据查询失败: {e}")
            return pd.DataFrame()
    
    def get_total_records(self) -> int:
        """获取总记录数"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM customer_info")
                return cursor.fetchone()[0]
        except Exception as e:
            self.logger.error(f"查询总记录数失败: {e}")
            return 0
    
    def get_recent_industries(self, limit: int = 5) -> List[Dict]:
        """获取最近收集的行业"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                sql = '''
                    SELECT industry, COUNT(*) as count, MAX(created_at) as last_crawl
                    FROM customer_info 
                    GROUP BY industry 
                    ORDER BY last_crawl DESC 
                    LIMIT ?
                '''
                
                cursor = conn.cursor()
                cursor.execute(sql, (limit,))
                
                results = []
                for row in cursor.fetchall():
                    results.append({
                        'industry': row[0],
                        'count': row[1],
                        'last_crawl': row[2]
                    })
                
                return results
                
        except Exception as e:
            self.logger.error(f"查询最近行业失败: {e}")
            return []
    
    def get_crawl_history(self, limit: int = 10) -> pd.DataFrame:
        """获取爬取历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                sql = '''
                    SELECT * FROM crawl_history 
                    ORDER BY start_time DESC 
                    LIMIT ?
                '''
                
                df = pd.read_sql_query(sql, conn, params=[limit])
                return df
                
        except Exception as e:
            self.logger.error(f"查询爬取历史失败: {e}")
            return pd.DataFrame()
    
    def backup_database(self, backup_path: str = None) -> bool:
        """备份数据库"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"data/backups/customer_data_backup_{timestamp}.db"
            
            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(self.db_path, backup_file)
            
            self.logger.info(f"数据库备份完成: {backup_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return False
    
    def get_table_info(self) -> List[str]:
        """获取数据库表信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                return tables
        except Exception as e:
            self.logger.error(f"获取表信息失败: {e}")
            return []

    def search_companies(self, keyword: str, industry: str = None) -> pd.DataFrame:
        """搜索公司"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                sql = '''
                    SELECT * FROM customer_info
                    WHERE company_name LIKE ? OR description LIKE ?
                '''
                params = [f'%{keyword}%', f'%{keyword}%']

                if industry:
                    sql += " AND industry = ?"
                    params.append(industry)

                sql += " ORDER BY company_name"

                df = pd.read_sql_query(sql, conn, params=params)
                return df

        except Exception as e:
            self.logger.error(f"搜索公司失败: {e}")
            return pd.DataFrame()
