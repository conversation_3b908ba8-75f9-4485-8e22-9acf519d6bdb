#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版演示程序 - 测试虚拟环境和基础功能
不依赖复杂的第三方包，展示核心爬虫逻辑
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import random
from urllib.parse import quote, urljoin
import sqlite3
from pathlib import Path
import yaml

def print_banner():
    """打印横幅"""
    banner = """
    🕷️ 客户信息收集系统 - 简化演示
    ================================
    虚拟环境测试 + 基础爬虫功能
    ================================
    """
    print(banner)

def test_environment():
    """测试环境配置"""
    print("🔍 环境测试")
    print("-" * 40)
    
    # 测试Python标准库
    print("✅ Python标准库正常")
    
    # 测试已安装的包
    try:
        import requests
        print("✅ requests库已安装")
    except ImportError:
        print("❌ requests库未安装")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✅ beautifulsoup4库已安装")
    except ImportError:
        print("❌ beautifulsoup4库未安装")
        return False
    
    try:
        import yaml
        print("✅ pyyaml库已安装")
    except ImportError:
        print("❌ pyyaml库未安装")
        return False
    
    print("✅ 环境测试通过")
    return True

def create_demo_database():
    """创建演示数据库"""
    print("\n💾 创建演示数据库")
    print("-" * 40)
    
    # 确保数据目录存在
    Path("data").mkdir(exist_ok=True)
    
    db_path = "data/demo_customer_data.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建客户信息表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customer_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_name TEXT NOT NULL,
                industry TEXT,
                website TEXT,
                phone TEXT,
                email TEXT,
                description TEXT,
                source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print(f"✅ 数据库创建成功: {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        return False

def simple_web_crawler(keyword, max_results=5):
    """简化版网页爬虫"""
    print(f"\n🕷️ 开始爬取: {keyword}")
    print("-" * 40)
    
    results = []
    
    # 模拟用户代理
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        # 构建搜索URL（使用百度）
        search_url = f"https://www.baidu.com/s?wd={quote(keyword + ' 公司')}"
        
        print(f"🔍 搜索URL: {search_url}")
        
        # 发送请求
        response = requests.get(search_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ 请求成功")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找搜索结果
            result_items = soup.find_all('div', class_='result')
            
            print(f"📋 找到 {len(result_items)} 个搜索结果")
            
            for i, item in enumerate(result_items[:max_results]):
                try:
                    # 提取标题
                    title_elem = item.find('h3')
                    if title_elem:
                        title = title_elem.get_text().strip()
                    else:
                        title = "未知标题"
                    
                    # 提取链接
                    link_elem = item.find('a')
                    if link_elem:
                        link = link_elem.get('href', '')
                    else:
                        link = ""
                    
                    # 提取描述
                    desc_elem = item.find('span')
                    if desc_elem:
                        description = desc_elem.get_text().strip()[:200]
                    else:
                        description = "无描述"
                    
                    # 简单的公司名称提取
                    company_name = extract_company_name(title)
                    
                    result = {
                        'company_name': company_name,
                        'industry': keyword,
                        'website': clean_url(link),
                        'description': description,
                        'source': 'baidu_demo',
                        'title': title
                    }
                    
                    results.append(result)
                    print(f"  {i+1}. {company_name}")
                    
                except Exception as e:
                    print(f"  ⚠️ 解析第{i+1}个结果时出错: {e}")
                    continue
        
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
    
    except Exception as e:
        print(f"❌ 爬取过程出错: {e}")
    
    print(f"✅ 爬取完成，获得 {len(results)} 条有效数据")
    return results

def extract_company_name(title):
    """从标题中提取公司名称"""
    # 移除常见的后缀
    title = title.replace('官网', '').replace('首页', '').replace('主页', '')
    
    # 简单的公司名称提取
    if '有限公司' in title:
        parts = title.split('有限公司')
        if parts[0]:
            return parts[0].strip() + '有限公司'
    
    if '股份' in title:
        parts = title.split('股份')
        if parts[0]:
            return parts[0].strip() + '股份有限公司'
    
    if '集团' in title:
        parts = title.split('集团')
        if parts[0]:
            return parts[0].strip() + '集团'
    
    # 如果没有找到特定模式，返回前20个字符
    return title[:20].strip()

def clean_url(url):
    """清理URL"""
    if not url:
        return ""
    
    # 移除百度跳转
    if 'baidu.com' in url and 'url=' in url:
        try:
            from urllib.parse import unquote, parse_qs, urlparse
            parsed = urlparse(url)
            if 'url' in parse_qs(parsed.query):
                url = parse_qs(parsed.query)['url'][0]
                url = unquote(url)
        except:
            pass
    
    return url

def save_to_database(data, industry):
    """保存数据到数据库"""
    print(f"\n💾 保存数据到数据库")
    print("-" * 40)
    
    if not data:
        print("⚠️ 没有数据需要保存")
        return
    
    try:
        conn = sqlite3.connect("data/demo_customer_data.db")
        cursor = conn.cursor()
        
        saved_count = 0
        for item in data:
            try:
                cursor.execute('''
                    INSERT INTO customer_info 
                    (company_name, industry, website, description, source)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    item.get('company_name', ''),
                    item.get('industry', ''),
                    item.get('website', ''),
                    item.get('description', ''),
                    item.get('source', '')
                ))
                saved_count += 1
            except Exception as e:
                print(f"  ⚠️ 保存记录失败: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        print(f"✅ 成功保存 {saved_count} 条记录")
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")

def display_results(data):
    """显示结果"""
    print(f"\n📊 爬取结果展示")
    print("=" * 60)
    
    if not data:
        print("没有找到数据")
        return
    
    for i, item in enumerate(data, 1):
        print(f"\n企业 {i}:")
        print(f"  公司名称: {item.get('company_name', 'N/A')}")
        print(f"  行业: {item.get('industry', 'N/A')}")
        print(f"  网站: {item.get('website', 'N/A')}")
        print(f"  描述: {item.get('description', 'N/A')[:100]}...")

def query_database():
    """查询数据库中的数据"""
    print(f"\n📋 数据库查询")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect("data/demo_customer_data.db")
        cursor = conn.cursor()
        
        # 查询总记录数
        cursor.execute("SELECT COUNT(*) FROM customer_info")
        total_count = cursor.fetchone()[0]
        print(f"📊 数据库中共有 {total_count} 条记录")
        
        # 查询最近的5条记录
        cursor.execute('''
            SELECT company_name, industry, website, created_at 
            FROM customer_info 
            ORDER BY created_at DESC 
            LIMIT 5
        ''')
        
        recent_records = cursor.fetchall()
        
        if recent_records:
            print("\n🕒 最近的5条记录:")
            for i, record in enumerate(recent_records, 1):
                print(f"  {i}. {record[0]} ({record[1]}) - {record[3]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 测试环境
    if not test_environment():
        print("\n❌ 环境测试失败，请检查依赖包安装")
        return
    
    # 创建数据库
    if not create_demo_database():
        print("\n❌ 数据库创建失败")
        return
    
    # 获取用户输入
    print("\n🎯 请输入要搜索的行业关键词:")
    industry = input("行业关键词 (例如: 人工智能): ").strip()
    
    if not industry:
        industry = "人工智能"
        print(f"使用默认关键词: {industry}")
    
    # 开始爬取
    results = simple_web_crawler(industry, max_results=3)
    
    # 显示结果
    display_results(results)
    
    # 保存到数据库
    save_to_database(results, industry)
    
    # 查询数据库
    query_database()
    
    print(f"\n🎉 演示完成！")
    print(f"💡 数据已保存到: data/demo_customer_data.db")
    print(f"📁 您可以使用SQLite工具查看数据库内容")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n💥 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
