#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码处理功能测试脚本
验证OCR识别、第三方服务集成和验证码动作执行功能
"""

import asyncio
import sys
import logging
import io
from PIL import Image, ImageDraw, ImageFont
from src.captcha_solver import CaptchaSolver, CaptchaType, get_captcha_solver
from src.browser_manager import get_browser_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_test_captcha_image(text: str = "ABCD") -> bytes:
    """创建测试验证码图片"""
    # 创建图片
    img = Image.new('RGB', (120, 40), color='white')
    draw = ImageDraw.Draw(img)
    
    # 绘制文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        # 使用默认字体
        font = ImageFont.load_default()
    
    # 绘制验证码文字
    draw.text((10, 10), text, fill='black', font=font)
    
    # 添加一些干扰线
    for i in range(5):
        import random
        x1, y1 = random.randint(0, 120), random.randint(0, 40)
        x2, y2 = random.randint(0, 120), random.randint(0, 40)
        draw.line([(x1, y1), (x2, y2)], fill='gray', width=1)
    
    # 转换为字节
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    return img_bytes.getvalue()

async def test_captcha_config():
    """测试验证码配置"""
    print("🔍 测试验证码配置...")
    
    try:
        captcha_solver = CaptchaSolver()
        
        # 检查配置加载
        config = captcha_solver.config
        
        print("✅ 验证码配置:")
        print(f"   OCR启用: {config.get('ocr_enabled', False)}")
        print(f"   本地OCR启用: {config.get('local_ocr_enabled', False)}")
        print(f"   超时时间: {config.get('timeout', 30)}s")
        print(f"   重试次数: {config.get('retry_count', 3)}")
        
        # 检查第三方服务配置
        services = config.get('third_party_services', {})
        print(f"   第三方服务: {list(services.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

async def test_local_ocr():
    """测试本地OCR功能"""
    print("🔍 测试本地OCR功能...")
    
    try:
        captcha_solver = CaptchaSolver()
        
        # 创建测试图片
        test_text = "TEST"
        test_image = create_test_captcha_image(test_text)
        
        print(f"✅ 创建测试验证码图片: {test_text}")
        print(f"   图片大小: {len(test_image)} bytes")
        
        # 测试本地OCR
        result = await captcha_solver._local_ocr_recognize(test_image)
        
        print(f"✅ 本地OCR识别结果:")
        print(f"   成功: {result.success}")
        print(f"   结果: {result.result}")
        print(f"   置信度: {result.confidence}")
        
        if result.error_message:
            print(f"   错误: {result.error_message}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 本地OCR测试失败: {e}")
        return False

async def test_online_ocr():
    """测试在线OCR功能"""
    print("🔍 测试在线OCR功能...")
    
    try:
        captcha_solver = CaptchaSolver()
        
        # 创建测试图片
        test_text = "DEMO"
        test_image = create_test_captcha_image(test_text)
        
        # 测试在线OCR
        result = await captcha_solver._online_ocr_recognize(test_image)
        
        print(f"✅ 在线OCR识别结果:")
        print(f"   成功: {result.success}")
        print(f"   结果: {result.result}")
        print(f"   置信度: {result.confidence}")
        
        if result.error_message:
            print(f"   错误: {result.error_message}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 在线OCR测试失败: {e}")
        return False

async def test_third_party_services():
    """测试第三方服务"""
    print("🔍 测试第三方服务...")
    
    try:
        captcha_solver = CaptchaSolver()
        
        # 创建测试图片
        test_image = create_test_captcha_image("SERV")
        
        # 测试各个第三方服务
        services = captcha_solver.third_party_services
        
        for service_name, service_config in services.items():
            print(f"   测试 {service_name}...")
            
            result = await captcha_solver._third_party_solve(
                test_image, service_name, service_config
            )
            
            print(f"   {service_name} 结果: {result.success} - {result.result}")
        
        print("✅ 第三方服务测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 第三方服务测试失败: {e}")
        return False

async def test_captcha_types():
    """测试不同验证码类型"""
    print("🔍 测试不同验证码类型...")
    
    try:
        captcha_solver = CaptchaSolver()
        
        # 测试文字验证码
        text_image = create_test_captcha_image("ABCD")
        text_result = await captcha_solver._solve_text_captcha(text_image)
        print(f"✅ 文字验证码: {text_result.success} - {text_result.result}")
        
        # 测试点击验证码
        click_result = await captcha_solver._solve_click_captcha(text_image)
        print(f"✅ 点击验证码: {click_result.success} - {click_result.result}")
        
        # 验证点击坐标格式
        if click_result.success and isinstance(click_result.result, list):
            for i, (x, y) in enumerate(click_result.result):
                print(f"   点击点 {i+1}: ({x}, {y})")
        
        return text_result.success and click_result.success
        
    except Exception as e:
        print(f"❌ 验证码类型测试失败: {e}")
        return False

async def test_browser_integration():
    """测试浏览器集成"""
    print("🔍 测试浏览器集成...")
    
    try:
        # 创建浏览器管理器
        browser_manager = get_browser_manager(headless=True)
        await browser_manager.start()
        
        # 创建浏览器实例和上下文
        browser = await browser_manager.get_or_create_browser()
        context = await browser_manager.create_context(browser)
        page = await browser_manager.create_page(context)
        
        print("✅ 浏览器环境创建成功")
        
        # 创建一个简单的HTML页面用于测试
        html_content = """
        <!DOCTYPE html>
        <html>
        <head><title>验证码测试</title></head>
        <body>
            <img id="captcha" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" width="120" height="40">
            <input id="captcha-input" type="text" placeholder="验证码">
            <button id="submit">提交</button>
        </body>
        </html>
        """
        
        # 设置页面内容
        await page.set_content(html_content)
        
        # 测试验证码处理
        success = await browser_manager.handle_captcha(
            page, 
            "#captcha", 
            "#captcha-input", 
            CaptchaType.TEXT
        )
        
        if success:
            print("✅ 浏览器验证码处理成功")
        else:
            print("⚠️ 浏览器验证码处理失败")
        
        # 清理
        await page.close()
        await browser_manager.close_context(context)
        
        from src.browser_manager import cleanup_browser_manager
        await cleanup_browser_manager()
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器集成测试失败: {e}")
        return False

async def test_performance():
    """测试性能"""
    print("🔍 测试性能...")
    
    try:
        captcha_solver = CaptchaSolver()
        
        # 创建多个测试图片
        test_images = []
        for i in range(10):
            img = create_test_captcha_image(f"T{i:03d}")
            test_images.append(img)
        
        print(f"✅ 创建了 {len(test_images)} 个测试图片")
        
        # 测试批量识别性能
        import time
        start_time = time.time()
        
        results = []
        for img in test_images:
            result = await captcha_solver._local_ocr_recognize(img)
            results.append(result)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        success_count = sum(1 for r in results if r.success)
        
        print(f"✅ 性能测试结果:")
        print(f"   总时间: {total_time:.2f}s")
        print(f"   平均每个: {total_time/len(test_images)*1000:.1f}ms")
        print(f"   成功率: {success_count}/{len(test_images)} ({success_count/len(test_images)*100:.1f}%)")
        
        # 性能应该合理
        avg_time = total_time / len(test_images)
        return avg_time < 1.0  # 每个验证码处理应该在1秒内
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("验证码处理功能测试")
    print("=" * 60)
    
    async def run_all_tests():
        # 测试配置
        test1 = await test_captcha_config()
        
        # 测试本地OCR
        test2 = await test_local_ocr()
        
        # 测试在线OCR
        test3 = await test_online_ocr()
        
        # 测试第三方服务
        test4 = await test_third_party_services()
        
        # 测试验证码类型
        test5 = await test_captcha_types()
        
        # 测试浏览器集成
        test6 = await test_browser_integration()
        
        # 测试性能
        test7 = await test_performance()
        
        return test1, test2, test3, test4, test5, test6, test7
    
    # 运行所有测试
    results = asyncio.run(run_all_tests())
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 6:  # 至少6个测试通过
        print("✅ 验证码处理功能基本正常")
        print("💡 建议：在实际使用中配置真实的OCR服务API密钥")
        sys.exit(0)
    else:
        print("❌ 验证码处理功能测试失败，需要检查实现")
        sys.exit(1)

if __name__ == "__main__":
    main()
