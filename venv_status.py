#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟环境状态检查脚本
检查虚拟环境配置和依赖安装情况
"""

import sys
import os
import subprocess
from pathlib import Path
import platform

def print_banner():
    """打印横幅"""
    banner = """
    🐍 虚拟环境状态检查
    ================================
    环境配置 + 依赖检查 + 使用指南
    ================================
    """
    print(banner)

def check_virtual_environment():
    """检查虚拟环境状态"""
    print("🔍 虚拟环境检查")
    print("-" * 40)
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if in_venv:
        print("✅ 当前运行在虚拟环境中")
        print(f"   虚拟环境路径: {sys.prefix}")
        
        # 检查虚拟环境名称
        venv_name = Path(sys.prefix).name
        print(f"   环境名称: {venv_name}")
        
        return True
    else:
        print("⚠️  当前运行在全局Python环境中")
        print("💡 建议使用虚拟环境")
        
        # 检查项目虚拟环境是否存在
        venv_path = Path("customer_crawler_env")
        if venv_path.exists():
            print(f"✅ 项目虚拟环境存在: {venv_path.absolute()}")
            print("💡 激活命令:")
            if platform.system().lower() == "windows":
                print("   customer_crawler_env\\Scripts\\activate.bat")
            else:
                print("   source customer_crawler_env/bin/activate")
        else:
            print("❌ 项目虚拟环境不存在")
            print("💡 创建命令: python -m venv customer_crawler_env")
        
        return False

def check_python_version():
    """检查Python版本"""
    print("\n🐍 Python版本检查")
    print("-" * 40)
    
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    print(f"Python路径: {sys.executable}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_installed_packages():
    """检查已安装的包"""
    print("\n📦 已安装包检查")
    print("-" * 40)
    
    # 核心包列表 (包名, 导入名, 描述)
    core_packages = [
        ("requests", "requests", "HTTP请求库"),
        ("beautifulsoup4", "bs4", "HTML解析库"),
        ("pyyaml", "yaml", "YAML配置文件解析"),
    ]
    
    # 可选包列表 (包名, 导入名, 描述)
    optional_packages = [
        ("streamlit", "streamlit", "Web界面框架"),
        ("plotly", "plotly", "交互式图表"),
        ("pandas", "pandas", "数据处理"),
        ("selenium", "selenium", "浏览器自动化"),
        ("fake-useragent", "fake_useragent", "用户代理"),
        ("fuzzywuzzy", "fuzzywuzzy", "模糊匹配"),
    ]
    
    print("核心包:")
    core_missing = []
    for package, import_name, description in core_packages:
        try:
            __import__(import_name)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            print(f"  ❌ {package} - {description}")
            core_missing.append(package)

    print("\n可选包:")
    optional_missing = []
    for package, import_name, description in optional_packages:
        try:
            __import__(import_name)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            print(f"  ⚪ {package} - {description} (未安装)")
            optional_missing.append(package)
    
    # 安装建议
    if core_missing:
        print(f"\n⚠️  缺少核心包: {', '.join(core_missing)}")
        print("💡 安装命令:")
        print(f"   pip install {' '.join(core_missing)}")
    
    if optional_missing:
        print(f"\n💡 可选包安装命令:")
        print(f"   pip install {' '.join(optional_missing)}")
    
    return len(core_missing) == 0

def check_project_files():
    """检查项目文件"""
    print("\n📁 项目文件检查")
    print("-" * 40)
    
    essential_files = [
        ("simple_demo.py", "简化演示程序"),
        ("demo.py", "完整演示程序"),
        ("main.py", "主程序"),
        ("config.yaml", "配置文件"),
        ("requirements.txt", "依赖列表"),
    ]
    
    missing_files = []
    for file_path, description in essential_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path} - {description}")
        else:
            print(f"  ❌ {file_path} - {description}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ 项目文件完整")
        return True

def check_data_directory():
    """检查数据目录"""
    print("\n💾 数据目录检查")
    print("-" * 40)
    
    data_dir = Path("data")
    logs_dir = Path("logs")
    
    if data_dir.exists():
        print(f"✅ 数据目录存在: {data_dir.absolute()}")
        
        # 检查数据库文件
        db_files = list(data_dir.glob("*.db"))
        if db_files:
            print(f"📊 数据库文件: {len(db_files)} 个")
            for db_file in db_files:
                size = db_file.stat().st_size
                print(f"   - {db_file.name} ({size} bytes)")
        else:
            print("ℹ️  暂无数据库文件")
    else:
        print("❌ 数据目录不存在")
        try:
            data_dir.mkdir(exist_ok=True)
            print("   🔧 已自动创建")
        except Exception as e:
            print(f"   ❌ 创建失败: {e}")
    
    if logs_dir.exists():
        print(f"✅ 日志目录存在: {logs_dir.absolute()}")
    else:
        print("❌ 日志目录不存在")
        try:
            logs_dir.mkdir(exist_ok=True)
            print("   🔧 已自动创建")
        except Exception as e:
            print(f"   ❌ 创建失败: {e}")

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 使用指南")
    print("=" * 50)
    
    print("\n🚀 快速启动:")
    print("  1. 双击 start_venv.bat (Windows)")
    print("  2. 或运行: python simple_demo.py")
    
    print("\n🐍 虚拟环境操作:")
    if platform.system().lower() == "windows":
        print("  激活: customer_crawler_env\\Scripts\\activate.bat")
        print("  退出: deactivate")
    else:
        print("  激活: source customer_crawler_env/bin/activate")
        print("  退出: deactivate")
    
    print("\n📦 包管理:")
    print("  安装包: pip install package_name")
    print("  查看包: pip list")
    print("  导出: pip freeze > requirements-lock.txt")
    
    print("\n🔧 常用命令:")
    print("  简化演示: python simple_demo.py")
    print("  完整演示: python demo.py")
    print("  Web界面: python run.py")
    print("  状态检查: python venv_status.py")

def generate_summary(results):
    """生成检查摘要"""
    print("\n📋 检查摘要")
    print("=" * 50)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"通过率: {passed_checks/total_checks*100:.1f}%")
    
    if passed_checks == total_checks:
        print("\n🎉 环境配置完美！")
        print("💡 可以开始使用系统了")
    elif passed_checks >= total_checks * 0.8:
        print("\n✅ 环境基本正常")
        print("💡 可以运行基础功能")
    else:
        print("\n⚠️  环境需要配置")
        print("💡 请按照上述建议进行修复")

def main():
    """主函数"""
    print_banner()
    
    print(f"检查时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"工作目录: {os.getcwd()}")
    
    # 执行检查
    results = {}
    
    results["python_version"] = check_python_version()
    results["virtual_env"] = check_virtual_environment()
    results["packages"] = check_installed_packages()
    results["project_files"] = check_project_files()
    check_data_directory()  # 信息性检查
    
    # 显示使用指南
    show_usage_guide()
    
    # 生成摘要
    generate_summary(results)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 检查被用户中断")
    except Exception as e:
        print(f"\n💥 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
