#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业数据爬虫 - 真实数据抓取
从真实网站抓取企业信息
"""

import sys
import os
import time
import re
import json
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
from urllib.parse import quote, urljoin
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import random

class EnterpriseCrawler:
    """企业数据爬虫"""
    
    def __init__(self):
        """初始化爬虫"""
        self.session = requests.Session()
        self.driver = None
        self.setup_session()
        self.setup_browser()
        
        # 真实数据源配置
        self.sources = {
            'qichacha': {
                'base_url': 'https://www.qichacha.com',
                'search_url': 'https://www.qichacha.com/search',
                'enabled': True
            },
            'tianyancha': {
                'base_url': 'https://www.tianyancha.com', 
                'search_url': 'https://www.tianyancha.com/search',
                'enabled': True
            },
            'gsxt': {
                'base_url': 'http://www.gsxt.gov.cn',
                'search_url': 'http://www.gsxt.gov.cn/index.html',
                'enabled': True
            }
        }
        
        print("🚀 企业数据爬虫初始化完成")
    
    def setup_session(self):
        """设置HTTP会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        }
        self.session.headers.update(headers)
        self.session.timeout = 30
    
    def setup_browser(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 禁用图片加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ 浏览器初始化成功")
            
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            self.driver = None
    
    def crawl_qichacha(self, keyword: str, region: str = None, max_pages: int = 3) -> List[Dict[str, Any]]:
        """从企查查爬取真实企业数据"""
        print(f"🔍 正在从企查查爬取: {keyword}")
        
        if not self.driver:
            print("❌ 需要浏览器支持")
            return []
        
        companies = []
        
        try:
            for page in range(1, max_pages + 1):
                print(f"📄 正在爬取第 {page} 页...")
                
                # 构建搜索URL
                search_url = f"https://www.qichacha.com/search?key={quote(keyword)}&p={page}"
                if region:
                    search_url += f"&province={quote(region)}"
                
                # 访问页面
                self.driver.get(search_url)
                time.sleep(random.uniform(2, 4))
                
                # 等待页面加载
                try:
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".m_srchList, .search-result"))
                    )
                except TimeoutException:
                    print(f"⚠️ 第 {page} 页加载超时")
                    continue
                
                # 解析页面数据
                page_companies = self._parse_qichacha_page()
                companies.extend(page_companies)
                
                print(f"✅ 第 {page} 页获取到 {len(page_companies)} 条数据")
                
                # 随机延迟
                time.sleep(random.uniform(3, 6))
            
            print(f"🎉 企查查爬取完成，共获取 {len(companies)} 条真实企业数据")
            return companies
            
        except Exception as e:
            print(f"❌ 企查查爬取失败: {e}")
            return companies
    
    def _parse_qichacha_page(self) -> List[Dict[str, Any]]:
        """解析企查查页面数据"""
        companies = []
        
        try:
            # 获取页面HTML
            html = self.driver.page_source
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找企业列表
            company_items = soup.find_all('tr', class_='')
            
            for item in company_items:
                try:
                    # 提取企业名称
                    name_link = item.find('a', class_='ma_h1')
                    if not name_link:
                        continue
                    
                    company_name = name_link.get_text(strip=True)
                    detail_url = name_link.get('href', '')
                    
                    # 提取其他信息
                    tds = item.find_all('td')
                    
                    legal_rep = ""
                    capital = ""
                    establish_date = ""
                    status = ""
                    
                    for td in tds:
                        text = td.get_text(strip=True)
                        if '法定代表人' in text:
                            legal_rep = text.replace('法定代表人:', '').strip()
                        elif '注册资本' in text:
                            capital = text.replace('注册资本:', '').strip()
                        elif '成立时间' in text:
                            establish_date = text.replace('成立时间:', '').strip()
                        elif '经营状态' in text:
                            status = text.replace('经营状态:', '').strip()
                    
                    if company_name:
                        company_info = {
                            'company_name': company_name,
                            'legal_representative': legal_rep,
                            'registration_capital': capital,
                            'establishment_date': establish_date,
                            'business_status': status,
                            'detail_url': urljoin('https://www.qichacha.com', detail_url),
                            'source': '企查查',
                            'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S')
                        }
                        companies.append(company_info)
                        
                except Exception as e:
                    print(f"⚠️ 解析企业信息失败: {e}")
                    continue
            
        except Exception as e:
            print(f"❌ 页面解析失败: {e}")
        
        return companies
    
    def crawl_tianyancha(self, keyword: str, region: str = None, max_pages: int = 3) -> List[Dict[str, Any]]:
        """从天眼查爬取真实企业数据"""
        print(f"🔍 正在从天眼查爬取: {keyword}")
        
        if not self.driver:
            print("❌ 需要浏览器支持")
            return []
        
        companies = []
        
        try:
            for page in range(1, max_pages + 1):
                print(f"📄 正在爬取第 {page} 页...")
                
                # 构建搜索URL
                search_url = f"https://www.tianyancha.com/search?key={quote(keyword)}&checkFrom=searchBox&p={page}"
                
                # 访问页面
                self.driver.get(search_url)
                time.sleep(random.uniform(3, 5))
                
                # 等待页面加载
                try:
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".search-result-single, .result-list"))
                    )
                except TimeoutException:
                    print(f"⚠️ 第 {page} 页加载超时")
                    continue
                
                # 解析页面数据
                page_companies = self._parse_tianyancha_page()
                companies.extend(page_companies)
                
                print(f"✅ 第 {page} 页获取到 {len(page_companies)} 条数据")
                
                # 随机延迟
                time.sleep(random.uniform(4, 7))
            
            print(f"🎉 天眼查爬取完成，共获取 {len(companies)} 条真实企业数据")
            return companies
            
        except Exception as e:
            print(f"❌ 天眼查爬取失败: {e}")
            return companies
    
    def _parse_tianyancha_page(self) -> List[Dict[str, Any]]:
        """解析天眼查页面数据"""
        companies = []
        
        try:
            # 获取页面HTML
            html = self.driver.page_source
            soup = BeautifulSoup(html, 'html.parser')
            
            # 查找企业列表
            company_items = soup.find_all('div', class_='search-result-single')
            
            for item in company_items:
                try:
                    # 提取企业名称
                    name_element = item.find('span', class_='name')
                    if not name_element:
                        name_element = item.find('a', class_='name')
                    
                    if not name_element:
                        continue
                    
                    company_name = name_element.get_text(strip=True)
                    
                    # 提取详情链接
                    detail_link = item.find('a')
                    detail_url = detail_link.get('href', '') if detail_link else ''
                    
                    # 提取其他信息
                    info_text = item.get_text()
                    
                    # 使用正则表达式提取信息
                    legal_rep = self._extract_info(info_text, r'法定代表人[：:]\s*([^\s\n]+)')
                    capital = self._extract_info(info_text, r'注册资本[：:]\s*([^\s\n]+)')
                    status = self._extract_info(info_text, r'经营状态[：:]\s*([^\s\n]+)')
                    establish_date = self._extract_info(info_text, r'成立时间[：:]\s*([^\s\n]+)')
                    
                    if company_name:
                        company_info = {
                            'company_name': company_name,
                            'legal_representative': legal_rep,
                            'registration_capital': capital,
                            'establishment_date': establish_date,
                            'business_status': status,
                            'detail_url': urljoin('https://www.tianyancha.com', detail_url),
                            'source': '天眼查',
                            'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S')
                        }
                        companies.append(company_info)
                        
                except Exception as e:
                    print(f"⚠️ 解析企业信息失败: {e}")
                    continue
            
        except Exception as e:
            print(f"❌ 页面解析失败: {e}")
        
        return companies
    
    def _extract_info(self, text: str, pattern: str) -> str:
        """使用正则表达式提取信息"""
        match = re.search(pattern, text)
        return match.group(1).strip() if match else ""
    
    def crawl_enterprises(self, keyword: str, region: str = None, max_results: int = 100) -> List[Dict[str, Any]]:
        """爬取企业数据主函数"""
        print(f"🎯 开始爬取企业数据")
        print(f"关键词: {keyword}")
        print(f"地区: {region or '全国'}")
        print(f"目标数量: {max_results}")
        print("=" * 60)
        
        all_companies = []
        
        # 计算每个源需要爬取的页数
        max_pages_per_source = max(1, max_results // 30)  # 假设每页约10条数据，每个源3页
        
        # 1. 从企查查爬取
        if self.sources['qichacha']['enabled']:
            try:
                qichacha_data = self.crawl_qichacha(keyword, region, max_pages_per_source)
                all_companies.extend(qichacha_data)
            except Exception as e:
                print(f"❌ 企查查爬取异常: {e}")
        
        # 2. 从天眼查爬取
        if self.sources['tianyancha']['enabled'] and len(all_companies) < max_results:
            try:
                tianyancha_data = self.crawl_tianyancha(keyword, region, max_pages_per_source)
                all_companies.extend(tianyancha_data)
            except Exception as e:
                print(f"❌ 天眼查爬取异常: {e}")
        
        # 数据去重
        unique_companies = self._deduplicate_companies(all_companies)
        
        # 限制结果数量
        final_results = unique_companies[:max_results]
        
        print(f"\n📊 爬取结果统计:")
        print(f"  原始数据: {len(all_companies)} 条")
        print(f"  去重后: {len(unique_companies)} 条")
        print(f"  最终结果: {len(final_results)} 条")
        
        return final_results
    
    def _deduplicate_companies(self, companies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """企业数据去重"""
        seen_names = set()
        unique_companies = []
        
        for company in companies:
            name = company.get('company_name', '').strip()
            if name and name not in seen_names:
                seen_names.add(name)
                unique_companies.append(company)
        
        return unique_companies
    
    def save_data(self, data: List[Dict[str, Any]], filename: str = None):
        """保存数据"""
        if not data:
            print("❌ 没有数据需要保存")
            return
        
        if not filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"enterprise_data_{timestamp}"
        
        # 保存为JSON
        json_file = f"{filename}.json"
        try:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ JSON数据已保存: {json_file}")
        except Exception as e:
            print(f"❌ 保存JSON失败: {e}")
        
        # 保存为CSV
        csv_file = f"{filename}.csv"
        try:
            df = pd.DataFrame(data)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"✅ CSV数据已保存: {csv_file}")
        except Exception as e:
            print(f"❌ 保存CSV失败: {e}")
    
    def close(self):
        """关闭资源"""
        if self.driver:
            self.driver.quit()
            print("✅ 浏览器已关闭")

def main():
    """主函数"""
    crawler = EnterpriseCrawler()
    
    try:
        # 测试爬取
        test_keywords = ["建筑工程", "酒店管理", "房地产开发"]
        test_regions = ["北京", "上海", "深圳"]
        
        for i, keyword in enumerate(test_keywords):
            region = test_regions[i] if i < len(test_regions) else None
            
            print(f"\n{'='*80}")
            print(f"🧪 测试爬取: {keyword} - {region or '全国'}")
            print(f"{'='*80}")
            
            # 爬取数据
            companies = crawler.crawl_enterprises(keyword, region, max_results=30)
            
            if companies:
                print(f"\n📋 爬取到的企业信息:")
                for j, company in enumerate(companies[:5], 1):
                    print(f"  {j}. {company['company_name']}")
                    print(f"     来源: {company['source']}")
                    print(f"     法人: {company.get('legal_representative', 'N/A')}")
                    print(f"     资本: {company.get('registration_capital', 'N/A')}")
                    print(f"     状态: {company.get('business_status', 'N/A')}")
                    print()
                
                # 保存数据
                filename = f"real_enterprises_{keyword}_{region or 'nationwide'}"
                crawler.save_data(companies, filename)
            else:
                print("❌ 未爬取到数据")
    
    finally:
        crawler.close()

if __name__ == "__main__":
    main()
