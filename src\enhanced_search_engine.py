#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强搜索引擎模块
使用搜索引擎查找专业网站内容，绕过直接访问限制
"""

import requests
import time
import random
import re
import pandas as pd
from urllib.parse import quote, urljoin
from typing import List, Dict, Optional
import json

try:
    from bs4 import BeautifulSoup
except ImportError:
    BeautifulSoup = None

try:
    from fake_useragent import UserAgent
except ImportError:
    UserAgent = None

from .utils.logger import get_logger
from .region_manager import RegionManager

class EnhancedSearchEngine:
    """增强搜索引擎类"""
    
    def __init__(self):
        """初始化增强搜索引擎"""
        self.logger = get_logger(__name__)
        self.ua = UserAgent() if UserAgent else None
        self.session = requests.Session()
        self.region_manager = RegionManager()
        
        # 设置请求头
        self._setup_session()
        
        # 专业网站域名列表
        self.professional_domains = {
            'bidding': [
                'ccgp.gov.cn',  # 中国政府采购网
                'ggzy.gov.cn',  # 公共资源交易平台
                'cebpubservice.com',  # 招标投标公共服务平台
                'bidcenter.com.cn',  # 中国采招网
                'jszb.com.cn',  # 中国建设招标网
            ],
            'real_estate': [
                'crei.cn',  # 中国房地产信息网
                'fang.com',  # 搜房网
                'cric.com',  # 克而瑞
                'landchina.com',  # 中国土地市场网
            ],
            'hotel': [
                'meadin.com',  # 迈点网
                'chinahotel.org.cn',  # 中国饭店协会
                'hotelengineering.cn',  # 酒店工程网
            ]
        }
    
    def _setup_session(self):
        """设置会话请求头"""
        headers = {
            'User-Agent': self.ua.random if self.ua else 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
    
    def search_professional_content(self, industry: str, province: str = None, city: str = None, 
                                  search_types: List[str] = None) -> pd.DataFrame:
        """搜索专业内容"""
        if not search_types:
            search_types = ['bidding', 'real_estate', 'hotel']
        
        all_results = []
        
        for search_type in search_types:
            self.logger.info(f"开始搜索 {search_type} 相关专业内容")
            
            # 生成针对性搜索查询
            search_queries = self._generate_professional_queries(search_type, industry, province, city)
            
            for query in search_queries:
                try:
                    # 使用必应搜索（相对稳定）
                    results = self._search_bing_professional(query, search_type)
                    all_results.extend(results)
                    
                    time.sleep(random.uniform(2, 4))  # 避免请求过快
                    
                except Exception as e:
                    self.logger.error(f"搜索查询 '{query}' 失败: {e}")
                    continue
        
        return pd.DataFrame(all_results)
    
    def _generate_professional_queries(self, search_type: str, industry: str, 
                                     province: str = None, city: str = None) -> List[str]:
        """生成专业搜索查询"""
        queries = []
        region = self.region_manager.get_region_display_name(province, city)
        
        if search_type == 'bidding':
            # 招标相关查询
            base_queries = [
                f"site:ccgp.gov.cn {region} {industry} 招标",
                f"site:ggzy.gov.cn {region} {industry} 中标",
                f"site:bidcenter.com.cn {region} {industry} 采购",
                f"{region} {industry} 招标公告 工程",
                f"{region} {industry} 中标结果 项目",
                f"{region} 酒店建设 招标 工程",
                f"{region} 房地产 招标 开发",
            ]
        elif search_type == 'real_estate':
            # 房地产相关查询
            base_queries = [
                f"site:fang.com {region} 新楼盘 项目",
                f"site:cric.com {region} 房地产 开发",
                f"{region} 房地产项目 开发商",
                f"{region} 土地出让 房地产",
                f"{region} 商业地产 投资",
                f"{region} 住宅项目 建设",
            ]
        elif search_type == 'hotel':
            # 酒店相关查询
            base_queries = [
                f"site:meadin.com {region} 酒店 开业",
                f"{region} 酒店建设 项目",
                f"{region} 酒店投资 开发",
                f"{region} 度假村 建设",
                f"{region} 星级酒店 项目",
                f"{region} 酒店管理 公司",
            ]
        else:
            base_queries = []
        
        # 过滤和优化查询
        for query in base_queries:
            if query.strip():
                queries.append(query.strip())
        
        return queries[:5]  # 限制查询数量
    
    def _search_bing_professional(self, query: str, search_type: str) -> List[Dict]:
        """使用必应搜索专业内容"""
        results = []
        
        try:
            # 构建搜索URL
            search_url = f"https://www.bing.com/search?q={quote(query)}"
            
            # 发送请求
            response = self.session.get(search_url, timeout=30)
            if response.status_code != 200:
                return results
            
            # 解析搜索结果
            if BeautifulSoup:
                soup = BeautifulSoup(response.text, 'html.parser')
                result_items = soup.find_all('li', class_='b_algo')
                
                for item in result_items:
                    try:
                        # 提取基本信息
                        title_elem = item.find('h2')
                        if not title_elem:
                            continue
                        
                        link_elem = title_elem.find('a')
                        if not link_elem:
                            continue
                        
                        title = title_elem.get_text().strip()
                        link = link_elem.get('href', '')
                        
                        # 提取描述
                        desc_elem = item.find('p')
                        description = desc_elem.get_text().strip() if desc_elem else ""
                        
                        # 检查是否来自专业网站
                        if not self._is_professional_source(link, search_type):
                            continue
                        
                        # 检查内容相关性
                        if not self._is_relevant_content(title, description, search_type):
                            continue
                        
                        # 提取专业信息
                        professional_info = self._extract_professional_info(
                            title, description, link, search_type
                        )
                        
                        if professional_info:
                            professional_info.update({
                                'source': self._get_source_name(link),
                                'search_query': query,
                                'data_type': search_type,
                                'original_url': link
                            })
                            results.append(professional_info)
                    
                    except Exception as e:
                        self.logger.debug(f"解析搜索结果项出错: {e}")
                        continue
            
            self.logger.info(f"查询 '{query}' 获得 {len(results)} 条专业结果")
            
        except Exception as e:
            self.logger.error(f"必应专业搜索出错: {e}")
        
        return results
    
    def _is_professional_source(self, url: str, search_type: str) -> bool:
        """检查是否来自专业网站"""
        if not url:
            return False
        
        # 检查是否来自专业域名
        professional_domains = self.professional_domains.get(search_type, [])
        for domain in professional_domains:
            if domain in url:
                return True
        
        # 检查是否包含专业关键词
        professional_keywords = {
            'bidding': ['招标', '采购', '中标', 'bidding', 'tender'],
            'real_estate': ['房地产', '地产', 'real', 'estate', 'property'],
            'hotel': ['酒店', '宾馆', 'hotel', '度假村', 'resort']
        }
        
        keywords = professional_keywords.get(search_type, [])
        return any(keyword in url.lower() for keyword in keywords)
    
    def _is_relevant_content(self, title: str, description: str, search_type: str) -> bool:
        """检查内容相关性"""
        content = f"{title} {description}".lower()
        
        # 排除无关内容
        exclude_keywords = ['招聘', '求职', '新闻', '百科', '知乎', '贴吧', '论坛', '问答']
        if any(keyword in content for keyword in exclude_keywords):
            return False
        
        # 检查必须包含的关键词
        if search_type == 'bidding':
            required_keywords = ['招标', '中标', '采购', '投标', '工程', '项目']
        elif search_type == 'real_estate':
            required_keywords = ['房地产', '地产', '楼盘', '项目', '开发', '投资']
        elif search_type == 'hotel':
            required_keywords = ['酒店', '宾馆', '度假村', '民宿', '建设', '开业']
        else:
            required_keywords = []
        
        return any(keyword in content for keyword in required_keywords)
    
    def _extract_professional_info(self, title: str, description: str, url: str, 
                                 search_type: str) -> Optional[Dict]:
        """提取专业信息"""
        try:
            # 基础信息
            info = {
                'title': title,
                'description': description[:200] + '...' if len(description) > 200 else description,
                'website': url,
                'industry': self._determine_industry(title, description, search_type)
            }
            
            # 根据类型提取特定信息
            if search_type == 'bidding':
                info.update(self._extract_bidding_info(title, description))
            elif search_type == 'real_estate':
                info.update(self._extract_real_estate_info(title, description))
            elif search_type == 'hotel':
                info.update(self._extract_hotel_info(title, description))
            
            # 提取公司信息
            company_name = self._extract_company_name(title, description)
            if company_name:
                info['company_name'] = company_name
            
            # 提取位置信息
            location = self._extract_location(title, description)
            if location:
                info['location'] = location
            
            return info
            
        except Exception as e:
            self.logger.debug(f"提取专业信息出错: {e}")
            return None
    
    def _extract_bidding_info(self, title: str, description: str) -> Dict:
        """提取招标信息"""
        info = {}
        content = f"{title} {description}"
        
        # 提取项目名称
        project_match = re.search(r'([^，。；：\s]*(?:项目|工程|建设)[^，。；：\s]*)', content)
        if project_match:
            info['project_name'] = project_match.group(1)
        else:
            info['project_name'] = title
        
        # 提取金额
        amount_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:万元|万|元|亿)', content)
        if amount_match:
            info['amount'] = amount_match.group(0)
        
        # 提取日期
        date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', content)
        if date_match:
            info['announcement_date'] = date_match.group(1)
        
        return info
    
    def _extract_real_estate_info(self, title: str, description: str) -> Dict:
        """提取房地产信息"""
        info = {}
        content = f"{title} {description}"
        
        # 提取项目名称
        project_match = re.search(r'([^，。；：\s]*(?:项目|楼盘|小区|广场|中心)[^，。；：\s]*)', content)
        if project_match:
            info['project_name'] = project_match.group(1)
        else:
            info['project_name'] = title
        
        # 提取开发商
        developer_match = re.search(r'(?:开发商|开发|投资)[:：]\s*([^，。；\n]+)', content)
        if developer_match:
            info['developer'] = developer_match.group(1).strip()
        
        return info
    
    def _extract_hotel_info(self, title: str, description: str) -> Dict:
        """提取酒店信息"""
        info = {}
        content = f"{title} {description}"
        
        # 提取酒店名称
        hotel_match = re.search(r'([^，。；：\s]*(?:酒店|宾馆|度假村|民宿)[^，。；：\s]*)', content)
        if hotel_match:
            info['hotel_name'] = hotel_match.group(1)
        else:
            info['hotel_name'] = title
        
        # 提取品牌
        brand_match = re.search(r'(?:品牌|集团)[:：]\s*([^，。；\n]+)', content)
        if brand_match:
            info['hotel_brand'] = brand_match.group(1).strip()
        
        return info
    
    def _extract_company_name(self, title: str, description: str) -> str:
        """提取公司名称"""
        content = f"{title} {description}"
        
        # 公司名称模式
        patterns = [
            r'([^，。；：\s]+(?:公司|企业|集团|有限|股份|科技|建设|工程|地产|酒店))',
            r'(?:投标人|中标单位|开发商|建设方)[:：]\s*([^，。；\s]+)',
            r'([^，。；\s]*(?:建筑|设计|装饰|工程)[^，。；\s]*(?:公司|有限))'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                company = match.group(1).strip()
                if len(company) > 3 and len(company) < 50:  # 合理的公司名称长度
                    return company
        
        return title[:30]  # 如果没找到，返回标题前30个字符
    
    def _extract_location(self, title: str, description: str) -> str:
        """提取位置信息"""
        content = f"{title} {description}"
        
        # 位置模式
        patterns = [
            r'([^，。；\s]*(?:省|市|区|县|街道|路|号)[^，。；\s]*)',
            r'(?:地址|位置|地点)[:：]\s*([^，。；\n]+)',
            r'([^，。；\s]*(?:北京|上海|广州|深圳|杭州|成都|重庆)[^，。；\s]*)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                location = match.group(1).strip()
                if len(location) > 2 and len(location) < 50:
                    return location
        
        return ''
    
    def _determine_industry(self, title: str, description: str, search_type: str) -> str:
        """确定行业"""
        content = f"{title} {description}".lower()
        
        if search_type == 'bidding':
            if any(keyword in content for keyword in ['酒店', '宾馆', '度假村']):
                return '酒店建设'
            elif any(keyword in content for keyword in ['房地产', '地产', '住宅']):
                return '房地产开发'
            else:
                return '建筑工程'
        elif search_type == 'real_estate':
            return '房地产开发'
        elif search_type == 'hotel':
            return '酒店管理'
        else:
            return '其他'
    
    def _get_source_name(self, url: str) -> str:
        """获取数据源名称"""
        domain_names = {
            'ccgp.gov.cn': '中国政府采购网',
            'ggzy.gov.cn': '公共资源交易平台',
            'bidcenter.com.cn': '中国采招网',
            'fang.com': '搜房网',
            'cric.com': '克而瑞',
            'meadin.com': '迈点网',
        }
        
        for domain, name in domain_names.items():
            if domain in url:
                return name
        
        return '专业网站'
