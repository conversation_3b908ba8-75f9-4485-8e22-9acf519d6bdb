const BrowserManager = require('../../config/browserConfig');
const fs = require('fs').promises;
const path = require('path');
const Tesseract = require('tesseract.js');

class QichachaCrawler {
    constructor() {
        this.browserManager = new BrowserManager();
        this.baseUrl = 'https://www.qcc.com';
        this.loginUrl = 'https://www.qcc.com/user_login';
    }

    async login(page) {
        const username = process.env.QICHACHA_USERNAME;
        const password = process.env.QICHACHA_PASSWORD;

        if (!username || !password) {
            throw new Error('请在.env文件中配置企查查账号密码');
        }

        console.log('🔐 开始登录企查查...');

        await page.goto(this.loginUrl, { waitUntil: 'networkidle' });
        await page.waitForTimeout(2000);

        // 输入用户名和密码
        await page.fill('#nameNormal', username);
        await page.fill('#pwdNormal', password);

        // 处理验证码
        await this.handleCaptcha(page);

        // 点击登录
        await page.click('#user_login_normal');
        await page.waitForTimeout(3000);

        // 检查是否登录成功
        const isLoggedIn = await this.checkLoginStatus(page);
        if (!isLoggedIn) {
            throw new Error('企查查登录失败，请检查账号密码或验证码');
        }

        console.log('✅ 企查查登录成功');
    }

    async handleCaptcha(page) {
        try {
            const captchaImg = await page.$('#normalLoginCaptchaImg');
            if (captchaImg) {
                console.log('🔍 检测到验证码，正在识别...');
                
                // 截取验证码图片
                const captchaBuffer = await captchaImg.screenshot();
                
                // 使用OCR识别验证码
                const { data: { text } } = await Tesseract.recognize(captchaBuffer, 'eng', {
                    logger: m => console.log(m)
                });
                
                const captchaCode = text.replace(/[^a-zA-Z0-9]/g, '');
                console.log(`🔤 识别的验证码: ${captchaCode}`);
                
                if (captchaCode.length >= 4) {
                    await page.fill('#normalLoginCaptcha', captchaCode);
                } else {
                    console.warn('⚠️ 验证码识别失败，尝试手动处理');
                    await page.waitForTimeout(10000); // 给用户手动输入时间
                }
            }
        } catch (error) {
            console.warn('⚠️ 验证码处理失败:', error.message);
        }
    }

    async checkLoginStatus(page) {
        try {
            // 检查是否跳转到首页或出现用户信息
            await page.waitForTimeout(2000);
            const currentUrl = page.url();
            
            if (currentUrl.includes('user_login')) {
                return false;
            }
            
            // 检查是否有用户头像或用户名
            const userInfo = await page.$('.header-user-info');
            return !!userInfo;
        } catch (error) {
            return false;
        }
    }

    async searchCompany(page, companyName) {
        console.log(`🔍 搜索企业: ${companyName}`);
        
        const searchUrl = `${this.baseUrl}/web/search?key=${encodeURIComponent(companyName)}`;
        await page.goto(searchUrl, { waitUntil: 'networkidle' });
        await page.waitForTimeout(3000);

        // 获取搜索结果中的第一个公司链接
        const firstCompanyLink = await page.$eval('.search-result .title a', el => el.href);
        
        if (!firstCompanyLink) {
            throw new Error(`未找到企业: ${companyName}`);
        }

        console.log(`✅ 找到企业链接: ${firstCompanyLink}`);
        return firstCompanyLink;
    }

    async extractCompanyInfo(page, companyUrl) {
        console.log('📊 正在提取企业信息...');
        
        await page.goto(companyUrl, { waitUntil: 'networkidle' });
        await page.waitForTimeout(3000);

        const companyInfo = await page.evaluate(() => {
            const getText = (selector) => {
                const element = document.querySelector(selector);
                return element ? element.textContent.trim() : '';
            };

            const getTableValue = (label) => {
                const rows = document.querySelectorAll('.company-base-info tr');
                for (const row of rows) {
                    const labelEl = row.querySelector('td:first-child');
                    const valueEl = row.querySelector('td:last-child');
                    if (labelEl && labelEl.textContent.includes(label)) {
                        return valueEl ? valueEl.textContent.trim() : '';
                    }
                }
                return '';
            };

            return {
                companyName: getText('.company-top .title') || getText('h1'),
                legalPerson: getTableValue('法定代表人') || getText('.legal-person'),
                registeredCapital: getTableValue('注册资本'),
                establishDate: getTableValue('成立日期'),
                businessScope: getTableValue('经营范围') || getText('.business-scope'),
                registrationNumber: getTableValue('统一社会信用代码'),
                companyType: getTableValue('企业类型'),
                businessStatus: getTableValue('经营状态'),
                registeredAddress: getTableValue('注册地址'),
                businessTerm: getTableValue('营业期限'),
                industry: getTableValue('所属行业'),
                phone: getText('.contact-info .phone'),
                email: getText('.contact-info .email'),
                website: getText('.contact-info .website')
            };
        });

        console.log(`✅ 企业信息提取完成: ${companyInfo.companyName}`);
        return companyInfo;
    }

    async saveCompanyData(data, companyName) {
        const dataDir = path.join(__dirname, '../../../data/qichacha');
        await fs.mkdir(dataDir, { recursive: true });
        
        const filename = `${companyName.replace(/[^\w\u4e00-\u9fa5]/g, '_')}_${Date.now()}.json`;
        const filepath = path.join(dataDir, filename);
        
        await fs.writeFile(filepath, JSON.stringify(data, null, 2), 'utf8');
        console.log(`💾 企业数据已保存到: ${filepath}`);
        
        return filepath;
    }

    async crawlCompany(companyName) {
        const browser = await this.browserManager.createBrowser();
        const page = await this.browserManager.createPage(browser);

        try {
            // 登录企查查
            await this.login(page);

            // 搜索企业
            const companyUrl = await this.searchCompany(page, companyName);

            // 提取企业信息
            const companyInfo = await this.extractCompanyInfo(page, companyUrl);

            // 添加爬取时间和来源
            const result = {
                ...companyInfo,
                crawlTime: new Date().toISOString(),
                source: 'qichacha',
                sourceUrl: companyUrl
            };

            // 保存数据
            await this.saveCompanyData(result, companyName);

            return {
                success: true,
                data: result,
                message: `成功爬取企业信息: ${companyInfo.companyName}`
            };

        } catch (error) {
            console.error('❌ 企查查数据爬取失败:', error.message);
            return {
                success: false,
                error: error.message,
                message: '企查查数据爬取失败'
            };
        } finally {
            await this.browserManager.closeBrowser(browser);
        }
    }

    async batchCrawlCompanies(companyNames) {
        const results = [];
        
        for (const companyName of companyNames) {
            console.log(`\n🏢 开始爬取: ${companyName}`);
            
            const result = await this.crawlCompany(companyName);
            results.push({
                companyName,
                ...result
            });

            // 添加延迟避免被封
            await new Promise(resolve => setTimeout(resolve, 5000));
        }

        return results;
    }
}

module.exports = QichachaCrawler;
