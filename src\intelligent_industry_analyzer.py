#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能行业识别分析器
根据输入关键词智能识别行业类别，无需显示位置信息
"""

import re
from typing import Dict, List, Tuple, Optional
import json
from pathlib import Path

# 简化版分词函数，不依赖jieba
def simple_tokenize(text: str) -> List[str]:
    """简化版中文分词"""
    # 使用正则表达式进行基础分词
    # 分离中文、英文、数字
    tokens = re.findall(r'[\u4e00-\u9fa5]+|[a-zA-Z]+|\d+', text)
    return tokens

class IntelligentIndustryAnalyzer:
    """智能行业识别分析器"""
    
    def __init__(self):
        """初始化行业分析器"""
        self.industry_keywords = self._load_industry_keywords()
        self.industry_patterns = self._build_industry_patterns()
        
        # 使用简化版分词
        pass
        
    def _load_industry_keywords(self) -> Dict[str, Dict[str, List[str]]]:
        """加载行业关键词库"""
        return {
            "建筑工程": {
                "primary": ["建筑", "工程", "施工", "建设", "房建", "基建", "装修", "装饰"],
                "secondary": ["混凝土", "钢结构", "幕墙", "防水", "电梯", "暖通", "给排水", "弱电"],
                "materials": ["水泥", "钢材", "砂石", "涂料", "瓷砖", "门窗", "管材"],
                "types": ["住宅", "商业", "办公", "厂房", "学校", "医院", "桥梁", "道路"]
            },
            "酒店餐饮": {
                "primary": ["酒店", "宾馆", "餐厅", "饭店", "民宿", "客栈", "度假村"],
                "secondary": ["餐饮", "住宿", "会议", "宴会", "婚庆", "康养", "温泉"],
                "services": ["客房", "餐饮", "会务", "娱乐", "健身", "SPA", "商务"],
                "types": ["星级酒店", "经济型", "精品酒店", "主题酒店", "商务酒店"]
            },
            "房地产开发": {
                "primary": ["房地产", "地产", "开发", "置业", "物业", "楼盘", "项目"],
                "secondary": ["住宅", "商业", "写字楼", "商铺", "别墅", "公寓", "综合体"],
                "services": ["销售", "租赁", "物业管理", "中介", "评估", "咨询"],
                "types": ["新房", "二手房", "商业地产", "工业地产", "旅游地产"]
            },
            "医疗健康": {
                "primary": ["医院", "诊所", "医疗", "健康", "康复", "养老", "护理"],
                "secondary": ["中医", "西医", "口腔", "眼科", "妇科", "儿科", "骨科"],
                "services": ["体检", "手术", "治疗", "康复", "保健", "美容", "整形"],
                "types": ["综合医院", "专科医院", "社区医院", "私立医院"]
            },
            "教育培训": {
                "primary": ["学校", "教育", "培训", "学院", "大学", "幼儿园", "培训班"],
                "secondary": ["语言", "技能", "职业", "艺术", "体育", "音乐", "美术"],
                "services": ["教学", "培训", "考试", "认证", "留学", "游学"],
                "types": ["学历教育", "职业培训", "兴趣培训", "在线教育"]
            },
            "科技互联网": {
                "primary": ["科技", "互联网", "软件", "网络", "信息", "数据", "AI"],
                "secondary": ["开发", "设计", "运营", "维护", "安全", "云计算", "大数据"],
                "services": ["软件开发", "网站建设", "系统集成", "技术咨询"],
                "types": ["互联网公司", "软件公司", "科技公司", "IT服务"]
            },
            "制造业": {
                "primary": ["制造", "生产", "工厂", "加工", "机械", "设备", "器械"],
                "secondary": ["汽车", "电子", "纺织", "化工", "食品", "医药", "金属"],
                "services": ["生产", "加工", "组装", "检测", "包装", "物流"],
                "types": ["重工业", "轻工业", "高新技术", "传统制造"]
            },
            "金融服务": {
                "primary": ["银行", "金融", "投资", "保险", "证券", "基金", "信贷"],
                "secondary": ["理财", "贷款", "信用", "风控", "资产", "财富", "支付"],
                "services": ["存款", "放贷", "理财", "保险", "投资", "咨询"],
                "types": ["商业银行", "投资银行", "保险公司", "证券公司"]
            },
            "交通运输": {
                "primary": ["运输", "物流", "快递", "货运", "客运", "航空", "铁路"],
                "secondary": ["仓储", "配送", "托运", "搬运", "装卸", "包装"],
                "services": ["运输", "仓储", "配送", "装卸", "包装", "报关"],
                "types": ["公路运输", "铁路运输", "航空运输", "水路运输"]
            },
            "能源环保": {
                "primary": ["能源", "电力", "燃气", "石油", "煤炭", "环保", "新能源"],
                "secondary": ["太阳能", "风能", "水电", "核电", "生物质", "地热"],
                "services": ["发电", "输电", "配电", "节能", "减排", "治理"],
                "types": ["传统能源", "新能源", "环保工程", "节能服务"]
            }
        }
    
    def _build_industry_patterns(self) -> Dict[str, List[str]]:
        """构建行业识别模式"""
        patterns = {}
        
        for industry, keywords in self.industry_keywords.items():
            industry_patterns = []
            
            # 添加所有关键词类别的词汇
            for category, words in keywords.items():
                industry_patterns.extend(words)
            
            patterns[industry] = industry_patterns
        
        return patterns
    
    def analyze_industry(self, input_text: str) -> Dict[str, any]:
        """智能分析行业"""
        if not input_text or not input_text.strip():
            return {
                'success': False,
                'message': '请输入有效的关键词',
                'industry': None,
                'confidence': 0,
                'suggestions': []
            }
        
        # 清理输入文本
        text = input_text.strip().lower()
        
        # 使用简化版分词
        words = simple_tokenize(text)
        
        # 计算每个行业的匹配度
        industry_scores = {}
        
        for industry, patterns in self.industry_patterns.items():
            score = 0
            matched_keywords = []
            
            # 检查直接匹配
            for pattern in patterns:
                if pattern in text:
                    # 根据关键词重要性给分
                    if pattern in self.industry_keywords[industry].get('primary', []):
                        score += 10  # 主要关键词高分
                        matched_keywords.append(f"主要:{pattern}")
                    elif pattern in self.industry_keywords[industry].get('secondary', []):
                        score += 5   # 次要关键词中分
                        matched_keywords.append(f"次要:{pattern}")
                    else:
                        score += 2   # 其他关键词低分
                        matched_keywords.append(f"相关:{pattern}")
            
            # 检查分词匹配
            for word in words:
                for pattern in patterns:
                    if word == pattern or pattern in word or word in pattern:
                        score += 1
                        if f"分词:{word}" not in matched_keywords:
                            matched_keywords.append(f"分词:{word}")
            
            if score > 0:
                industry_scores[industry] = {
                    'score': score,
                    'matched_keywords': matched_keywords
                }
        
        # 找出最佳匹配
        if not industry_scores:
            return {
                'success': False,
                'message': '未能识别出明确的行业类别',
                'industry': None,
                'confidence': 0,
                'suggestions': self._get_industry_suggestions()
            }
        
        # 按分数排序
        sorted_industries = sorted(
            industry_scores.items(), 
            key=lambda x: x[1]['score'], 
            reverse=True
        )
        
        best_industry = sorted_industries[0]
        industry_name = best_industry[0]
        industry_data = best_industry[1]
        
        # 计算置信度
        max_score = industry_data['score']
        confidence = min(100, (max_score / 20) * 100)  # 最高分20分对应100%置信度
        
        # 生成相关建议
        suggestions = self._generate_industry_suggestions(industry_name, sorted_industries)
        
        return {
            'success': True,
            'message': f'识别成功，匹配度: {confidence:.1f}%',
            'industry': industry_name,
            'confidence': confidence,
            'matched_keywords': industry_data['matched_keywords'],
            'suggestions': suggestions,
            'alternative_industries': [item[0] for item in sorted_industries[1:3]]  # 备选行业
        }
    
    def _generate_industry_suggestions(self, industry: str, sorted_industries: List) -> List[str]:
        """生成行业相关建议"""
        suggestions = []
        
        # 基于识别的行业给出建议
        industry_advice = {
            "建筑工程": [
                "建议搜索：建筑公司、工程承包商、装修公司",
                "关注：施工资质、项目经验、安全记录",
                "数据源：住建部门、招投标平台、企业信用网"
            ],
            "酒店餐饮": [
                "建议搜索：酒店集团、餐饮连锁、民宿品牌",
                "关注：服务质量、位置分布、客户评价",
                "数据源：旅游平台、点评网站、工商注册"
            ],
            "房地产开发": [
                "建议搜索：开发商、物业公司、房产中介",
                "关注：项目规模、资金实力、开发经验",
                "数据源：房管部门、土地交易、企业年报"
            ],
            "医疗健康": [
                "建议搜索：医院、诊所、医疗器械公司",
                "关注：医疗资质、专业水平、服务范围",
                "数据源：卫健委、医疗机构、药监局"
            ],
            "教育培训": [
                "建议搜索：学校、培训机构、教育公司",
                "关注：办学资质、师资力量、教学质量",
                "数据源：教育部门、学校官网、培训平台"
            ]
        }
        
        if industry in industry_advice:
            suggestions.extend(industry_advice[industry])
        
        # 添加通用建议
        suggestions.extend([
            "可以结合地区筛选获得更精准结果",
            "建议使用多个关键词组合搜索"
        ])
        
        return suggestions
    
    def _get_industry_suggestions(self) -> List[str]:
        """获取行业建议"""
        return [
            "请尝试更具体的关键词，如：酒店建设、医院装修、学校工程",
            "可以使用行业名称，如：建筑、餐饮、房地产、医疗、教育",
            "支持的行业：建筑工程、酒店餐饮、房地产开发、医疗健康、教育培训等"
        ]
    
    def get_industry_keywords(self, industry: str) -> List[str]:
        """获取指定行业的关键词"""
        if industry not in self.industry_keywords:
            return []
        
        keywords = []
        for category, words in self.industry_keywords[industry].items():
            keywords.extend(words)
        
        return list(set(keywords))  # 去重
    
    def get_supported_industries(self) -> List[str]:
        """获取支持的行业列表"""
        return list(self.industry_keywords.keys())

# 测试函数
def test_industry_analyzer():
    """测试行业分析器"""
    analyzer = IntelligentIndustryAnalyzer()
    
    test_cases = [
        "酒店建设",
        "医院装修工程", 
        "学校建筑项目",
        "餐厅装修",
        "房地产开发",
        "软件开发",
        "物流运输",
        "新能源项目"
    ]
    
    print("🧪 智能行业识别测试")
    print("=" * 50)
    
    for test_input in test_cases:
        print(f"\n输入: {test_input}")
        result = analyzer.analyze_industry(test_input)
        
        if result['success']:
            print(f"✅ 识别行业: {result['industry']}")
            print(f"   置信度: {result['confidence']:.1f}%")
            print(f"   匹配关键词: {', '.join(result['matched_keywords'][:3])}")
            if result['alternative_industries']:
                print(f"   备选行业: {', '.join(result['alternative_industries'])}")
        else:
            print(f"❌ {result['message']}")

if __name__ == "__main__":
    test_industry_analyzer()
