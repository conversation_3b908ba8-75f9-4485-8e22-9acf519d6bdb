#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟环境配置脚本 - 专业级环境管理
自动创建和配置Python虚拟环境
"""

import subprocess
import sys
import os
from pathlib import Path
import platform
import venv

def print_banner():
    """打印横幅"""
    banner = """
    🐍 Python虚拟环境配置工具
    ================================
    专业级环境隔离 + 依赖管理
    ================================
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8+")
        print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def create_virtual_environment(env_name="venv"):
    """创建虚拟环境"""
    print(f"\n🏗️ 创建虚拟环境: {env_name}")
    
    env_path = Path(env_name)
    
    # 如果虚拟环境已存在，询问是否重新创建
    if env_path.exists():
        response = input(f"   虚拟环境 '{env_name}' 已存在，是否重新创建？(y/N): ")
        if response.lower() in ['y', 'yes']:
            print(f"   删除现有环境...")
            import shutil
            shutil.rmtree(env_path)
        else:
            print(f"   使用现有环境: {env_name}")
            return True
    
    try:
        # 创建虚拟环境
        print(f"   正在创建虚拟环境...")
        venv.create(env_path, with_pip=True)
        print(f"✅ 虚拟环境创建成功: {env_path.absolute()}")
        return True
        
    except Exception as e:
        print(f"❌ 虚拟环境创建失败: {e}")
        return False

def get_activation_command(env_name="venv"):
    """获取激活命令"""
    system = platform.system().lower()
    
    if system == "windows":
        activate_script = f"{env_name}\\Scripts\\activate.bat"
        pip_path = f"{env_name}\\Scripts\\pip.exe"
        python_path = f"{env_name}\\Scripts\\python.exe"
    else:
        activate_script = f"{env_name}/bin/activate"
        pip_path = f"{env_name}/bin/pip"
        python_path = f"{env_name}/bin/python"
    
    return activate_script, pip_path, python_path

def install_requirements_in_venv(env_name="venv"):
    """在虚拟环境中安装依赖"""
    print(f"\n📦 在虚拟环境中安装依赖包...")
    
    _, pip_path, python_path = get_activation_command(env_name)
    
    # 检查requirements.txt是否存在
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    try:
        # 升级pip
        print("   升级pip...")
        subprocess.run([python_path, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        print("   安装项目依赖...")
        result = subprocess.run([
            pip_path, "install", "-r", "requirements.txt"
        ], check=True, capture_output=True, text=True)
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败，尝试使用国内镜像源...")
        
        try:
            subprocess.run([
                pip_path, "install", "-r", "requirements.txt",
                "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"
            ], check=True)
            print("✅ 使用镜像源安装成功")
            return True
        except subprocess.CalledProcessError as e2:
            print(f"❌ 镜像源安装也失败: {e2}")
            return False

def test_installation_in_venv(env_name="venv"):
    """在虚拟环境中测试安装"""
    print(f"\n🧪 测试虚拟环境中的安装...")
    
    _, _, python_path = get_activation_command(env_name)
    
    test_imports = [
        "streamlit",
        "plotly", 
        "pandas",
        "requests",
        "bs4",
        "selenium",
        "fake_useragent",
        "fuzzywuzzy"
    ]
    
    failed_imports = []
    
    for module in test_imports:
        try:
            result = subprocess.run([
                python_path, "-c", f"import {module}"
            ], check=True, capture_output=True)
            print(f"   ✅ {module}")
        except subprocess.CalledProcessError:
            print(f"   ❌ {module}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ 以下模块导入失败: {', '.join(failed_imports)}")
        return False
    
    print("\n✅ 所有模块测试通过")
    return True

def create_activation_scripts(env_name="venv"):
    """创建激活脚本"""
    print(f"\n📝 创建激活脚本...")
    
    activate_script, _, python_path = get_activation_command(env_name)
    system = platform.system().lower()
    
    # Windows批处理脚本
    if system == "windows":
        bat_content = f"""@echo off
chcp 65001 >nul
title 客户信息收集系统 - 虚拟环境

echo.
echo 🐍 激活Python虚拟环境
echo ================================
echo.

call {activate_script}

if errorlevel 1 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)

echo ✅ 虚拟环境已激活: {env_name}
echo.
echo 💡 可用命令:
echo    python run.py     - 启动Web界面
echo    python demo.py    - 运行演示
echo    deactivate        - 退出虚拟环境
echo.

cmd /k
"""
        
        with open("activate_env.bat", "w", encoding="utf-8") as f:
            f.write(bat_content)
        print("   ✅ activate_env.bat")
        
        # 更新start.bat使用虚拟环境
        start_bat_content = f"""@echo off
chcp 65001 >nul
title 智能客户信息收集系统

echo.
echo 🕷️ 智能客户信息收集系统
echo ================================
echo.

REM 激活虚拟环境
call {activate_script}
if errorlevel 1 (
    echo ❌ 虚拟环境激活失败，请先运行 setup_env.py
    pause
    exit /b 1
)

echo ✅ 虚拟环境已激活

echo.
echo 🚀 启动Web界面...
echo 📱 浏览器将自动打开 http://localhost:8501
echo ⏹️  按 Ctrl+C 停止服务
echo.

REM 启动应用
python run.py

pause
"""
        
        with open("start.bat", "w", encoding="utf-8") as f:
            f.write(start_bat_content)
        print("   ✅ start.bat (已更新)")
    
    # Unix shell脚本
    else:
        sh_content = f"""#!/bin/bash

echo "🐍 激活Python虚拟环境"
echo "================================"
echo

# 激活虚拟环境
source {activate_script}

if [ $? -ne 0 ]; then
    echo "❌ 虚拟环境激活失败"
    exit 1
fi

echo "✅ 虚拟环境已激活: {env_name}"
echo
echo "💡 可用命令:"
echo "   python run.py     - 启动Web界面"
echo "   python demo.py    - 运行演示"
echo "   deactivate        - 退出虚拟环境"
echo

# 启动新的shell会话
exec bash
"""
        
        with open("activate_env.sh", "w") as f:
            f.write(sh_content)
        
        # 设置执行权限
        os.chmod("activate_env.sh", 0o755)
        print("   ✅ activate_env.sh")

def create_requirements_lock():
    """创建requirements锁定文件"""
    print(f"\n🔒 生成依赖锁定文件...")
    
    _, pip_path, _ = get_activation_command()
    
    try:
        # 生成当前安装的包列表
        result = subprocess.run([
            pip_path, "freeze"
        ], check=True, capture_output=True, text=True)
        
        with open("requirements-lock.txt", "w") as f:
            f.write(result.stdout)
        
        print("   ✅ requirements-lock.txt (精确版本锁定)")
        
    except subprocess.CalledProcessError as e:
        print(f"   ⚠️ 无法生成锁定文件: {e}")

def show_usage_instructions(env_name="venv"):
    """显示使用说明"""
    system = platform.system().lower()
    activate_script, _, _ = get_activation_command(env_name)
    
    instructions = f"""
🎉 虚拟环境配置完成！

📁 环境信息:
   虚拟环境路径: {Path(env_name).absolute()}
   Python解释器: {Path(env_name).absolute() / ('Scripts/python.exe' if system == 'windows' else 'bin/python')}

🚀 使用方法:

1. 激活虚拟环境:
"""
    
    if system == "windows":
        instructions += f"""   方法1: activate_env.bat
   方法2: {activate_script}
   方法3: start.bat (直接启动项目)"""
    else:
        instructions += f"""   方法1: source activate_env.sh
   方法2: source {activate_script}"""
    
    instructions += f"""

2. 运行项目:
   python run.py      # 启动Web界面
   python demo.py     # 运行演示

3. 退出虚拟环境:
   deactivate

💡 开发提示:
   • 始终在虚拟环境中运行项目
   • 安装新包后运行: pip freeze > requirements-lock.txt
   • 团队协作使用: pip install -r requirements-lock.txt

📦 包管理:
   pip install <package>     # 安装新包
   pip uninstall <package>   # 卸载包
   pip list                  # 查看已安装包
   pip freeze               # 导出包列表

⚠️  注意事项:
   • 每次开发前记得激活虚拟环境
   • 不要将{env_name}/目录提交到版本控制
   • 定期更新requirements.txt文件
    """
    print(instructions)

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 显示系统信息
    print(f"\n💻 系统信息:")
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   Python路径: {sys.executable}")
    print(f"   工作目录: {os.getcwd()}")
    
    env_name = "venv"
    
    # 创建虚拟环境
    if not create_virtual_environment(env_name):
        return False
    
    # 在虚拟环境中安装依赖
    if not install_requirements_in_venv(env_name):
        return False
    
    # 测试安装
    if not test_installation_in_venv(env_name):
        return False
    
    # 创建激活脚本
    create_activation_scripts(env_name)
    
    # 生成锁定文件
    create_requirements_lock()
    
    # 创建必要目录
    Path("data").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    Path("data/backups").mkdir(exist_ok=True)
    
    # 显示使用说明
    show_usage_instructions(env_name)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎊 虚拟环境配置成功完成！")
        else:
            print("\n💥 配置过程中出现问题")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 配置被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 配置过程中出现未知错误: {e}")
        sys.exit(1)
