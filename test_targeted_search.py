#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
针对性搜索测试脚本
专门测试酒店建设、房地产项目等高价值数据源
"""

import sys
import os
from pathlib import Path
import traceback
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_hotel_construction_search():
    """测试酒店建设项目搜索"""
    print("🏨 测试酒店建设项目搜索...")
    
    try:
        from src.crawler_engine import CrawlerEngine
        from src.data_processor import DataProcessor
        from src.database_manager import DatabaseManager
        
        # 初始化组件
        crawler = CrawlerEngine()
        processor = DataProcessor()
        db_manager = DatabaseManager()
        
        # 配置爬虫参数 - 专注酒店建设
        crawler_config = {
            'industry': '酒店建设',
            'province': '广东省',
            'city': '深圳市',
            'search_depth': 2,  # 增加搜索深度
            'use_baidu': False,  # 百度被反爬
            'use_bing': False,   # 暂时禁用必应
            'use_business_dirs': False,
            'use_professional_sources': True  # 专注专业数据源
        }
        
        print(f"开始搜索: {crawler_config['industry']} - {crawler_config['province']} {crawler_config['city']}")
        
        # 开始爬取
        raw_data = crawler.start_crawling(crawler_config)
        
        if raw_data.empty:
            print("⚠️ 未获取到酒店建设相关数据")
            return False
        
        print(f"✅ 获取到 {len(raw_data)} 条原始数据")
        
        # 过滤酒店相关数据
        hotel_data = raw_data[
            raw_data['project_name'].str.contains('酒店|宾馆|度假村|民宿', na=False) |
            raw_data['description'].str.contains('酒店|宾馆|度假村|民宿', na=False)
        ]
        
        print(f"✅ 过滤后酒店相关数据: {len(hotel_data)} 条")
        
        if not hotel_data.empty:
            print("\n酒店项目样本:")
            for i, row in hotel_data.head(3).iterrows():
                print(f"{i+1}. {row['project_name'][:60]}...")
                print(f"   公司: {row['company_name']}")
                print(f"   来源: {row['source']}")
                print()
        
        # 数据处理
        processed_data = processor.process_data(hotel_data)
        print(f"✅ 数据处理完成，处理后 {len(processed_data)} 条数据")
        
        # 保存到数据库
        if not processed_data.empty:
            db_manager.save_data(processed_data, '酒店建设')
            print("✅ 酒店建设数据保存成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 酒店建设搜索失败: {e}")
        traceback.print_exc()
        return False

def test_real_estate_search():
    """测试房地产项目搜索"""
    print("\n🏢 测试房地产项目搜索...")
    
    try:
        from src.crawler_engine import CrawlerEngine
        
        crawler = CrawlerEngine()
        
        # 配置爬虫参数 - 专注房地产
        crawler_config = {
            'industry': '房地产开发',
            'province': '广东省',
            'city': '深圳市',
            'search_depth': 2,
            'use_baidu': False,
            'use_bing': False,
            'use_business_dirs': False,
            'use_professional_sources': True
        }
        
        print(f"开始搜索: {crawler_config['industry']} - {crawler_config['province']} {crawler_config['city']}")
        
        # 开始爬取
        raw_data = crawler.start_crawling(crawler_config)
        
        if raw_data.empty:
            print("⚠️ 未获取到房地产相关数据")
            return False
        
        print(f"✅ 获取到 {len(raw_data)} 条原始数据")
        
        # 过滤房地产相关数据
        real_estate_data = raw_data[
            raw_data['project_name'].str.contains('房地产|地产|楼盘|住宅|商业|写字楼', na=False) |
            raw_data['description'].str.contains('房地产|地产|楼盘|住宅|商业|写字楼', na=False)
        ]
        
        print(f"✅ 过滤后房地产相关数据: {len(real_estate_data)} 条")
        
        if not real_estate_data.empty:
            print("\n房地产项目样本:")
            for i, row in real_estate_data.head(3).iterrows():
                print(f"{i+1}. {row['project_name'][:60]}...")
                print(f"   公司: {row['company_name']}")
                print(f"   来源: {row['source']}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ 房地产搜索失败: {e}")
        traceback.print_exc()
        return False

def test_bidding_search():
    """测试招标信息搜索"""
    print("\n📋 测试招标信息搜索...")
    
    try:
        from src.crawler_engine import CrawlerEngine
        
        crawler = CrawlerEngine()
        
        # 配置爬虫参数 - 专注建筑工程招标
        crawler_config = {
            'industry': '建筑工程',
            'province': '广东省',
            'city': '深圳市',
            'search_depth': 3,  # 增加搜索深度获取更多招标信息
            'use_baidu': False,
            'use_bing': False,
            'use_business_dirs': False,
            'use_professional_sources': True
        }
        
        print(f"开始搜索: {crawler_config['industry']} - {crawler_config['province']} {crawler_config['city']}")
        
        # 开始爬取
        raw_data = crawler.start_crawling(crawler_config)
        
        if raw_data.empty:
            print("⚠️ 未获取到招标相关数据")
            return False
        
        print(f"✅ 获取到 {len(raw_data)} 条原始数据")
        
        # 分析招标数据
        bidding_data = raw_data[raw_data['data_type'] == 'bidding']
        print(f"✅ 招标类型数据: {len(bidding_data)} 条")
        
        # 按金额排序（如果有金额信息）
        if 'amount' in bidding_data.columns:
            bidding_with_amount = bidding_data[bidding_data['amount'].notna() & (bidding_data['amount'] != '')]
            print(f"✅ 包含金额信息的招标: {len(bidding_with_amount)} 条")
            
            if not bidding_with_amount.empty:
                print("\n高价值招标项目样本:")
                for i, row in bidding_with_amount.head(3).iterrows():
                    print(f"{i+1}. {row['project_name'][:60]}...")
                    print(f"   投标企业: {row['company_name']}")
                    print(f"   金额: {row['amount']}")
                    print(f"   来源: {row['source']}")
                    print()
        
        # 按来源统计
        if not bidding_data.empty:
            source_counts = bidding_data['source'].value_counts()
            print("\n数据来源统计:")
            for source, count in source_counts.items():
                print(f"  {source}: {count} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 招标搜索失败: {e}")
        traceback.print_exc()
        return False

def test_data_quality():
    """测试数据质量"""
    print("\n📊 测试数据质量...")
    
    try:
        from src.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 获取最近的数据
        recent_data = db_manager.get_data(limit=50)
        
        if recent_data.empty:
            print("⚠️ 数据库中暂无数据")
            return False
        
        print(f"✅ 数据库中共有 {len(recent_data)} 条数据")
        
        # 分析数据完整性
        required_fields = ['公司名称', '行业', '数据源']
        completeness_stats = {}
        
        for field in required_fields:
            if field in recent_data.columns:
                non_empty = recent_data[field].notna().sum()
                completeness = (non_empty / len(recent_data)) * 100
                completeness_stats[field] = completeness
                print(f"✅ {field} 完整性: {completeness:.1f}% ({non_empty}/{len(recent_data)})")
        
        # 分析数据来源分布
        if '数据源' in recent_data.columns:
            source_dist = recent_data['数据源'].value_counts()
            print("\n数据源分布:")
            for source, count in source_dist.items():
                percentage = (count / len(recent_data)) * 100
                print(f"  {source}: {count} 条 ({percentage:.1f}%)")
        
        # 分析行业分布
        if '行业' in recent_data.columns:
            industry_dist = recent_data['行业'].value_counts()
            print("\n行业分布:")
            for industry, count in industry_dist.head(5).items():
                percentage = (count / len(recent_data)) * 100
                print(f"  {industry}: {count} 条 ({percentage:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据质量测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 开始针对性搜索测试")
    print("专注于酒店建设、房地产项目、招标信息等高价值数据源")
    print("=" * 70)
    
    test_results = []
    
    # 运行针对性测试
    test_results.append(("酒店建设搜索", test_hotel_construction_search()))
    test_results.append(("房地产搜索", test_real_estate_search()))
    test_results.append(("招标信息搜索", test_bidding_search()))
    test_results.append(("数据质量分析", test_data_quality()))
    
    # 汇总测试结果
    print("\n" + "=" * 70)
    print("📋 针对性搜索测试结果汇总")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 70)
    print(f"总测试项: {total}")
    print(f"成功测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed >= total * 0.75:  # 75%以上成功率认为良好
        print("\n🎉 针对性搜索测试表现良好！")
        print("💡 优化建议:")
        print("- 专业数据源策略有效")
        print("- 可以进一步优化关键词和过滤规则")
        print("- 建议增加更多专业网站的支持")
        return True
    else:
        print(f"\n⚠️ 针对性搜索需要改进")
        print("💡 改进建议:")
        print("- 检查专业网站的访问状态")
        print("- 优化搜索关键词策略")
        print("- 改进数据过滤和提取逻辑")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
