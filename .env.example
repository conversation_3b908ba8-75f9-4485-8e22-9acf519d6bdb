# 客户信息收集系统 - 环境变量配置模板
# 复制此文件为 .env 并根据需要修改配置

# 数据库配置
DATABASE_PATH=data/customer_data.db
BACKUP_PATH=data/backups/

# 爬虫配置
CRAWLER_DELAY=1
CRAWLER_TIMEOUT=30
CRAWLER_RETRIES=3
CONCURRENT_REQUESTS=16

# 代理配置（可选）
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=https://proxy.example.com:8080

# API密钥（如果使用第三方服务）
# BAIDU_API_KEY=your_baidu_api_key
# GOOGLE_API_KEY=your_google_api_key

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/crawler.log

# Web界面配置
STREAMLIT_PORT=8501
STREAMLIT_HOST=localhost

# 安全配置
# SECRET_KEY=your_secret_key_here

# 开发模式
DEBUG=False
