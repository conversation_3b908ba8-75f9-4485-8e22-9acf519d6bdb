#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地图可视化模块 - 基于地理位置的数据可视化
支持企业分布地图和区域统计图表
"""

import pandas as pd
try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
except ImportError:
    px = None
    go = None
    make_subplots = None
try:
    import streamlit as st
except ImportError:
    st = None
from typing import Dict, List, Any, Optional
import json

from .utils.logger import get_logger
from .region_manager import RegionManager

class MapVisualizer:
    """地图可视化器类"""
    
    def __init__(self):
        """初始化地图可视化器"""
        self.logger = get_logger(__name__)
        self.region_manager = RegionManager()
        
        # 中国省份坐标数据（简化版）
        self.province_coordinates = {
            '北京市': {'lat': 39.9042, 'lon': 116.4074},
            '上海市': {'lat': 31.2304, 'lon': 121.4737},
            '广东省': {'lat': 23.1291, 'lon': 113.2644},
            '江苏省': {'lat': 32.0603, 'lon': 118.7969},
            '浙江省': {'lat': 30.2741, 'lon': 120.1551},
            '山东省': {'lat': 36.6758, 'lon': 117.0009},
            '河南省': {'lat': 34.7466, 'lon': 113.6254},
            '湖北省': {'lat': 30.5928, 'lon': 114.2985},
            '湖南省': {'lat': 28.2282, 'lon': 112.9388},
            '四川省': {'lat': 30.6171, 'lon': 104.0648},
            '河北省': {'lat': 38.0428, 'lon': 114.5149},
            '福建省': {'lat': 26.0745, 'lon': 119.2965},
            '安徽省': {'lat': 31.8612, 'lon': 117.2272},
            '辽宁省': {'lat': 41.8057, 'lon': 123.4315},
            '江西省': {'lat': 28.6765, 'lon': 115.9092},
            '陕西省': {'lat': 34.3416, 'lon': 108.9398},
            '黑龙江省': {'lat': 45.7732, 'lon': 126.6614},
            '吉林省': {'lat': 43.8868, 'lon': 125.3245},
            '山西省': {'lat': 37.8570, 'lon': 112.5492},
            '重庆市': {'lat': 29.5647, 'lon': 106.5507},
            '天津市': {'lat': 39.3434, 'lon': 117.3616},
            '云南省': {'lat': 25.0406, 'lon': 102.7129},
            '贵州省': {'lat': 26.5783, 'lon': 106.7135},
            '广西壮族自治区': {'lat': 22.8150, 'lon': 108.3669},
            '内蒙古自治区': {'lat': 40.8175, 'lon': 111.7656},
            '新疆维吾尔自治区': {'lat': 43.7930, 'lon': 87.6177},
            '西藏自治区': {'lat': 29.6544, 'lon': 91.1409},
            '宁夏回族自治区': {'lat': 38.4872, 'lon': 106.2309},
            '青海省': {'lat': 36.6171, 'lon': 101.7782},
            '甘肃省': {'lat': 36.0611, 'lon': 103.8343},
            '海南省': {'lat': 20.0174, 'lon': 110.3493}
        }
        
        # 主要城市坐标数据（简化版）
        self.city_coordinates = {
            '北京市': {'lat': 39.9042, 'lon': 116.4074},
            '上海市': {'lat': 31.2304, 'lon': 121.4737},
            '广州市': {'lat': 23.1291, 'lon': 113.2644},
            '深圳市': {'lat': 22.5431, 'lon': 114.0579},
            '杭州市': {'lat': 30.2741, 'lon': 120.1551},
            '南京市': {'lat': 32.0603, 'lon': 118.7969},
            '武汉市': {'lat': 30.5928, 'lon': 114.2985},
            '成都市': {'lat': 30.6171, 'lon': 104.0648},
            '西安市': {'lat': 34.3416, 'lon': 108.9398},
            '重庆市': {'lat': 29.5647, 'lon': 106.5507},
            '天津市': {'lat': 39.3434, 'lon': 117.3616},
            '苏州市': {'lat': 31.2989, 'lon': 120.5853},
            '青岛市': {'lat': 36.0671, 'lon': 120.3826},
            '长沙市': {'lat': 28.2282, 'lon': 112.9388},
            '大连市': {'lat': 38.9140, 'lon': 121.6147},
            '厦门市': {'lat': 24.4798, 'lon': 118.0894},
            '宁波市': {'lat': 29.8683, 'lon': 121.5440},
            '济南市': {'lat': 36.6758, 'lon': 117.0009},
            '沈阳市': {'lat': 41.8057, 'lon': 123.4315},
            '郑州市': {'lat': 34.7466, 'lon': 113.6254}
        }
    
    def create_province_distribution_map(self, df: pd.DataFrame):
        """创建省份分布地图"""
        self.logger.info("创建省份分布地图")

        if not go or not px:
            self.logger.warning("Plotly未安装，无法创建地图")
            return None

        # 分析地址字段中的省份信息
        province_stats = {}
        
        if 'address' in df.columns:
            for _, row in df.iterrows():
                address = str(row.get('address', ''))
                
                # 匹配省份
                for province in self.province_coordinates.keys():
                    province_short = province.replace('省', '').replace('市', '').replace('自治区', '').replace('壮族', '').replace('维吾尔', '').replace('回族', '')
                    if province in address or province_short in address:
                        province_stats[province] = province_stats.get(province, 0) + 1
                        break
        
        if not province_stats:
            # 如果没有地址信息，创建空地图
            fig = go.Figure()
            fig.add_annotation(
                text="暂无地理位置数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=20)
            )
            fig.update_layout(
                title="企业省份分布地图",
                showlegend=False,
                height=500
            )
            return fig
        
        # 准备地图数据
        map_data = []
        for province, count in province_stats.items():
            if province in self.province_coordinates:
                coord = self.province_coordinates[province]
                map_data.append({
                    'province': province,
                    'count': count,
                    'lat': coord['lat'],
                    'lon': coord['lon']
                })
        
        map_df = pd.DataFrame(map_data)
        
        # 创建散点地图
        fig = px.scatter_mapbox(
            map_df,
            lat='lat',
            lon='lon',
            size='count',
            hover_name='province',
            hover_data={'count': True, 'lat': False, 'lon': False},
            color='count',
            color_continuous_scale='Viridis',
            size_max=50,
            zoom=3,
            center={'lat': 35, 'lon': 105},
            mapbox_style='open-street-map',
            title='企业省份分布地图'
        )
        
        fig.update_layout(
            height=600,
            margin=dict(t=50, b=0, l=0, r=0)
        )
        
        return fig
    
    def create_city_distribution_map(self, df: pd.DataFrame):
        """创建城市分布地图"""
        self.logger.info("创建城市分布地图")
        
        # 分析地址字段中的城市信息
        city_stats = {}
        
        if 'address' in df.columns:
            for _, row in df.iterrows():
                address = str(row.get('address', ''))
                
                # 匹配城市
                for city in self.city_coordinates.keys():
                    if city in address:
                        city_stats[city] = city_stats.get(city, 0) + 1
                        break
        
        if not city_stats:
            # 如果没有城市数据，返回空地图
            fig = go.Figure()
            fig.add_annotation(
                text="暂无城市位置数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=20)
            )
            fig.update_layout(
                title="企业城市分布地图",
                showlegend=False,
                height=500
            )
            return fig
        
        # 准备地图数据
        map_data = []
        for city, count in city_stats.items():
            if city in self.city_coordinates:
                coord = self.city_coordinates[city]
                map_data.append({
                    'city': city,
                    'count': count,
                    'lat': coord['lat'],
                    'lon': coord['lon']
                })
        
        map_df = pd.DataFrame(map_data)
        
        # 创建散点地图
        fig = px.scatter_mapbox(
            map_df,
            lat='lat',
            lon='lon',
            size='count',
            hover_name='city',
            hover_data={'count': True, 'lat': False, 'lon': False},
            color='count',
            color_continuous_scale='Plasma',
            size_max=40,
            zoom=4,
            center={'lat': 35, 'lon': 105},
            mapbox_style='open-street-map',
            title='企业城市分布地图'
        )
        
        fig.update_layout(
            height=600,
            margin=dict(t=50, b=0, l=0, r=0)
        )
        
        return fig
    
    def create_region_statistics_chart(self, df: pd.DataFrame):
        """创建区域统计图表"""
        self.logger.info("创建区域统计图表")
        
        # 获取区域统计数据
        region_stats = self.region_manager.get_region_statistics(df)
        
        # 创建子图
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('TOP 10 省份', 'TOP 10 城市'),
            specs=[[{"type": "bar"}, {"type": "bar"}]]
        )
        
        # 省份统计
        if region_stats['top_provinces']:
            provinces = [item[0] for item in region_stats['top_provinces'][:10]]
            province_counts = [item[1] for item in region_stats['top_provinces'][:10]]
            
            fig.add_trace(
                go.Bar(
                    x=province_counts,
                    y=provinces,
                    orientation='h',
                    name='省份',
                    marker_color='lightblue'
                ),
                row=1, col=1
            )
        
        # 城市统计
        if region_stats['top_cities']:
            cities = [item[0] for item in region_stats['top_cities'][:10]]
            city_counts = [item[1] for item in region_stats['top_cities'][:10]]
            
            fig.add_trace(
                go.Bar(
                    x=city_counts,
                    y=cities,
                    orientation='h',
                    name='城市',
                    marker_color='lightcoral'
                ),
                row=1, col=2
            )
        
        fig.update_layout(
            title='区域分布统计',
            height=500,
            showlegend=False
        )
        
        fig.update_xaxes(title_text="企业数量", row=1, col=1)
        fig.update_xaxes(title_text="企业数量", row=1, col=2)
        
        return fig
    
    def create_all_maps(self, df: pd.DataFrame) -> Dict[str, Any]:
        """创建所有地图可视化"""
        maps = {}
        
        try:
            maps['province_map'] = self.create_province_distribution_map(df)
            maps['city_map'] = self.create_city_distribution_map(df)
            maps['region_stats'] = self.create_region_statistics_chart(df)
            
            self.logger.info(f"成功生成 {len(maps)} 个地图可视化")
            
        except Exception as e:
            self.logger.error(f"生成地图可视化时出错: {e}")
        
        return maps
